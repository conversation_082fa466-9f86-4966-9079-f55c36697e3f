using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

public class AutomatedApprovalDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("drawLimit")]
    public decimal? DrawLimit { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("dailyAmountLimit")]
    public decimal? DailyAmountLimit { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("weeklyAmountLimit")]
    public decimal? WeeklyAmountLimit { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("creditLimitPercentage")]
    public decimal? CreditLimitPercentage { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("autoApprovedDrawsAmountThisDay")]
    public decimal? AutoApprovedDrawsAmountThisDay { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("autoApprovedDrawsAmountLastSevenDays")]
    public decimal? AutoApprovedDrawsAmountLastSevenDays { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("velocityCheckResult")]
    public string? VelocityCheckResult { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("reason")]
    public string? Reason { get; set; }
}