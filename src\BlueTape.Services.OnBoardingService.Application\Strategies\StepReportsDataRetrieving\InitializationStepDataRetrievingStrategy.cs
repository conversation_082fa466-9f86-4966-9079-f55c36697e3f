﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;

public class InitializationStepDataRetrievingStrategy : StepDataRetrievingStrategyBase
{
    private readonly ICreditApplicationRepository _creditApplicationRepository;

    public InitializationStepDataRetrievingStrategy(IDecisionEngineStepsRepository decisionEngineStepsRepository,
        ICreditApplicationRepository creditApplicationRepository,
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService)
        : base(decisionEngineStepsRepository, decisionEngineStepsBviResultsService, creditApplicationAuthorizationDetailsService)
    {
        _creditApplicationRepository = creditApplicationRepository;
    }

    public override async Task<ReportStepDataModel?> CollectReportStepData(string creditApplicationId, string? companyId, CancellationToken ct)
    {
        var creditApplication = await _creditApplicationRepository.GetById(creditApplicationId, ct);
        var input = new
        {
            creditApplication.DraftId,
        };

        return new ReportStepDataModel
        {
            CompanyId = companyId ?? string.Empty,
            StepName = StepName,
            StepOutput = creditApplication.ToFormattedJsonString(),
            StepInput = input.ToFormattedJsonString(),
            CreditApplicationId = creditApplication.Id
        };
    }

    protected override StepName StepName => StepName.Initialization;

    protected override Task<BviResultModel[]> GetBviResults(string? stepId, string? companyId, AccountAuthorization? accountAuthorizationDetails, CancellationToken ct)
    {
        return Task.FromResult(Array.Empty<BviResultModel>());
    }
}