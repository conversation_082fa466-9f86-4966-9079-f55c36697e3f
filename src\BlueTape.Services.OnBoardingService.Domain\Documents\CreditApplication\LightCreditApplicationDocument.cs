using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;

public sealed class LightCreditApplicationDocument
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;
    
    [BsonElement("type")]
    public string? Type { get; set; }
    
    [BsonElement("companyId")]
    public string? CompanyId { get; set; }
}