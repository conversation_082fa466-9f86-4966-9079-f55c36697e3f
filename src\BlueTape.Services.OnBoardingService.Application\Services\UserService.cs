﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.User;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;
    private readonly IUserRoleRepository _userRoleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<UserService> _logger;

    public UserService(IUserRepository userRepository, IUserRoleRepository userRoleRepository, IMapper mapper, ILogger<UserService> logger)
    {
        _userRepository = userRepository;
        _userRoleRepository = userRoleRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<User?> GetBySubAsync(string sub, CancellationToken ct)
    {
        var user = await _userRepository.GetBySub(sub, ct);
        if (user is null)
        {
            _logger.LogWarning("Could not found user by sub: {Sub}", sub);
            return null;
        }

        return _mapper.Map<User>(user);
    }

    public async Task<User?> GetByCompanyId(string companyId, CancellationToken ct)
    {
        var userRole = await _userRoleRepository.GetByCompanyId(companyId, ct);
        if (userRole is null || string.IsNullOrEmpty(userRole.Sub))
        {
            _logger.LogWarning("Could not found user role with valid sub for company: {CompanyId}", companyId);
            return null;
        }
        var user = await _userRepository.GetBySub(userRole.Sub, ct);
        if (user is null)
        {
            _logger.LogWarning("Could not found user for company: {CompanyId}, user role sub: {UserRoleSub}", companyId, userRole.Sub);
            return null;
        }

        return new User()
        {
            Ip = user.Settings?.Ip,
            Sub = userRole?.Sub,
            Login = user.Login,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName
        };
    }
}
