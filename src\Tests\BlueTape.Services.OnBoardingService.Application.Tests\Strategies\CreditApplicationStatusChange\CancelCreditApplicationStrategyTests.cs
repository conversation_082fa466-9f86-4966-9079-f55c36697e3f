﻿using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Strategies.CreditApplicationStatusChange;

public class CancelCreditApplicationStrategyTests
{
    private readonly CancelCreditApplicationStrategy _strategy;

    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock;
    private readonly Mock<ICreditApplicationSyncService> _creditApplicationSyncServiceMock;
    private readonly Mock<ICreditApplicationNotesService> _creditApplicationNotesServiceMock;

    public CancelCreditApplicationStrategyTests()
    {
        _dateProviderMock = new();
        _creditApplicationNotesServiceMock = new();

        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        var mapper = new Mapper(mapperConfig);
        Mock<IAccountStatusService> accountStatusServiceMock = new();
        _creditApplicationRepositoryMock = new();
        _creditApplicationSyncServiceMock = new();
        Mock<ILogger<CancelCreditApplicationStrategy>> loggerMock = new();

        _strategy = new CancelCreditApplicationStrategy(
            _dateProviderMock.Object,
            mapper,
            accountStatusServiceMock.Object,
            _creditApplicationRepositoryMock.Object,
            _creditApplicationSyncServiceMock.Object,
            _creditApplicationNotesServiceMock.Object,
            loggerMock.Object);
    }

    [Fact]
    public void IsApplicable_CanceledStatus_ReturnsTrue()
    {
        var result = _strategy.IsApplicable(CreditApplicationStatus.Canceled.ToString());

        result.ShouldBe(true);
    }

    [Fact]
    public void IsApplicable_NotCanceledStatus_ReturnsFalse()
    {
        var result = _strategy.IsApplicable(CreditApplicationStatus.Processed.ToString());

        result.ShouldBe(false);
    }

    [Theory, CustomAutoData]
    public async Task ChangeStatus_UpdatesCreditApplication(CreditApplicationDocument creditApplication, ReviewCreditApplicationDto reviewCreditApplication)
    {
        var userId = "userId";
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.Update(creditApplication, default)).ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);

        var result = await _strategy.ChangeStatus(creditApplication, reviewCreditApplication, userId, default);

        _creditApplicationSyncServiceMock.Verify(x => x.SyncApplicableCreditApplication(creditApplication, default), Times.Once);
        result.CanceledAt.ShouldBe(now);
        result.CanceledBy.ShouldBe(userId);
        result.Status.ShouldBe(CreditApplicationStatus.Canceled.ToString().ToLower());
    }
}
