using Amazon;
using Amazon.Runtime;
using Amazon.SimpleNotificationService;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.API.Extensions.DI;
using BlueTape.Services.OnBoardingService.API.Mappers;
using BlueTape.Services.OnBoardingService.API.Middlewares;
using BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.CreateAccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Mappers;
using BlueTape.Services.OnBoardingService.Application.DI;
using BlueTape.Services.OnBoardingService.Application.Mappers.DraftMapper;
using BlueTape.Services.OnBoardingService.Compatibility.DI;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.DI;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using BlueTape.Services.Utilities.Options;
using BlueTape.Services.Utilities.Security;
using BlueTape.Utilities.Security;
using FluentValidation;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

builder.AddAzureDependencies();
builder.Services.AddDefaultAWSOptions(configuration.GetAWSOptions());
builder.Services.AddOptions();
builder.Services.AddSingleton<IAmazonSimpleNotificationService>(sp =>
{
    var awsCredentials = new BasicAWSCredentials(configuration["AWS-ACCESS-KEY-ID"], configuration["AWS-SECRET-ACCESS-KEY"]);
    var region = RegionEndpoint.GetBySystemName(configuration["AWS-DEFAULT-REGION"]);
    return new AmazonSimpleNotificationServiceClient(awsCredentials, region);
});
builder.Services.Configure<BlueTapeOptions>(builder.Configuration.GetSection(nameof(BlueTapeOptions)));

builder.ConfigureLogging(configuration);
builder.Services.AddValidatorsFromAssemblyContaining<CreateAccountAuthorizationDtoValidator>();
builder.ConfigureSwagger();
builder.ConfigureAuthentication();
builder.Services.AddScoped<IDraftMapper, DraftMapper>();
builder.Services.UseAuthorizationDetailsRefreshDetector(configuration);
builder.Services.AddTransient<IKmsEncryptionService, KmsEncryptionService>();
builder.Services.AddTransient<IMd5HashService, Md5Hashservice>();
builder.Services.AddApplicationDependencies(configuration);
builder.Services.UseCompatibilityService(configuration);
builder.Services.AddControllers().AddJsonOptions(op =>
{
    op.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
    op.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});
builder.Services.AddHealthChecks();
builder.Services.AddAutoMapper(typeof(ViewModelsProfile).GetTypeInfo().Assembly);

var app = builder.Build();

app.UseBlueTapeTracing();

if (true || !app.Environment.IsEnvironment(EnvironmentConstants.Production))
{
    app.UseSwagger(opt => opt.PreSerializeFilters.Add((swagger, httpReq) =>
    {
        if (!httpReq.Host.Host.Contains(EndpointConstants.LocalHost) && !app.Environment.IsEnvironment(EnvironmentConstants.Dev))
        {
            swagger.Servers = new List<OpenApiServer>
            {
                new()
                {
                    Url = EndpointConstants.Suffix
                }
            };
        }
    }));
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseMiddleware<LoggingMiddleware>();
app.UseMiddleware<ExceptionMiddleware>();

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHealthChecks("/health");
await app.RunAsync();
