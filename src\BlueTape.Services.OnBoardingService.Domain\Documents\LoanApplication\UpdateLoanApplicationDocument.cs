﻿namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
public class UpdateLoanApplicationDocument
{
    public string? CompanyId { get; set; }
    public InvoiceDetails? InvoiceDetails { get; set; }
    public object? Outputs { get; set; }

    public object? PrevOutputs { get; set; }
    public string? Status { get; set; }

    public double? AmountDue { get; set; }

    public string? CreditApplicationId { get; set; }
    public string? DrawApprovalId { get; set; }

    public DateTime? SubmitDate { get; set; }
    public DateTime? DecisionDate { get; set; }
    public string? ApprovedBy { get; set; }
    public double? ApprovedAmount { get; set; }

    public bool? IsSentBack { get; set; }
    public bool? DisableCancelNotification { get; set; }

    public object? Notes { get; set; }

    public DateTime? UpdatedAt { get; set; }
    public DateTime? UpdatedAtByCompatibility { get; set; }
}
