﻿namespace BlueTape.Services.OnBoardingService.AuthorizationDetailsRefreshDetectorService;

public class AuthorizationDetailsRefreshDetectorService : IAuthorizationDetailsRefreshDetectorService
{
    private readonly IAuthorizationDetailsRefreshConfigurationManager _configurationManager;
    private readonly ICreditApplicationRepository _creditApplicationRepository;
    private readonly ICompanyScheduledUpdateEventsGenerator _eventsGenerator;
    private readonly IDecisionEngineStepsService _decisionEngineStepsService;
    private readonly IRefreshServiceMessageSender _refreshServiceMessageSender;
    private readonly ICompanyService _companyService;
    private readonly AuthorizationDetailsDetectorMessagingOptions _options;
    private readonly ILogger<AuthorizationDetailsRefreshDetectorService> _logger;

    public AuthorizationDetailsRefreshDetectorService(
        IAuthorizationDetailsRefreshConfigurationManager configurationManager,
        ICreditApplicationRepository creditApplicationRepository,
        ICompanyScheduledUpdateEventsGenerator eventsGenerator,
        IDecisionEngineStepsService decisionEngineStepsService,
        IRefreshServiceMessageSender refreshServiceMessageSender,
        ICompanyService companyService,
        IOptions<AuthorizationDetailsDetectorMessagingOptions> options,
        ILogger<AuthorizationDetailsRefreshDetectorService> logger)
    {
        _configurationManager = configurationManager;
        _creditApplicationRepository = creditApplicationRepository;
        _eventsGenerator = eventsGenerator;
        _decisionEngineStepsService = decisionEngineStepsService;
        _refreshServiceMessageSender = refreshServiceMessageSender;
        _companyService = companyService;
        _options = options.Value;
        _logger = logger;
    }

    public async Task Run(CancellationToken ctx, string? companyId = null)
    {
        _logger.LogInformation("Started authorization details refresh detector execution");

        var activeCompanies = companyId is not null ? [(await _companyService.GetCompanyById(companyId, ctx))!] : await _companyService.GetCompaniesByActiveAccounts(ctx) ?? [];

        var approvedCreditApplications = await GetApprovedCreditApplications(activeCompanies, ctx);
        var companiesForScheduledUpdates = GetCompaniesForScheduledUpdates(approvedCreditApplications, activeCompanies);
        var companyIds = companiesForScheduledUpdates.Select(x => x.Id).ToArray();
        var scheduledChecks = _configurationManager.GetRefreshChecks();
        var latestStepsDictionary = await _decisionEngineStepsService.GetLatestByCompanyIds(companyIds, ctx);

        var refreshServiceMessages = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>();
        foreach (var company in companiesForScheduledUpdates)
        {
            using (LogContext.PushProperty("CompanyId", company.Id))
            {
                var doesCompanyContainExecutions = latestStepsDictionary.TryGetValue(company.Id, out var lastCompanyExecutions);
                foreach (var scheduledCheck in scheduledChecks)
                {
                    var companyEvents = _eventsGenerator.GenerateScheduledUpdateEvents(company, scheduledCheck,
                        approvedCreditApplications, doesCompanyContainExecutions ? lastCompanyExecutions.ToList() : new List<DecisionEngineSteps>());
                    refreshServiceMessages.AddRange(companyEvents);
                }
            }
        }
        await SendEventsToRefreshService(refreshServiceMessages, ctx);
        _logger.LogInformation("Finished authorization details refresh detector execution");
    }

    private async Task<List<LightCreditApplicationDocument>> GetApprovedCreditApplications(List<CompanyModel> activeCompanies, CancellationToken ctx)
    {
        var companyIds = activeCompanies.Select(company => company.Id).ToArray();
        var approvedCreditApplications = (await _creditApplicationRepository.GetLightCreditApplications(companyIds,
            CreditApplicationStatus.Approved.ToString(),
            ctx)).ToList();

        return approvedCreditApplications;
    }

    private static IEnumerable<CompanyModel> GetCompaniesForScheduledUpdates(List<LightCreditApplicationDocument> approvedCreditApplications, List<CompanyModel> activeCompanies)
    {
        var activeCompaniesWithApprovedCreditApplicationsIds = approvedCreditApplications
            .Select(x => x.CompanyId)
            .ToArray();
        var activeCompaniesWithApprovedCreditApplications = activeCompanies
            .Where(x => activeCompaniesWithApprovedCreditApplicationsIds.Contains(x.Id));
        return activeCompaniesWithApprovedCreditApplications;
    }

    private Task SendEventsToRefreshService(IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> events, CancellationToken ct)
    {
        if (!events.Any()) return Task.CompletedTask;
        var scheduledPeriodInMinutes = _options.ScheduledPeriodDurationBetweenMessagesInMinutes;
        var maxSimultaneouslySentMessagesCount = _options.MaxSimultaneouslySentMessagesCount;
        var currentDateTimeOffset = DateTimeOffset.UtcNow;
        for (var messagesSentCount = 0; messagesSentCount < events.Count; messagesSentCount++)
        {
            var currentEvent = events[messagesSentCount];
            var offsetInMinutes = (messagesSentCount / maxSimultaneouslySentMessagesCount) * scheduledPeriodInMinutes;
            currentEvent.MessageAttributes!.ScheduledEnqueueTimeUtc = currentDateTimeOffset.AddMinutes(offsetInMinutes);
        }

        return _refreshServiceMessageSender.SendMessages(events, ct);
    }
}