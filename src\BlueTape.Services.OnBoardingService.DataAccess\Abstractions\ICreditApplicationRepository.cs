﻿using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions
{
    public interface ICreditApplicationRepository : IGenericRepository<CreditApplicationDocument>
    {
        Task<IEnumerable<CreditApplicationDocument>> GetAllByFilters(GetCreditApplicationQuery query, CancellationToken ct);

        Task<GetQueryWithPaginationResult<CreditApplicationDocument>> GetAllByFiltersWithPagination(
            GetCreditApplicationQueryWithPagination query, CancellationToken ct);

        Task<IEnumerable<CreditApplicationDocument>> GetByEinHashes(string[] einHashes, CancellationToken ct);
        Task<IEnumerable<CreditApplicationDocument>> GetByIds(string[] ids, CancellationToken ct);
        Task<IReadOnlyList<LightCreditApplicationDocument>> GetLightCreditApplications(string[] companyIds, string status, CancellationToken ct);
        Task<IReadOnlyList<LightCreditApplicationDocument>> GetLightCreditApplications(string[] companyIds, CancellationToken ct);
        Task<IEnumerable<CreditApplicationDocument>> GetByCompanyIds(
            GetCreditApplicationsByCompanyIdsQuery query,
            CancellationToken ct);
    }
}
