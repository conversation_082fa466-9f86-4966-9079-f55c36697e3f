using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Process;
public class PlaidDataService : IPlaidDataService
{
    private readonly ICompanyExternalService _companyExternalService;

    public PlaidDataService(ICompanyExternalService companyExternalService)
    {
        _companyExternalService = companyExternalService;
    }

    public async Task<IEnumerable<CashFlowItemResponse>?> LoanApplicationPlaidDataStep(LoanApplicationDocument application, CancellationToken cancellationToken)
    {
        if (application == null)
            throw new Exception("Loan Application is not found");

        var companyId = application.CompanyId;

        var query = new AssetReportQuery()
        {
            From = DateTime.Today.AddYears(-2),
            To = DateTime.Today,
            Grouping = CashFlowGrouping.Monthly
        };

        var cashFlow = await _companyExternalService.GetPlaidAssetReport(companyId, query, cancellationToken);

        return cashFlow;
    }
}
