﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.Reports;

public class ReportStepDataModel
{
    public StepName StepName { get; set; }
    public string CreditApplicationId { get; set; } = string.Empty;
    public string CompanyId { get; set; } = string.Empty;
    public string StepInput { get; set; } = string.Empty;
    public string StepOutput { get; set; } = string.Empty;
    public BviResultModel[] BviResults { get; set; } = Array.Empty<BviResultModel>();
    public string? AccountAuthorizationDetails { get; set; }
}