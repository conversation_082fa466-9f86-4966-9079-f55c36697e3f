﻿using Azure.Identity;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;

namespace BlueTape.Services.OnBoardingService.API.Extensions.DI;

public static class AzureExtensions
{
    public static void AddAzureDependencies(this WebApplicationBuilder builder)
    {
        var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(ConfigConstants.KeyVaultUri) ?? throw new VariableNullException(nameof(ConfigConstants.KeyVaultUri)));
        var azureCredentials = new DefaultAzureCredential();
        builder.Services.AddMemoryCache();
        builder.Services.AddTransient<IKeyVaultService, KeyVaultService>();
        builder.Configuration.AddAzureKeyVault(keyVaultUri, azureCredentials);

        builder.Services.AddApplicationInsightsTelemetry(cfg =>
        {
            cfg.ConnectionString = builder.Configuration.GetSection(ConfigConstants.AppInsightsConnection).Value;
        });
    }
}
