﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;

public class CreateCreditApplication
{
    public string? CompanyId { get; set; }

    public string? EinHash { get; set; }

    public string? DraftId { get; set; }

    public DateTime? ApplicationDate { get; set; }

    public CreditApplicationType Type { get; set; }

    public string? MerchantId { get; set; }

    public string? BusinessName { get; set; }

    public string? BusinessDba { get; set; }

    public string? BusinessCategory { get; set; }

    public string? ApplicantName { get; set; }

    public string? SupplierName { get; set; }

    public decimal? RequestedAmount { get; set; }

    public string? AutomatedDecisionResult { get; set; }

    public string? CreatedBy { get; set; }

    public string? Status { get; set; }

    public string? PurchaseType { get; set; }

    public decimal? ApprovedCreditLimit { get; set; }

    public double? RevenueFallPercentage { get; set; }

    public bool? IsNotNotifyUser { get; set; }

    public DateTime? RejectedAt { get; set; }

    public string? RejectedBy { get; set; }

    public string? ExecutionId { get; set; }

    public BankAccountType? BankAccountType { get; set; }

    public bool? IsArAdvanceRequested { get; set; }

    public DateTime? LastStatusChangedAt { get; set; }

    public string? LastStatusChangedBy { get; set; }

    public CreateMerchantSettings? MerchantSettings { get; set; }
    public bool? ShouldIgnoreCaching { get; set; }
}