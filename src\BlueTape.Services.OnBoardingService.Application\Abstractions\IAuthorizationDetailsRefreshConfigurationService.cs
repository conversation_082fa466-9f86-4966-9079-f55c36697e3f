﻿using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions
{
    public interface IAuthorizationDetailsRefreshConfigurationService
    {
        Task<IReadOnlyList<RefreshCheckConfiguration>> GetRefreshChecks(CancellationToken ct);

        Task<RefreshCheckConfiguration?> GetRefreshCheckByScheduledUpdateType(string scheduledUpdate, CancellationToken ct);
    }
}
