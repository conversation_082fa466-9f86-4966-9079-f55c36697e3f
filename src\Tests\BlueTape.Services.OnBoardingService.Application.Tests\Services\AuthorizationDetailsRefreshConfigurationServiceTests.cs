﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Configuration;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services
{
    public class AuthorizationDetailsRefreshConfigurationServiceTests
    {
        private readonly Mock<IAuthorizationDetailsRefreshConfigurationRepository> _refreshConfigurationRepositoryMock = new();
        private readonly IMapper _mapper;

        public AuthorizationDetailsRefreshConfigurationServiceTests()
        {
            {
                var mapperConfig = new MapperConfiguration(
                    cfg =>
                    {
                        cfg.AddProfile(new ModelsProfile());
                    });
                _mapper = new Mapper(mapperConfig);
            }
        }

        [Fact]
        public async void GetRefreshChecks_ReturnsValidChecksFromConfig()
        {
            var configuration = new AuthorizationDetailsRefreshConfigurationDocument()
            {
                Checks =
                [
                    new()
                {
                    ScheduledUpdate = "one"
                },
                new()
                {
                    ScheduledUpdate = "two"
                }
                ]
            };


            _refreshConfigurationRepositoryMock.Setup(x => x.GetConfiguration(default)).ReturnsAsync(configuration);

            var configurationManager = new AuthorizationDetailsRefreshConfigurationService(_refreshConfigurationRepositoryMock.Object, _mapper);

            var checks = await configurationManager.GetRefreshChecks(default);

            checks[0].ScheduledUpdate.ShouldBe("one");
            checks[1].ScheduledUpdate.ShouldBe("two");
        }

        [Fact]
        public async void GetRefreshCheckByScheduledUpdateType_ReturnsCorrectCheckFromConfig()
        {
            var configuration = new AuthorizationDetailsRefreshConfigurationDocument()
            {
                Checks =
                [
                    new()
                {
                    ScheduledUpdate = "one"
                },
                new()
                {
                    ScheduledUpdate = "two"
                }
                ]
            };


            _refreshConfigurationRepositoryMock.Setup(x => x.GetConfiguration(default)).ReturnsAsync(configuration);

            var configurationManager = new AuthorizationDetailsRefreshConfigurationService(_refreshConfigurationRepositoryMock.Object, _mapper);

            var check = await configurationManager.GetRefreshCheckByScheduledUpdateType("two", default);

            check!.ScheduledUpdate.ShouldBe("two");
        }

        [Fact]
        public async void GetRefreshCheckByScheduledUpdateType_NoApplicableCheck_ReturnsNull()
        {
            var configuration = new AuthorizationDetailsRefreshConfigurationDocument()
            {
                Checks =
                [
                    new()
                {
                    ScheduledUpdate = "one"
                },
                new()
                {
                    ScheduledUpdate = "two"
                }
                ]
            };


            _refreshConfigurationRepositoryMock.Setup(x => x.GetConfiguration(default)).ReturnsAsync(configuration);

            var configurationManager = new AuthorizationDetailsRefreshConfigurationService(_refreshConfigurationRepositoryMock.Object, _mapper);

            var check = await configurationManager.GetRefreshCheckByScheduledUpdateType("three", default);

            check.ShouldBeNull();
        }

        [Fact]
        public async void GetRefreshCheckByScheduledUpdateType_ApplicableCheckFound_MapsFieldCorrectly()
        {
            var configuration = new AuthorizationDetailsRefreshConfigurationDocument()
            {
                Checks =
                [
                    new()
                {
                    ScheduledUpdate = "one",
                    CreditApplicationTypes = ["ArAdvance", "GETPAID", "loc", "ihc"],
                    FrequencyInDays = 90,
                    StepsIncluded = ["KYB", "KYC"]
                }
                ]
            };

            _refreshConfigurationRepositoryMock.Setup(x => x.GetConfiguration(default)).ReturnsAsync(configuration);

            var configurationManager = new AuthorizationDetailsRefreshConfigurationService(_refreshConfigurationRepositoryMock.Object, _mapper);

            var check = await configurationManager.GetRefreshCheckByScheduledUpdateType("one", default)!;

            check.ScheduledUpdate.ShouldBe("one");
            check.CreditApplicationTypes.ShouldBe(
            [CreditApplicationType.ARAdvance,
            CreditApplicationType.GetPaid,
            CreditApplicationType.LineOfCredit,
            CreditApplicationType.InHouseCredit]);
            check.FrequencyInDays.ShouldBe(90);
            check.StepsIncluded.ShouldBe(["KYB", "KYC"]);
        }
    }
}
