﻿using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Abstractions;

public interface IOutputsManagerService
{
    Task SyncGiact(string loanApplicationId, string creditApplicationId, LoanApplicationDocument loanApplication, CancellationToken cancellationToken);
    Task SyncKnockout(LoanApplicationDocument loanApplication, string creditApplicationId, CancellationToken cancellationToken);
    Task SyncBankAccounts(string loanApplicationId, string creditApplicationId, CancellationToken cancellationToken);
    Task SyncBluetape(LoanApplicationDocument loanApplication, string creditApplicationId, CancellationToken cancellationToken);
    Task SyncExperian(string loanApplicationId, string creditApplicationId, LoanApplicationDocument loanApplication, LoanApplicationDocument? firstLoanApplication, DraftDocument draft, AccountAuthorizationDocument? accountAuthorization, CancellationToken cancellationToken);
    Task SyncKyb(string loanApplicationId, CreditApplication creditApplication, LoanApplicationDocument loanApplication, LoanApplicationDocument? firstLoanApplication, DraftDocument draft, AccountAuthorizationDocument? accountAuthorization, CancellationToken cancellationToken);
    Task SyncKyc(string loanApplicationId, string creditApplicationId, LoanApplicationDocument loanApplication, LoanApplicationDocument? firstLoanApplication, AccountAuthorizationDocument? accountAuthorization, CancellationToken cancellationToken);
    Task SyncValidation(string loanApplicationId, CancellationToken cancellationToken);
    Task SyncProcessManualData(LoanApplicationDocument loanApplication, CancellationToken cancellationToken);
    Task SyncLoanDecision(LoanApplicationDocument loanApplication, CancellationToken cancellationToken);
    Task SyncProcessPlaidData(LoanApplicationDocument loanApplication, CancellationToken cancellationToken);
}
