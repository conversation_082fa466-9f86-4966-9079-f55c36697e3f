﻿using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using Microsoft.ApplicationInsights.Extensibility;
using Serilog;

namespace BlueTape.Services.OnBoardingService.API.Extensions.DI
{
    public static class LoggingExtensions
    {
        public static void ConfigureLogging(this WebApplicationBuilder builder, ConfigurationManager configurationManager)
        {
            builder.Host.UseSerilog((hostingContext, services, loggerConfiguration) =>
            {
                loggerConfiguration
                    .ReadFrom.Configuration(configurationManager)
                    .Enrich.FromGlobalLogContext()
                    .Enrich.WithProperty(LoggerConstants.ProjectName, LoggerConstants.ProjectValue)
                    .Enrich.WithProperty(LoggerConstants.EnvironmentName, hostingContext.HostingEnvironment.EnvironmentName)
                    .Enrich.WithProperty(LoggerConstants.ContentRootPath, hostingContext.HostingEnvironment.ContentRootPath)
                    .WriteTo.ApplicationInsights(
                        services.GetRequiredService<TelemetryConfiguration>(),
                        TelemetryConverter.Traces)
                    .WriteTo.Console();

                if (hostingContext.HostingEnvironment.IsDevelopment())
                    loggerConfiguration.WriteTo.Seq(hostingContext.Configuration["Serilog:Seq:Address"]!);
            });

            builder.Services.AddBlueTapeTracing();
        }
    }
}
