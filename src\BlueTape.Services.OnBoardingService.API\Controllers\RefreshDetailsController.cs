﻿using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Configuration;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;
[Route(ControllersConstants.Deadrs)]
[ApiController]

public class RefreshDetailsController(IAuthorizationDetailsRefreshDetectorService detectorService, IAuthorizationDetailsRefreshConfigurationRepository refreshConfigurationRepository) : ControllerBase
{
    [HttpPatch]
    public Task RunDetectorForCompany([FromQuery] string? companyId, CancellationToken ctx)
    {
        return detectorService.Run(ctx, companyId);
    }

    [HttpGet("config")]
    public Task<AuthorizationDetailsRefreshConfigurationDocument> GetRefreshServiceConfig(CancellationToken ctx)
    {
        return refreshConfigurationRepository.GetConfiguration(ctx);
    }

    [HttpPost]
    public Task GenerateNote(CreditApplicationNoteDetails note, CancellationToken ctx)
    {
        return detectorService.GenerateCreditApplicationNotes(note, ctx);
    }
}
