using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApprovalNotes;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

public interface IDrawApprovalNotesRepository: IGenericRepository<DrawApprovalNoteDocument>
{
    Task<IEnumerable<DrawApprovalNoteDocument>> GetByDrawApprovalId(string id, CancellationToken ct);
    Task SoftDelete(string id, string userId, CancellationToken ct);
}