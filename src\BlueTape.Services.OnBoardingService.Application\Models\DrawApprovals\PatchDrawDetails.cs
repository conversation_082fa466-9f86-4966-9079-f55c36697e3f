﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class PatchDrawDetails
{
    public DateOnly? LoansLastDefaultedDate { get; set; }
    public int? MaxPastDueDays { get; set; }
    public decimal? CreditLimit { get; set; }
    public decimal? CurrentCreditLimitPercentage { get; set; }
    public decimal? CreditLimitPercentageWithCurrentDraw { get; set; }
    public CreditPurchaseType? CreditPurchaseType { get; set; }
    public decimal? CreditAvailableBalance { get; set; }
    public decimal? ProjectAvailableBalance { get; set; }
    public decimal? ProjectContractValue { get; set; }
    public DateOnly? ProjectEndDate { get; set; }
    public decimal? OutstandingBalance { get; set; }
    public CreditStatus? AccountStatus { get; set; }
    public ProjectApprovalStatus? ProjectApprovalStatus { get; set; }
    public decimal? PastDueAmount { get; set; }
}
