﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.IntegrationLogs;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.Application.Tests.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Moq;
using Newtonsoft.Json;
using Shouldly;
using System.Linq.Expressions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Strategies.Reports;

public class CreditRatingCoOwnersStepDataRetrievingStrategyTests
{
    private readonly CreditRatingCoOwnersStepDataRetrievingStrategy _strategy;

    private readonly Mock<IDecisionEngineStepsBviResultsService> _decisionEngineStepsBviResultsServiceMock = new();
    private readonly Mock<IDecisionEngineStepsRepository> _decisionEngineStepsRepository = new();
    private readonly Mock<ICreditApplicationAuthorizationDetailsService> _accountAuthorizationService = new();

    public CreditRatingCoOwnersStepDataRetrievingStrategyTests()
    {
        _strategy = new CreditRatingCoOwnersStepDataRetrievingStrategy(
            _decisionEngineStepsRepository.Object,
            _decisionEngineStepsBviResultsServiceMock.Object,
            _accountAuthorizationService.Object);
    }

    [Theory, CustomAutoData]
    public async Task CollectReportStepData_ValidData_ReturnsDataForReport(CreditApplicationDocument creditApplication, List<DecisionEngineStepsDocument> steps,
        List<RequestResponseBaseModel> bviResults, CreditApplicationAuthorizationDetails accountAuthorizationDetails)
    {
        steps.Last().CreatedAt = DateTime.MaxValue;
        bviResults[2] = null!;
        creditApplication.Id = TestDataConstants.CreditApplicationId;
        creditApplication.CompanyId = TestDataConstants.CompanyId;
        _decisionEngineStepsRepository.Setup(x =>
            x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(steps);
        _decisionEngineStepsBviResultsServiceMock.Setup(x => x.GetBviResponsesByStepId(steps.Last().Id, default))
            .ReturnsAsync(bviResults);
        _accountAuthorizationService.Setup(x =>
            x.GetByCreditApplicationId(TestDataConstants.CreditApplicationId, default)).ReturnsAsync(accountAuthorizationDetails);
        var inputString = JsonConvert.SerializeObject(new
        {
            steps.Last().ExecutionId,
            CreditApplicationId = creditApplication.Id,
        }, Formatting.Indented);
        var outputString = JsonConvert.SerializeObject(steps.Last(), Formatting.Indented);

        var result = await _strategy.CollectReportStepData(TestDataConstants.CreditApplicationId, TestDataConstants.CompanyId, default);

        result.ShouldNotBeNull();
        result.CompanyId.ShouldBe(creditApplication.CompanyId);
        result.StepName.ShouldBe(StepName.CoOwnersCreditRating);
        result.CreditApplicationId.ShouldBe(creditApplication.Id);
        result.StepInput.ShouldBe(inputString);
        result.StepOutput.ShouldBe(outputString);
        result.BviResults.Length.ShouldBe(2);
        result.BviResults[0].IntegrationSource.ShouldBe(bviResults[0].RequestType);
        result.BviResults[1].IntegrationSource.ShouldBe(bviResults[1].RequestType);
        result.BviResults[0].ResponseJsonString.ShouldBe(bviResults[0].Response?.RawBody);
        result.BviResults[1].ResponseJsonString.ShouldBe(bviResults[1].Response?.RawBody);
        result.AccountAuthorizationDetails.ShouldBe(JsonConvert.SerializeObject(accountAuthorizationDetails.AccountAuthorizationDetailsSnapshot, Formatting.Indented));
    }

    [Theory, CustomAutoData]
    public async Task CollectReportStepData_RequestTypeNull_SetRequestUrlInIntegrationSource(CreditApplicationDocument creditApplication, List<DecisionEngineStepsDocument> steps)
    {
        var bviResults = new List<RequestResponseBaseModel?>()
        {
            new()
            {
               RequestUrl = "url"
            },
            new()
            {
            }
        };
        _decisionEngineStepsRepository.Setup(x =>
                x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(steps);
        _decisionEngineStepsBviResultsServiceMock.Setup(x => x.GetBviResponsesByStepId(It.IsAny<string>(), default))
            .ReturnsAsync(bviResults);
        _accountAuthorizationService.Setup(x =>
                x.GetByCreditApplicationId(creditApplication.Id, default))
            .ReturnsAsync(new CreditApplicationAuthorizationDetails { AccountAuthorizationDetailsSnapshot = new() });

        var result = await _strategy.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default);

        result.ShouldNotBeNull();
        result.BviResults[0].IntegrationSource.ShouldBe(bviResults[0]!.RequestUrl);
        result.BviResults[1].IntegrationSource.ShouldBe(string.Empty);
    }

    [Fact]
    public async Task CollectReportStepData_OutputIsNull_ReturnsNull()
    {
        _decisionEngineStepsRepository.Setup(x =>
                x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(Enumerable.Empty<DecisionEngineStepsDocument>());
        var result = await _strategy.CollectReportStepData(TestDataConstants.CreditApplicationId, TestDataConstants.CompanyId, default);
        result.ShouldBeNull();
    }

    [Theory, CustomAutoData]
    public async Task CollectReportStepData_RawResponseNull_SetEmptyStringToResponseJsonString(CreditApplicationDocument creditApplication, List<DecisionEngineStepsDocument> steps)
    {
        var bviResults = new List<RequestResponseBaseModel?>()
        {
            new()
            {
                Response = new()
                {
                    RawBody = null
                }
            }
        };
        _decisionEngineStepsRepository.Setup(x =>
                x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(steps);
        _decisionEngineStepsBviResultsServiceMock.Setup(x => x.GetBviResponsesByStepId(It.IsAny<string>(), default))
            .ReturnsAsync(bviResults);
        _accountAuthorizationService.Setup(x =>
                x.GetByCreditApplicationId(creditApplication.Id, default))
            .ReturnsAsync(new CreditApplicationAuthorizationDetails { AccountAuthorizationDetailsSnapshot = new() });

        var result = await _strategy.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default);
        result!.BviResults[0].ResponseJsonString.ShouldBe(string.Empty);
    }

    [Theory, CustomAutoData]
    public Task CollectReportStepData_StepIdNull_ThrowsValidationException(CreditApplicationDocument creditApplication, List<DecisionEngineStepsDocument> steps,
        List<RequestResponseBaseModel> bviResults)
    {
        steps.Last().CreatedAt = DateTime.MaxValue;
        steps.Last().Id = null!;
        creditApplication.Id = TestDataConstants.CreditApplicationId;
        creditApplication.CompanyId = TestDataConstants.CompanyId;
        _decisionEngineStepsRepository.Setup(x =>
                x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(steps);
        _decisionEngineStepsBviResultsServiceMock.Setup(x => x.GetBviResponsesByStepId(steps.Last().Id, default))
            .ReturnsAsync(bviResults);
        _accountAuthorizationService.Setup(x =>
                x.GetByCreditApplicationId(creditApplication.Id, default))
            .ReturnsAsync(new CreditApplicationAuthorizationDetails { AccountAuthorizationDetailsSnapshot = new() });

        var act = () => _strategy.CollectReportStepData(TestDataConstants.CreditApplicationId, TestDataConstants.CompanyId, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public void IsApplicable_ApplicableStepName_ReturnsTrue()
    {
        var result = _strategy.IsApplicable(StepName.CoOwnersCreditRating);

        result.ShouldBeTrue();
    }

    [Fact]
    public void IsApplicable_NotApplicableStepName_ReturnsFalse()
    {
        var result = _strategy.IsApplicable(StepName.AffordabilityAssessment);

        result.ShouldBeFalse();
    }
}