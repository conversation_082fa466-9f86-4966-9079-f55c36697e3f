﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<NoWarn>1701;1702;1591;1573</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<NoWarn>1701;1702;1591;1573</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.ExpressionMapping" Version="6.0.4" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
		<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
		<PackageReference Include="BlueTape.AzureKeyVault" Version="1.0.3" />
		<PackageReference Include="BlueTape.Common.ExceptionHandling" Version="1.0.8" />
		<PackageReference Include="BlueTape.OBS" Version="1.6.71" />
		<PackageReference Include="DateOnlyTimeOnly.AspNet.Swashbuckle" Version="2.1.1" />
		<PackageReference Include="FluentValidation" Version="11.9.0" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Serilog" Version="3.1.1" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
		<PackageReference Include="Serilog.Enrichers.GlobalLogContext" Version="3.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
		<PackageReference Include="Serilog.Sinks.Logz.Io" Version="7.2.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="6.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\BlueTape.Services.OnBoardingService.Application\BlueTape.Services.OnBoardingService.Application.csproj" />
		<ProjectReference Include="..\BlueTape.Services.OnBoardingService.Compatibility\BlueTape.Services.OnBoardingService.Compatibility.csproj" />
		<ProjectReference Include="..\BlueTape.Services.OnBoardingService.Infrastructure\BlueTape.Services.OnBoardingService.Infrastructure.csproj" />
		<ProjectReference Include="..\BlueTape.Services.OnBoardingService.RefreshDetectorService\BlueTape.Services.OnBoardingService.RefreshDetectorService.csproj" />
	</ItemGroup>

</Project>
