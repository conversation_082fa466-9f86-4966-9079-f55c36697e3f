{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "AllowedHosts": "*", "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_qa"}, "StepsReportOptions": {"BucketName": "qa.uw1.linqpal-temp-assets"}, "SlackNotification": {"ErrorSnsTopicName": "obs-notifications-qa"}, "ConnectorQueueOptions": {"TopicName": "netsuite-connector-qa.fifo"}, "InitializationStepOptions": {"CreditApplicationsStateMachineName": "dotnet-decision-engine-qa", "DrawApprovalsStateMachineName": "dotnet-draw-approval-qa"}}