﻿using BlueTape.MongoDB.Constants;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;

public class PreliminaryStepDataRetrievingStrategy : StepDataRetrievingStrategyBase
{
    public PreliminaryStepDataRetrievingStrategy(
        IDecisionEngineStepsRepository decisionEngineStepsRepository,
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService)
        : base(decisionEngineStepsRepository, decisionEngineStepsBviResultsService, creditApplicationAuthorizationDetailsService)
    {
    }

    protected override StepName StepName => StepName.Preliminary;

    protected override Task<BviResultModel[]> GetBviResults(string? stepId, string? companyId, AccountAuthorization? accountAuthorizationDetails, CancellationToken ct)
    {
        var accountAuthBusinessDetails = accountAuthorizationDetails?.BusinessDetails;

        var bviResult = new
        {
            accountAuthBusinessDetails?.LastEINRejectionDate,
            accountAuthBusinessDetails?.LoansLastDefaultedDate,
            accountAuthBusinessDetails?.BusinessStartDate
        };

        return Task.FromResult(new BviResultModel[]
        {
            new()
            {
                IntegrationSource = IntegrationServicesNamesConstants.LoanService,
                ResponseJsonString =bviResult.ToFormattedJsonString()
            }
        });
    }
}