﻿using AutoMapper;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.DrawApproval.Requests;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.DrawApprovals)]
[ApiController]
public class DrawApprovalController : ControllerBase
{
    private readonly IDrawApprovalService _drawApprovalService;
    private readonly IMapper _mapper;

    public DrawApprovalController(
        IDrawApprovalService drawApprovalService,
        <PERSON><PERSON>apper mapper,
        ILogger<DrawApprovalController> logger)
    {
        _drawApprovalService = drawApprovalService;
        _mapper = mapper;
    }

    /// <summary>
    /// Get array of draw approvals by different filters
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /drawApprovals?Id=650b28825f07d3ca092f294a
    ///     GET /drawApprovals?CompanyId=5fc7cb7fc5f00ee425c5d2d689c8383f
    ///     GET /drawApprovals?Id=650b28825f07d3ca092f294a&amp;CompanyId=5fc7cb7fc5f00ee425c5d2d689c8383f
    ///
    /// </remarks>
    /// <returns>Array of draw approvals</returns>
    [HttpGet]
    public async Task<GetQueryWithPaginationResultDto<DrawApprovalDto>> Get([FromQuery] GetDrawApprovalsQueryWithPagination drawApprovalsQuery, CancellationToken ct)
    {
        return _mapper.Map<GetQueryWithPaginationResultDto<DrawApprovalDto>>(await _drawApprovalService.GetByFilter(drawApprovalsQuery, ct));
    }

    /// <summary>
    /// Get draw approval by id
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /drawApprovals/650b28825f07d3ca092f294a
    ///
    /// </remarks>
    /// <returns>Draw approval</returns>
    [HttpGet(EndpointConstants.Id)]
    public async Task<DrawApprovalDto> GetById([FromRoute] string id, CancellationToken ct)
    {
        return _mapper.Map<DrawApprovalDto>(await _drawApprovalService.GetById(id, ct));
    }

    /// <summary>
    /// Patch draw approval by id
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /drawApprovals/650b28825f07d3ca092f294a
    ///
    /// </remarks>
    /// <returns>Draw approval</returns>
    [HttpPatch(EndpointConstants.Id)]
    public async Task<DrawApprovalDto> Patch([FromRoute] string id, [FromBody] PatchDrawApprovalInternalRequest patchDrawApproval, CancellationToken ct)
    {
        var patchModel = _mapper.Map<PatchDrawApproval>(patchDrawApproval);
        patchModel.Id = id;

        return _mapper.Map<DrawApprovalDto>(await _drawApprovalService.Patch(patchModel, ct));
    }

    /// <summary>
    /// Start Draw Approval Execution
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /DrawApprovals/Execute
    ///
    /// </remarks>
    [HttpPost($"{EndpointConstants.Execute}")]
    public Task<StepFunctionsExecutionResponse> ExecuteDecisionEngineForDrawApproval(
        [FromBody] DrawApprovalDecisionEngineExecutionRequest request, CancellationToken ct)
    {
        return _drawApprovalService.RunDrawApprovalDecisionEngineExecution(request, ct);
    }

    /// <summary>
    /// Start Draw Approval Initialization Step Execution
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /DrawApproval/Execute/InitializationStep
    /// 
    /// </remarks>
    [HttpPost($"{EndpointConstants.Execute}/{EndpointConstants.InitializationStep}")]
    public Task<StepFunctionsExecutionResponse> RunDecisionEngineInitializationStepForDrawApproval(
        [FromBody] DrawApprovalInitializationStepStartRequest request, CancellationToken ct)
    {
        return _drawApprovalService.RunDecisionEngineInitializationStepForDrawApproval(
            _mapper.Map<Application.Models.DecisionEngineExecution.DrawApprovalInitializationStepStartRequest>(request), ct);
    }

    [HttpPost]
    public async Task<DrawApprovalDto> CreateDrawApproval(CreateDrawApprovalRequest drawApprovalRequest, CancellationToken ctx)
    {
        var createDrawApproval = _mapper.Map<CreateDrawApproval>(drawApprovalRequest);
        var result = await _drawApprovalService.Create(createDrawApproval, ctx);

        return _mapper.Map<DrawApprovalDto>(result);
    }

    [HttpPost(EndpointConstants.InvoicesIds)]
    public async Task<DrawApprovalDto> GetByInvoicesIds([FromBody] string[] invoicesIds, CancellationToken ct)
        => _mapper.Map<DrawApprovalDto>(await _drawApprovalService.GetByInvoicesIds(invoicesIds, ct));

    [HttpPost(EndpointConstants.Invoices)]
    public async Task<IEnumerable<DrawApprovalDto>> GetManyByInvoicesIds([FromBody] string[] invoicesIds, CancellationToken ct)
        => _mapper.Map<IEnumerable<DrawApprovalDto>>(await _drawApprovalService.GetManyByInvoicesIds(invoicesIds, ct));

    [HttpPut(EndpointConstants.Id)]
    public async Task<DrawApprovalDto> UpdateDrawApproval([FromRoute] string id, UpdateDrawApprovalRequest updateDrawApprovalRequest, CancellationToken ct)
        => _mapper.Map<DrawApprovalDto>(await _drawApprovalService.UpdateDrawApproval(id, _mapper.Map<UpdateDrawApproval>(updateDrawApprovalRequest), default));

    [HttpPatch("{id}/details")]
    public async Task<DrawApprovalDto> PatchDrawApprovalDetails([FromRoute] string id, PatchDrawDetailsInternalRequest drawApprovalRequest, CancellationToken ctx)
    {
        var patchDrawApproval = _mapper.Map<PatchInternalDrawApproval>(drawApprovalRequest);
        var result = await _drawApprovalService.PatchDrawApprovalDetails(id, patchDrawApproval, ctx);

        return _mapper.Map<DrawApprovalDto>(result);
    }

    [HttpPost($"{EndpointConstants.Id}/{EndpointConstants.Invoice}")]
    public async Task<DrawApprovalDto> AddInvoices([FromRoute] string id, AddPayablesToDrawRequest request, CancellationToken ct)
        => _mapper.Map<DrawApprovalDto>(await _drawApprovalService.AddInvoices(id, _mapper.Map<List<PayableItem>>(request.Paybles), ct));

    [HttpGet("{id}/project")]
    public Task<string?> GetProjectIdByDrawApprovalId([FromRoute] string id, CancellationToken ctx)
    {
        return _drawApprovalService.GetProjectIdByDrawId(id, ctx);
    }

    [HttpPatch($"{EndpointConstants.Id}/{EndpointConstants.AuthorizationPeriod}")]
    public async Task<DrawApprovalDto> PatchAuthorizationPeriodId([FromRoute] string id, [FromBody] PatchDrawApprovalAuthorizationPeriodIdRequest patchRequest, CancellationToken ct)
        => _mapper.Map<DrawApprovalDto>(await _drawApprovalService.PatchAuthorizationPeriod(id, patchRequest.AuthorizationPeriodId, ct));

    [HttpGet("MigrateCreditAndAccountStatuses")]
    public Task MigrateDrawApprovalCreditAndAccountStatuses(CancellationToken ct)
    {
        return _drawApprovalService.MigrateDrawApprovalCreditAndAccountStatuses(ct);
    }
}
