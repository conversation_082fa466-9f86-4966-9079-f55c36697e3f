﻿using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.LoanApplication;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
public interface ILoanApplicationRepository : IGenericRepository<LoanApplicationDocument>
{
    Task<LoanApplicationDocument?> GetByInvoicesIds(IEnumerable<string> invoiceIds, CancellationToken ct);
    Task<List<LoanApplicationDocument>?> GetByCreditApplicationInfo(string creditApplicationId, string einHash, CancellationToken ct);
    Task UpsertOutputDocument(string loanApplicationId, OutputDocument outputDocument, CancellationToken ct);
    Task<List<LoanApplicationDocument>> GetAllByFilters(GetLoanApplicationQuery query, CancellationToken ct);
    Task<BsonDocument> GetRejectedLoanApplicationsCount(string companyId, string status, CancellationToken ct);
    Task<BsonDocument> GetTotalOutstandingApprovedLoanAmount(string companyId, CancellationToken ct);
    Task<List<LoanApplicationDocument>> GetApprovedLoanApplicationsWithOutstandingAmount(string companyId,
        string status, CancellationToken ct);
    Task<List<BsonDocument>> GetDocumentsByEinHashWithPipeline(List<BsonDocument> lookupPipeline,
        List<PipelineStageDefinition<LoanApplicationDocument, BsonDocument>>? prePipeline, string einHash,
        CancellationToken ct);
    Task<List<BsonDocument>> GetDocumentsBySsnHashWithPipeline(string ssnHash,
        List<PipelineStageDefinition<LoanApplicationDocument, BsonDocument>>? prePipeline, List<BsonDocument> lookupPipeline, CancellationToken ct);
    Task UpdateManyByIds(IEnumerable<string> loanApplicationIds, UpdateLoanApplicationDocument updateLoanApplication, CancellationToken cancellationToken);
    Task<LoanApplicationDocument?> GetFirstApprovedLegacyLoanByCompany(string companyId, CancellationToken ct);
    Task<LoanApplicationDocument?> GetByDrawId(string id, CancellationToken ct);
}
