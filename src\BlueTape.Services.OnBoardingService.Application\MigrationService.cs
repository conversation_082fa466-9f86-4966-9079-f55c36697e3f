﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Cipher;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Company;
using BlueTape.Services.OnBoardingService.Domain.Documents.User;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BlueTape.Services.OnBoardingService.Application;

public class MigrationService(ICreditApplicationService creditApplicationService,
    IDraftService draftService,
    ICompanyHttpClient companyService,
    ICompanyRepository companyRepository,
    IAccountAuthorizationsService accountAuthorizationsService,
    IUserRepository userRepository,
    ICreditApplicationRepository creditApplicationRepository,
    ICardPricingPackageRepository cardPricingPackageRepository,
    ILoanPricingPackageRepository loanPricingPackageRepository,
    IDecisionEngineStepsRepository decisionEngineStepsRepository,
    IDrawApprovalRepository drawApprovalRepository,
    ILogger<MigrationService> logger) : IMigrationService
{
    public async Task MigrateType(CancellationToken ct)
    {
        var creditApps = await creditApplicationRepository.GetAll(ct);
        foreach (var creditApp in creditApps)
        {
            if (!string.IsNullOrEmpty(creditApp.Type)) continue;
            creditApp.Type = CreditApplicationType.LineOfCredit.ToString();
            await creditApplicationRepository.Update(creditApp, ct);
        }
    }

    //create getpaid CreditApplications and empty AccountAuthorizationsDetails only
    // 
    // Create as many creditApplications with type: getPaid as many active and rejected suppliers we have,
    // fill the fields accordingly.
    // Account authorizations will be created with mostly empty data.
    // Not filled, just created (could be a problem later).
    // No decision engine steps are created.
    public async Task MigrateGetPaidApplications(CancellationToken cancellationToken)
    {
        logger.LogInformation("MigrateGetPaidApplications: started migration process. Getting existing getPaid apps");

        var migratedCreditApplications = new List<CreateCreditApplication>();
        var migratedAccountAuthorizations = new List<CreateAccountAuthorization>();

        var existingGetPaidCreditApplications = await creditApplicationService.GetAllByFilters(new GetCreditApplicationQuery()
        {
            Type = [CreditApplicationType.GetPaid.ToString()]
        }, cancellationToken);
        var existingGetPaidCreditApplicationsCompanyIds = existingGetPaidCreditApplications.Select(x => x.CompanyId).Distinct();

        logger.LogInformation("MigrateGetPaidApplications: received existing getPaid apps. Count: {getPaidAppsCount}." +
                              " Started getting suppliers", existingGetPaidCreditApplications.Count());

        var suppliers = await companyRepository.GetSuppliers(cancellationToken);

        var bankAccountIds = suppliers.SelectMany(x => x.BankAccounts).Select(x => x.ToString()).Distinct().Where(id => !string.IsNullOrEmpty(id) && id.Length == 24);
        var loanPricingPackagesTitles = suppliers.Select(x => x.Settings?.LoanPricingPackageId).Distinct().Where(x => !string.IsNullOrEmpty(x));
        var cardPricingPackagesTitles = suppliers.Select(x => x.Settings?.CardPricingPackageId).Distinct().Where(x => !string.IsNullOrEmpty(x));

        var loanPricingPackages = await loanPricingPackageRepository.GetByTitles(loanPricingPackagesTitles!, cancellationToken);
        var cardPricingPackages = await cardPricingPackageRepository.GetByTitles(cardPricingPackagesTitles!, cancellationToken);

        logger.LogInformation("MigrateGetPaidApplications: Finished getting suppliers. Count: {suppliersCount}. Started getting suppliers bank accounts", suppliers.Count());

        var bankAccounts = await companyService.GetBankAccountsByIdsAsync(bankAccountIds, cancellationToken);

        logger.LogInformation("MigrateGetPaidApplications: Finished getting suppliers bank accounts. Count: {bankAccountsCount}.", bankAccounts.Count());

        foreach (var supplier in suppliers)
        {
            logger.LogInformation("MigrateGetPaidApplications: Started migration for supplier {supplierId}.", supplier.Id);

            if (existingGetPaidCreditApplicationsCompanyIds.Contains(supplier.Id))
            {
                logger.LogInformation("MigrateGetPaidApplications: skip migration for supplier: {companyId} " +
                                      "as he already has get paid credit application", supplier.Id);
                continue;
            }

            var draft = (await draftService.GetAllByFilters(new DraftFilter() { CompanyId = supplier.Id }, cancellationToken)).FirstOrDefault();
            var user = string.IsNullOrEmpty(draft?.Sub)
                ? null
                : await userRepository.GetBySub(draft.Sub, cancellationToken);

            if (draft == null)
            {
                logger.LogInformation("MigrateGetPaidApplications: skip migration for supplier: {companyId} " +
                                      "as he does not have draft", supplier.Id);

                continue;
            }

            logger.LogInformation("MigrateGetPaidApplications:Received draft {draftId} for supplier {supplierId}.", draft.Id, supplier.Id);

            var supplierBankAccountsIds = supplier.BankAccounts.Select(x => x.ToString()).Where(id => !string.IsNullOrEmpty(id) && id.Length == 24).ToList();
            var supplierBankAccounts = bankAccounts.Where(x => supplierBankAccountsIds.Contains(x.Id));
            var businessCategory = supplier.Settings?.OnBoardingType?.LastOrDefault();

            CreateCreditApplication createCreditApplicationModel;
            if (draft.Type == "general_application")
            {
                createCreditApplicationModel = GetCreateCreditApplicationModelFromGeneralApplicationDraft(draft, supplier, businessCategory, supplierBankAccounts, user);
            }
            else
            {
                createCreditApplicationModel =
                    GetCreateCreditApplicationModelFromSupplierApplicationDraft(draft, supplier, businessCategory,
                        supplierBankAccounts, user);
            }

            var loanPricingPackage = loanPricingPackages.FirstOrDefault(x => x.Name == supplier.Settings?.LoanPricingPackageId);
            var cardPricingPackage = cardPricingPackages.FirstOrDefault(x => x.Name == supplier.Settings?.CardPricingPackageId);
            var isValidCardPricingPackageId = ObjectId.TryParse(supplier.Settings?.CardPricingPackageId, out _);
            var isValidLoanPricingPackageId = ObjectId.TryParse(supplier.Settings?.LoanPricingPackageId, out _);

            createCreditApplicationModel.MerchantSettings = new CreateMerchantSettings()
            {
                CardPricingPackageId = isValidCardPricingPackageId ? supplier.Settings?.CardPricingPackageId : cardPricingPackage?.Id,
                LoanPricingPackageId = isValidLoanPricingPackageId ? supplier.Settings?.LoanPricingPackageId : loanPricingPackage?.Id
            };

            logger.LogInformation("MigrateGetPaidApplications: Generated create credit app model for supplier {supplierId}.", supplier?.Id);
            migratedCreditApplications.Add(createCreditApplicationModel);

            var accountAuthDetails = new CreateAccountAuthorization()
            {
                CompanyId = supplier?.Id ?? string.Empty,
                CreatedBy = "BlueTape.OnBoardingService.MigrationService",
                EinHash = createCreditApplicationModel.EinHash ?? string.Empty,
            };

            logger.LogInformation("MigrateGetPaidApplications: Generated create account auth details  model for supplier {supplierId}.", supplier?.Id);
            migratedAccountAuthorizations.Add(accountAuthDetails);
        }

        logger.LogInformation("MigrateGetPaidApplications: stated adding credit applications into database");
        await creditApplicationService.CreateRange(migratedCreditApplications, cancellationToken);
        logger.LogInformation("MigrateGetPaidApplications: finished adding credit applications into database");

        logger.LogInformation("MigrateGetPaidApplications: stated adding account authorizations into database");
        await accountAuthorizationsService.CreateRange(migratedAccountAuthorizations, cancellationToken);
        logger.LogInformation("MigrateGetPaidApplications: finished adding account authorizations into database");

        logger.LogInformation("MigrateGetPaidApplications: finished migration process");
    }
    
    public async Task MigrateDecisionEngineExecutionType(CancellationToken ct)
        => await decisionEngineStepsRepository.MigrateExecutionType(ct);

    public async Task MigrateExecutionIdForCreditApplications(CancellationToken ct)
    {
        var creditApplications = (await creditApplicationRepository.GetAll(x => string.IsNullOrEmpty(x.ExecutionId), ct)).ToList();
        var creditApplicationSteps = (await decisionEngineStepsRepository.GetByCreditApplicationIds(creditApplications.Select(x => x.Id).ToArray(), ct)).ToList();
        foreach (var creditApplication in creditApplications)
        {
            creditApplication.ExecutionId = creditApplicationSteps.Where(x => x.CreditApplicationId == creditApplication.Id).MaxBy(x => x.CreatedAt)!.ExecutionId;
            await creditApplicationRepository.Update(creditApplication, ct);
        }
    }

    public async Task MigrateExecutionIdForDrawApprovals(CancellationToken ct)
    {
        var drawApprovals = (await drawApprovalRepository.GetAll(x => string.IsNullOrEmpty(x.ExecutionId), ct)).ToList();
        var drawApprovalSteps = (await decisionEngineStepsRepository.GetByDrawApprovalIds(drawApprovals.Select(x => x.Id).ToArray(), ct)).ToList();
        foreach (var drawApproval in drawApprovals)
        {
            drawApproval.ExecutionId = drawApprovalSteps.Where(x => x.DrawApprovalId == drawApproval.Id).MaxBy(x => x.CreatedAt)!.ExecutionId;
            await drawApprovalRepository.Update(drawApproval, ct);
        }
    }

    private static CreateCreditApplication GetCreateCreditApplicationModelFromGeneralApplicationDraft(Draft draft, CompanyDocument supplier, string? businessCategory,
        IEnumerable<BankAccountModel> supplierBankAccounts, UserDocument? user)
    {
        var financeInfoItems = draft.Data?.Finance?.Items?.ToList();
        var arAdvanceRequestedString = financeInfoItems?.Find(x => x.Identifier == "arAdvanceRequest")?.Content?.ToString();
        var isArAdvanceRequestContentValid = bool.TryParse(arAdvanceRequestedString, out var arAdvanceRequested);
        var businessInfoItems = draft.Data?.BusinessInfo?.Items?.ToList();
        var businessNameContent = businessInfoItems?.Find(x => x.Identifier == "businessName")?.Content;
        var businessNameContentJString = businessNameContent as string;
        BusinessNameModel? businessName;
        if (!string.IsNullOrEmpty(businessNameContentJString))
        {
            businessName = new BusinessNameModel()
            {
                LegalName = businessNameContentJString
            };
        }
        else
        {
            var businessNameContentJObject = businessNameContent == null ? null : JObject.FromObject(businessNameContent);
            var businessNameContentString = businessNameContentJObject?.ToString();
            businessName = businessNameContentString == null ? new BusinessNameModel()
                : JsonConvert.DeserializeObject<BusinessNameModel>(businessNameContentString);

        }

        var businessEin = businessInfoItems?.Find(x => x.Identifier == "ein")?.Content;
        var businessEinContent = businessEin == null ? null : JObject.FromObject(businessEin);
        var businessEinContentString = businessEinContent?.ToString();

        var ein = businessEinContentString == null
            ? new CipherModel()
            : JsonConvert.DeserializeObject<CipherModel>(businessEinContentString);
        var legalName = businessName?.LegalName;
        var dba = businessName?.Dba;
        var einHash = ein?.Hash;
        var ownerInfo = draft.Data?.BusinessOwner?.Items?.ToList();
        var firstName = ownerInfo?.Find(x => x.Identifier == "firstName")?.Content?.ToString();
        var lastName = ownerInfo?.Find(x => x.Identifier == "lastName")?.Content?.ToString();
        var ownerName = $"{firstName} {lastName}";
        var applicantName = user == null ? null : $"{user.FirstName} {user.LastName}";

        var createCreditApplicationModel = new CreateCreditApplication()
        {
            ApplicationDate = draft.SubmitDate ?? draft.CreatedAt,
            EinHash = einHash,
            BusinessDba = dba,
            BusinessName = legalName,
            ApplicantName = applicantName,
            SupplierName = ownerName,
            ApprovedCreditLimit = null,
            CreatedBy = "BlueTape.OnBoardingService.MigrationService",
            RequestedAmount = null,
            Status = CalculateMigratedGetPaidAppStatus(supplier).ToString(),
            CompanyId = supplier.Id,
            MerchantId = supplier?.Id,
            DraftId = draft.Id,
            BusinessCategory = businessCategory,
            Type = CreditApplicationType.GetPaid,
            BankAccountType = GetBankAccountType(supplierBankAccounts),
            IsArAdvanceRequested = isArAdvanceRequestContentValid && arAdvanceRequested,
            AutomatedDecisionResult = AutomatedDecisionResult.Pass.ToString(),
            LastStatusChangedAt = DateTime.UtcNow,
            LastStatusChangedBy = "<migrated>"
        };
        return createCreditApplicationModel;
    }

    private static CreateCreditApplication GetCreateCreditApplicationModelFromSupplierApplicationDraft(Draft draft, CompanyDocument supplier, string? businessCategory,
    IEnumerable<BankAccountModel> supplierBankAccounts, UserDocument? user)
    {
        var businessItems = draft.Data?.Business?.Items?.ToList();
        var business = businessItems?.FirstOrDefault(x => x.Identifier == "details")?.Content;
        var businessDetailsContent = business == null ? null : JObject.FromObject(business);
        var businessDetailsContentString = businessDetailsContent?.ToString();
        var businessDetails = string.IsNullOrEmpty(businessDetailsContentString) ? new BusinessDetailsContent()
            : JsonConvert.DeserializeObject<BusinessDetailsContent>(businessDetailsContentString);

        var legalName = businessDetails?.Name;
        var dba = businessDetails?.Dba;
        var einHash = businessDetails?.Ein?.Hash;
        businessCategory ??= businessDetails?.Category?.LastOrDefault();

        var ownerInfo = draft.Data?.Ownership?.Items?.Where(x => x.Identifier != "percentage").ToList();
        var ownersInfo = ownerInfo?.Select(ParseBusinessOwnerContent);
        var businessOwner = ownersInfo == null ? new BusinessOwnerContent() : ownersInfo.Where(x => x is not null).FirstOrDefault(x => x is { Owner: not null } && x.Owner.Value);
        var ownerName = businessOwner == null ? null : $"{businessOwner.FirstName} {businessOwner.LastName}";
        var applicantName = user == null ? null : $"{user.FirstName} {user.LastName}";
        var createCreditApplicationModel = new CreateCreditApplication()
        {
            ApplicationDate = draft.SubmitDate ?? draft.CreatedAt,
            EinHash = einHash,
            BusinessDba = dba,
            BusinessName = legalName,
            ApplicantName = applicantName,
            SupplierName = ownerName,
            ApprovedCreditLimit = null,
            CreatedBy = "BlueTape.OnBoardingService.MigrationService",
            RequestedAmount = null,
            Status = CalculateMigratedGetPaidAppStatus(supplier).ToString(),
            CompanyId = supplier.Id,
            MerchantId = supplier.Id,
            DraftId = draft.Id,
            BusinessCategory = businessCategory,
            Type = CreditApplicationType.GetPaid,
            BankAccountType = GetBankAccountType(supplierBankAccounts),
            IsArAdvanceRequested = false,
            AutomatedDecisionResult = AutomatedDecisionResult.Pass.ToString(),
            LastStatusChangedAt = DateTime.UtcNow,
            LastStatusChangedBy = "<migrated>"
        };
        return createCreditApplicationModel;
    }

    private static BusinessOwnerContent? ParseBusinessOwnerContent(Item item)
    {
        var itemContent = item.Content == null ? null : JObject.FromObject(item.Content);
        var itemContentString = itemContent?.ToString();
        if (string.IsNullOrEmpty(itemContentString)) return new BusinessOwnerContent();
        return JsonConvert.DeserializeObject<BusinessOwnerContent>(itemContentString);
    }

    private static CreditApplicationStatus CalculateMigratedGetPaidAppStatus(CompanyDocument supplier)
    {
        return string.Equals(supplier.Status, "rejected", StringComparison.InvariantCultureIgnoreCase)
            ? CreditApplicationStatus.Rejected
            : CreditApplicationStatus.Approved;
    }

    private static BankAccountType? GetBankAccountType(IEnumerable<BankAccountModel>? bankAccounts)
    {
        if (bankAccounts == null || !bankAccounts.Any()) return null;

        var plaidBankAccounts = bankAccounts.Where(x => x.Plaid is { Status: "active" });
        var manualBankAccounts = bankAccounts.Where(x => x.Plaid == null && x.isManualEntry == true);

        if (plaidBankAccounts.Any() && manualBankAccounts.Any()) return BankAccountType.Both;
        if (plaidBankAccounts.Any()) return BankAccountType.Plaid;
        if (manualBankAccounts.Any()) return BankAccountType.Manual;

        return null;
    }

    private class BusinessNameModel
    {
        public string? LegalName { get; set; }
        public string? Dba { get; set; }
    }

    private class BusinessDetailsContent
    {
        public string? Name { get; set; }
        public string? Dba { get; set; }
        public string[]? Category { get; set; }
        public CipherModel? Ein { get; set; }
    }

    private class BusinessOwnerContent
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool? Owner { get; set; }
    }
}
