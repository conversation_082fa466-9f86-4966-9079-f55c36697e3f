﻿using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Configuration.Models;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Senders;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService.DI;

public static class DependencyInjection
{
    public static void UseAuthorizationDetailsRefreshDetector(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IManualAuthorizationDetailsRefreshDetectorService, ManualAuthorizationDetailsRefreshDetectorService>();
        services.AddScoped<IAuthorizationDetailsRefreshDetectorService, AuthorizationDetailsRefreshDetectorService>();
        services.AddScoped<ICompanyScheduledUpdateEventsGenerator, CompanyScheduledUpdateEventsGenerator>();
        services.AddScoped<ICompanyManualScheduledUpdateEventsGenerator, CompanyManualScheduledUpdateEventsGenerator>();
        services.AddScoped<IRefreshServiceMessageSender, RefreshServiceMessageSender>();
        services.AddScoped<IAuthorizationDetailsRefreshService, AuthorizationDetailsRefreshService>();
        services.Configure<AuthorizationDetailsDetectorMessagingOptions>(configuration.GetSection(nameof(AuthorizationDetailsDetectorMessagingOptions)));
    }
}
