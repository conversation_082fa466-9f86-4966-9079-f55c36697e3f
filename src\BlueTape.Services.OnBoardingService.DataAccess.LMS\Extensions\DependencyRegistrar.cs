﻿using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.RetryPolicies;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.OnBoardingService.DataAccess.LMS.Extensions;

public static class DependencyRegistrar
{
    public static void AddLMSExternalServiceDependencies(this IServiceCollection services, IConfiguration config)
    {
        services.AddHttpClient<ILoanServiceHttpClient, LoanServiceHttpClient>(client =>
            {
                client.BaseAddress = GetBaseAddressUri(config, LoanServiceConstants.LoanServiceUrl);
            }).SetHandlerLifetime(TimeSpan.FromMinutes(5))
            .AddPolicyHandler((serviceProvider, request) => HttpRetryPolicies.GetLoanServiceRetryPolicy(serviceProvider));

        services.AddScoped<ILoanExternalService, LoanExternalService>();
    }

    private static Uri GetBaseAddressUri(IConfiguration config, string configurationUrl) => new(config[configurationUrl]!);
}