﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
[MongoCollection("output")]
public class OutputDocument
{
    [BsonElement("step")]
    public string? Step { get; set; } = string.Empty;

    [BsonElement("data")]
    public object? Data { get; set; }
}
