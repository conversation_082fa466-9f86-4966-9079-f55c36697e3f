﻿using BlueTape.Services.OnBoardingService.Domain.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;

public class CreateSystemCreditApplicationNote
{
    public string CreditApplicationId { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public string OldValue { get; set; } = string.Empty;
    public string NewValue { get; set; } = string.Empty;
    public CreditApplicationNotesTemplateType SystemNoteTemplate { get; set; }
}
