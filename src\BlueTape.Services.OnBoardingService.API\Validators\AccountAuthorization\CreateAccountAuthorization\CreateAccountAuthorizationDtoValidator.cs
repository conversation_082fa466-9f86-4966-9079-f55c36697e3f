﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.CreateAccountAuthorization;

public class CreateAccountAuthorizationDtoValidator : AbstractValidator<CreateAccountAuthorizationDto>
{
    public CreateAccountAuthorizationDtoValidator()
    {
        RuleFor(x => x.CompanyId).NotEmpty();
        RuleFor(x => x.EinHash).NotEmpty();
    }
}