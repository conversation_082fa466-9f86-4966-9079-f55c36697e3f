﻿using BlueTape.MongoDB.Constants;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;

public class AffordabilityAssessmentStepDataRetrievingStrategy : StepDataRetrievingStrategyBase
{
    public AffordabilityAssessmentStepDataRetrievingStrategy(IDecisionEngineStepsRepository decisionEngineStepsRepository,
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService)
        : base(decisionEngineStepsRepository, decisionEngineStepsBviResultsService, creditApplicationAuthorizationDetailsService)
    {
    }

    protected override StepName StepName => StepName.AffordabilityAssessment;

    protected override async Task<BviResultModel[]> GetBviResults(string? stepId, string? companyId, AccountAuthorization? accountAuthorizationDetails, CancellationToken ct)
    {
        var accountAuthBusinessDetails = accountAuthorizationDetails?.BusinessDetails;
        var assetReportBviResult = new
        {
            accountAuthBusinessDetails?.RevenueVariancePercentage,
            accountAuthBusinessDetails?.TotalTradeLines,
            accountAuthBusinessDetails?.CompanyIncome,
            accountAuthBusinessDetails?.LoanRevenue,
            accountAuthBusinessDetails?.AnnualRevenue,
            accountAuthBusinessDetails?.DTI2Value,
        };

        var integrationLogsBviResults = await GetBviResultsFromIntegrationLogs(stepId, ct);
        var bviResults = integrationLogsBviResults.ToList();
        bviResults.Add(new()
        {
            IntegrationSource = IntegrationServicesNamesConstants.CashFlow,
            ResponseJsonString = assetReportBviResult.ToFormattedJsonString()
        });

        return bviResults.ToArray();
    }
}
