using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IDrawApprovalNotesService
{
    Task<IEnumerable<DrawApprovalNote>> GetByDrawApprovalId(string id, CancellationToken ct);
    Task<DrawApprovalNote> Add(CreateDrawApprovalNote createNote, CancellationToken ct);
    Task<DrawApprovalNote> Patch(PatchDrawApprovalNote patchModel, CancellationToken ct);
    Task Delete(DeleteDrawApprovalNote note, CancellationToken ct);
}