﻿namespace BlueTape.Services.OnBoardingService.API.Constants
{
    internal static class ControllersConstants
    {
        public const string Admin = "Admin";
        public const string Draft = "Drafts";
        public const string DecisionEngineSteps = "DecisionEngineSteps";
        public const string CreditApplications = "CreditApplications";
        public const string AccountAuthorizations = "AccountAuthorizations";
        public const string CreditApplicationAuthorizationDetails = "CreditApplicationAuthorizationDetails";
        public const string DecisionEngineStepsBVIResults = "DecisionEngineStepsResults";
        public const string DrawApprovals = "DrawApprovals";
        public const string PaymentPlans = "PaymentPlans";
        public const string Compatibility = "Compatibilities";
        public const string Users = "Users";
        public const string CustomerAccounts = "CustomerAccounts";
        public const string ParsedDrafts = "ParsedDrafts";
        public const string Deadrs = "DEADRS";
    }
}
