﻿namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IMigrationService
{
    Task MigrateGetPaidApplications(CancellationToken cancellationToken);
    Task MigrateType(CancellationToken ct);
    Task MigrateDecisionEngineExecutionType(CancellationToken ct);
    Task MigrateExecutionIdForCreditApplications(CancellationToken ct);
    Task MigrateExecutionIdForDrawApprovals(CancellationToken ct);
}
