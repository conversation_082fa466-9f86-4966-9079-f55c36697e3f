﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.ParsedDraft;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;

namespace BlueTape.Services.OnBoardingService.Application.Services;
public class ParsedDraftService(IParsedDraftRepository parsedDraftRepository, IDraftMapper draftMapper, IDraftService draftService, IMapper mapper)
    : IParsedDraftService
{

    public async Task<ParsedDraftModel> GetById(string parsedDraftId, CancellationToken ct)
    {
        var draftDocument = await parsedDraftRepository.GetById(parsedDraftId, ct);
        return mapper.Map<ParsedDraftModel>(draftDocument);
    }

    public async Task<ParsedDraftModel> GetByDraftId(string draftId, CancellationToken ct)
    {
        var draftDocument = await parsedDraftRepository.GetByDraftId(draftId, ct);
        if (draftDocument is not null) return mapper.Map<ParsedDraftModel>(draftDocument);

        var draft = await draftService.GetById(draftId, ct);
        var parsedDraft = await draftMapper.ParseDraft(draft);
        var parsedDraftDocument = mapper.Map<ParsedDraftDocument>(parsedDraft);
        var createdDraft = await parsedDraftRepository.Add(parsedDraftDocument, ct);

        return mapper.Map<ParsedDraftModel>(createdDraft);
    }

    public async Task<ParsedDraftModel> GetByCompanyId(string companyId, CancellationToken ct)
    {
        var draftDocument = await parsedDraftRepository.GetByCompanyId(companyId, ct);
        if (draftDocument is not null) return mapper.Map<ParsedDraftModel>(draftDocument);

        var drafts = await draftService.GetAllByFilters(new DraftFilter() { CompanyId = companyId }, ct);
        var unifiedDraft = drafts.FirstOrDefault(x => x.Type == "general_application");
        if (unifiedDraft is null) throw new VariableNullException("Unified draft not found");

        var parsedDraft = await draftMapper.ParseDraft(unifiedDraft);
        var parsedDraftDocument = mapper.Map<ParsedDraftDocument>(parsedDraft);
        var createdDraft = await parsedDraftRepository.Add(parsedDraftDocument, ct);
        return mapper.Map<ParsedDraftModel>(createdDraft);
    }

    public async Task<ParsedDraftModel> MigrateDraft(string draftId, CancellationToken ct)
    {
        var draft = await draftService.GetById(draftId, ct);
        var parsedDraft = await draftMapper.ParseDraft(draft);
        var parsedDraftDocument = mapper.Map<ParsedDraftDocument>(parsedDraft);

        var existingParsedDraft = await parsedDraftRepository.GetByDraftId(draftId, ct);
        if (existingParsedDraft is null)
        {
            var createdDraft = await parsedDraftRepository.Add(parsedDraftDocument, ct);
            return mapper.Map<ParsedDraftModel>(createdDraft);
        }

        parsedDraftDocument.Id = existingParsedDraft.Id;
        var updatedDraft = await parsedDraftRepository.Update(parsedDraftDocument, ct);
        return mapper.Map<ParsedDraftModel>(updatedDraft);
    }


}
