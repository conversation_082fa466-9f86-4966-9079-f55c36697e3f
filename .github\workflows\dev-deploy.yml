name: Dev Deploy

on: workflow_dispatch
  
env:
 <PERSON><PERSON><PERSON><PERSON>_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
 PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}
 OBJECTS_DIRECTORY: obj
 NUGET_PACKAGES_DIRECTORY: ".nuget"
 PATH_TO_SLN: "./src"
 GIT_DEPTH: '0'
 LP_AWS_ACCOUNT: "${{secrets.AWS_ACCOUNT_ID}}"
 AWS_REGION: us-west-1
 BRANCH: "${{ github.ref }}"
 AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }} 
 AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
  
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4.1.1
      with:
          fetch-depth: 0
          
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 6.0.x  

    - name: Restore dependencies
      run: dotnet restore $PATH_TO_SLN --packages $NUGET_PACKAGES_DIRECTORY -f
    - name: Build
      run: dotnet build --no-restore --configuration Release $PATH_TO_SLN
    - name: Test
      run: dotnet test $PATH_TO_SLN --no-build --no-restore --configuration Release --verbosity normal

  deploy:
   needs: build
   environment: dev
   runs-on: ubuntu-latest
   container:
     image: mcr.microsoft.com/dotnet/sdk:6.0

   env:
    STAGE: ${{ vars.STAGE }}
    ASPNETCORE_ENVIRONMENT: ${{ vars.STAGE }}
    LOGZIO_TOKEN: "${{secrets.LOGZIO_TOKEN}}"
    LP_AWS_ACCOUNT: "${{secrets.AWS_ACCOUNT_ID}}"
    API_LAMBDA_PACKAGE_LOCATION: ./api-${{ github.run_id }}.zip
   steps:
    - uses: actions/checkout@v4.1.1
      with:
          fetch-depth: 0
          
    - run: apt-get update
    - run: apt-get -y install zip
    - run: dotnet tool restore
    - run: dotnet lambda package -pl  src/BlueTape.Services.OnBoardingService.API/ -o ./scripts/deploy/api-${{ github.run_id }}.zip
    - name: Use latest Node.js
      uses: actions/setup-node@v3.8.1
      with:
        node-version: latest

    - run: npm config set prefix /usr/local
    - run: npm i -g serverless

    - name: Setup serverless-dotenv-plugin
      run: serverless plugin install -n serverless-dotenv-plugin
      working-directory: ./scripts/deploy

    - name: Setup serverless-offline
      run: serverless plugin install -n serverless-offline
      working-directory: ./scripts/deploy

    - name: Setup serverless-plugin-warmup
      run: serverless plugin install -n serverless-plugin-warmup
      working-directory: ./scripts/deploy
      
    - name: Setup serverless-prune-plugin
      run: serverless plugin install -n serverless-prune-plugin
      working-directory: ./scripts/deploy
          
    - name: Setup serverless-domain-manager
      run: serverless plugin install -n serverless-domain-manager
      working-directory: ./scripts/deploy
      
    - name: Deploy
      run: serverless deploy --stage $STAGE --verbose
      working-directory: ./scripts/deploy
      