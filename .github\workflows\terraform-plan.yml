name: 'Terraform Plan'

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: Select the environment
        required: true

permissions:
  contents: read

jobs:
  terraform-plan:
    name: 'Terraform Plan ${{ github.event.inputs.environment }}'
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    env:
      ARM_CLIENT_ID: "${{ secrets.AZURE_CLIENT_ID }}"
      ARM_SUBSCRIPTION_ID: "${{ secrets.AZURE_SUBSCRIPTION_ID }}"
      ARM_TENANT_ID: "${{ secrets.AZURE_TENANT_ID }}"
      ARM_CLIENT_SECRET: "${{ secrets.AZURE_CLIENT_SECRET }}"
      ACR_PASSWORD: "${{ secrets.AZURE_ACR_PASSWORD }}"
      AWS_ACCESS_KEY_ID: "${{ secrets.AWS_ACCESS_KEY_ID_DEV }}"
      AWS_SECRET_ACCESS_KEY: "${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}"
      LP_AWS_ACCOUNT: "${{ secrets.AWS_ACCOUNT_ID }}"
      ENV_NAME: "${{ vars.STAGE }}"

    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Azure Login
        run: az login --service-principal -u $ARM_CLIENT_ID -p $ARM_CLIENT_SECRET --tenant $ARM_TENANT_ID

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Terraform Init
        run: |
          cd infra/environments/${ENV_NAME}
          terraform init 

      - name: Terraform Validate
        run: |
          cd infra/environments/${ENV_NAME}
          terraform validate

      - name: Terraform Plan
        run: |
          cd infra/environments/${ENV_NAME}
          terraform plan -var="acr_password=$ACR_PASSWORD" -var="client_id=$ARM_CLIENT_ID" -var="tenant_id=$ARM_TENANT_ID" -var="client_secret=$ARM_CLIENT_SECRET" -var="aws_access_key_id=$AWS_ACCESS_KEY_ID" -var="aws_secret_access_key=$AWS_SECRET_ACCESS_KEY" -out=${ENV_NAME}.tfplan