﻿using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Models.Compatibility;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CreditApplicationSyncServiceTests
{
    private readonly CreditApplicationSyncService _creditApplicationSyncService;

    private readonly Mock<ILoanApplicationSyncMessageSender> _loanApplicationSyncMessageSenderMock;

    public CreditApplicationSyncServiceTests()
    {
        _loanApplicationSyncMessageSenderMock = new();
        _creditApplicationSyncService = new CreditApplicationSyncService(_loanApplicationSyncMessageSenderMock.Object);
    }

    [Theory]
    [InlineData(CreditApplicationType.GetPaid)]
    [InlineData(CreditApplicationType.LineOfCredit)]
    public async Task SyncApplicableCreditApplication_ApplicableTypes_SendsMessage(CreditApplicationType type)
    {
        var creditApplication = new CreditApplicationDocument()
        {
            Id = Guid.NewGuid().ToString(),
            Type = type.ToString()
        };

        await _creditApplicationSyncService.SyncApplicableCreditApplication(creditApplication, default);

        _loanApplicationSyncMessageSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
    }

    [Theory]
    [InlineData(CreditApplicationType.ARAdvance)]
    [InlineData(CreditApplicationType.InHouseCredit)]
    public async Task SyncApplicableCreditApplication_NonApplicableTypes_DoesNotSendMessage(CreditApplicationType type)
    {
        var creditApplication = new CreditApplicationDocument()
        {
            Id = Guid.NewGuid().ToString(),
            Type = type.ToString()
        };

        await _creditApplicationSyncService.SyncApplicableCreditApplication(creditApplication, default);

        _loanApplicationSyncMessageSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Never);
    }
}
