﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Constants;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using BlueTape.Utilities.Extensions;
using TinyHelpers.Extensions;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class CriScorer : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        var ar1r1RiskIndicatorManualReview = LexisNexisConstants.ListAr1RiskIndicatorManualReview;

        var result = new List<OwnerScore>();

        if (kyb?.KYC != null && kyb.KYC.Any())
        {
            foreach (var item in kyb.KYC)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                ownerScore.Scores?.Add(Calculate("CRI", item.CRI!, ar1r1RiskIndicatorManualReview));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = Calculate("CRI", kyb?.CRI!, ar1r1RiskIndicatorManualReview);
        return [new OwnerScore { Scores = [score] }];
    }

    private static Score Calculate(string name, IEnumerable<string> cri, IEnumerable<string> companyRiskIndicatorManualReview)
    {
        bool containsAnyRisks = companyRiskIndicatorManualReview.Any(x => cri != null && cri.Contains(x));
        var pass = cri.IsNullOrEmpty() || containsAnyRisks ? ScoringResult.Review : ScoringResult.Pass;

        return new Score()
        {
            Name = name,
            Value = cri == null ? null : string.Join(" ", cri),
            Pass = pass
        };
    }
}
