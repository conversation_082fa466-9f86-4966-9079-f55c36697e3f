﻿using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Cipher;
using Newtonsoft.Json;

namespace BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;

public class CoOwnerModel : AddressModel
{
    [JsonProperty("id")]
    public string Id { get; set; } = null!;

    [JsonProperty("identifier")]
    public string? Identifier { get; set; }

    [JsonProperty("phone")]
    public string Phone { get; set; } = null!;

    [JsonProperty("firstName")]
    public string FirstName { get; set; } = null!;

    [JsonProperty("lastName")]
    public string LastName { get; set; } = null!;

    [JsonProperty("email")]
    public string Email { get; set; } = null!;

    [JsonProperty("birthday")]
    public string BirthDate { get; set; } = null!;

    [JsonIgnore]
    public CipherModel? Ssn { get; set; }

    public string Type { get; set; } = null!;

    public int PercentOwned { get; set; }

    public bool? IsPrincipal { get; set; }

    [JsonProperty("entityName")]
    public string? EntityName { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonIgnore]
    public CipherModel? Ein { get; set; }

}
