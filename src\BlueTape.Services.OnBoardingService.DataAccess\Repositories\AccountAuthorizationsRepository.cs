﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Pipelines;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class AccountAuthorizationsRepository(ILogger<AccountAuthorizationsRepository> logger, IObsMongoDBContext context) : IAccountAuthorizationsRepository
{
    private readonly ILogger _logger = logger;
    private readonly IMongoCollection<AccountAuthorizationDocument> _collection = context.GetCollection<AccountAuthorizationDocument>();

    public async Task<AccountAuthorizationDocument> Add(AccountAuthorizationDocument entity, CancellationToken ct)
    {
        _logger.LogInformation("Add new account authorization document");

        entity.CreatedAt = DateTime.UtcNow;

        await _collection.InsertOneAsync(entity, cancellationToken: ct);
        return entity;
    }
    public async Task<IEnumerable<AccountAuthorizationDocument>> AddRange(IEnumerable<AccountAuthorizationDocument> documents, CancellationToken ct)
    {
        _logger.LogInformation("Started adding new account authorization documents list");

        foreach (var document in documents)
        {
            document.CreatedAt ??= DateTime.UtcNow;
        }

        await _collection.InsertManyAsync(documents, cancellationToken: ct);

        _logger.LogInformation("Finished adding new account authorization documents list");

        return documents;
    }

    public async Task<IEnumerable<AccountAuthorizationDocument>> GetAll(CancellationToken ct)
    {
        _logger.LogInformation("Get all account authorizations documents");
        var pipeline = new EmptyPipelineDefinition<AccountAuthorizationDocument>()
            .AppendStage(AccountAuthorizationPipelines.LookupCreditApplications)
            .AppendStage(AccountAuthorizationPipelines.AddCreditApplicationsIds)
            .AppendStage(AccountAuthorizationPipelines.ProjectMatchingApplications);

        using var aggregationCursor = await _collection.AggregateAsync(pipeline, cancellationToken: ct);
        return await aggregationCursor.ToListAsync(ct);
    }

    public async Task<IEnumerable<AccountAuthorizationDocument>> GetAll(Expression<Func<AccountAuthorizationDocument, bool>> predicate, CancellationToken ct)
    {
        _logger.LogInformation("Get by expression account authorizations documents");
        return await _collection.Find(predicate).ToListAsync(ct);
    }

    public async Task<IEnumerable<AccountAuthorizationDocument>> GetAllByFilters(string? id, string? companyId, string? einHash, string? ssnHash, CancellationToken ct)
    {
        _logger.LogInformation("Get account authorizations by filters: {id}, {companyId}, {einHas}", id, companyId, einHash);

        var filterDefinition = Builders<AccountAuthorizationDocument>.Filter.Empty;

        if (id is not null) filterDefinition &= Builders<AccountAuthorizationDocument>.Filter.Eq(x => x.Id, id);
        if (companyId is not null) filterDefinition &= Builders<AccountAuthorizationDocument>.Filter.Eq(x => x.CompanyId, companyId);
        if (einHash is not null) filterDefinition &= Builders<AccountAuthorizationDocument>.Filter.Eq(x => x.EinHash, einHash);
        if (ssnHash is not null) filterDefinition &= Builders<AccountAuthorizationDocument>.Filter.ElemMatch(
            x => x.OwnersDetails,
            ownerDetail => ownerDetail.SsnHash == ssnHash
        );

        var pipeline = new EmptyPipelineDefinition<AccountAuthorizationDocument>()
            .Match(filterDefinition)
            .AppendStage(AccountAuthorizationPipelines.LookupCreditApplications)
            .AppendStage(AccountAuthorizationPipelines.AddCreditApplicationsIds)
            .AppendStage(AccountAuthorizationPipelines.ProjectMatchingApplications);

        using var creditApplicationsQuery = await _collection.AggregateAsync(pipeline, cancellationToken: ct);
        return await creditApplicationsQuery.ToListAsync(ct);
    }

    public async Task<AccountAuthorizationDocument> GetById(string id, CancellationToken ct)
    {
        _logger.LogInformation("Get by id account authorizations documents, id: {id}", id);

        var filterDefinition = Builders<AccountAuthorizationDocument>.Filter.Eq(x => x.Id, id);

        var pipeline = new EmptyPipelineDefinition<AccountAuthorizationDocument>()
            .Match(filterDefinition)
            .AppendStage(AccountAuthorizationPipelines.LookupCreditApplications)
            .AppendStage(AccountAuthorizationPipelines.AddCreditApplicationsIds)
            .AppendStage(AccountAuthorizationPipelines.ProjectMatchingApplications);

        using var creditApplicationsQuery = await _collection.AggregateAsync(pipeline, cancellationToken: ct);

        var document = await creditApplicationsQuery.FirstOrDefaultAsync(cancellationToken: ct);
        if (document is not null) return document;

        _logger.LogError("Account authorizations with {id} not found", id);
        throw new VariableNullException(nameof(AccountAuthorizationDocument));
    }

    public async Task<AccountAuthorizationDocument> Update(AccountAuthorizationDocument entity, CancellationToken ct)
    {
        _logger.LogInformation("Update account authorization document, id: {id}", entity.Id);

        entity.UpdatedAt = DateTime.UtcNow;

        await _collection.ReplaceOneAsync(x => x.Id == entity.Id, entity, cancellationToken: ct);
        return entity;
    }

    public async Task<IEnumerable<AccountAuthorizationDocument>?> GetByEinHashes(string[] einHashes, CancellationToken ct)
    {
        var filterDefinition = Builders<AccountAuthorizationDocument>.Filter.In(x => x.EinHash, einHashes);

        var pipeline = new EmptyPipelineDefinition<AccountAuthorizationDocument>()
            .Match(filterDefinition)
            .AppendStage(AccountAuthorizationPipelines.LookupCreditApplications)
            .AppendStage(AccountAuthorizationPipelines.AddCreditApplicationsIds)
            .AppendStage(AccountAuthorizationPipelines.ProjectMatchingApplications);

        using var creditApplicationsQuery = await _collection.AggregateAsync(pipeline, cancellationToken: ct);

        return await creditApplicationsQuery.ToListAsync(ct);
    }

    public async Task<IEnumerable<AccountAuthorizationDocument>?> GetBySsnHashes(string[] ssnHashes, CancellationToken ct)
    {
        var filterDefinition = Builders<AccountAuthorizationDocument>.Filter.ElemMatch(
            x => x.OwnersDetails,
            ownerDetail => ssnHashes.Contains(ownerDetail.SsnHash)
        );
        var pipeline = new EmptyPipelineDefinition<AccountAuthorizationDocument>()
            .Match(filterDefinition)
            .AppendStage(AccountAuthorizationPipelines.LookupCreditApplications)
            .AppendStage(AccountAuthorizationPipelines.AddCreditApplicationsIds)
            .AppendStage(AccountAuthorizationPipelines.ProjectMatchingApplications);

        using var creditApplicationsQuery = await _collection.AggregateAsync(pipeline, cancellationToken: ct);

        return await creditApplicationsQuery.ToListAsync(ct);
    }
}