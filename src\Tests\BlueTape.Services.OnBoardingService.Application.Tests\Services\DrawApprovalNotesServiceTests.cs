using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApprovalNotes;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class DrawApprovalNotesServiceTests
{
    private readonly DrawApprovalNotesService _notesService;
    private readonly Mapper _mapper;
    
    private readonly Mock<IDrawApprovalNotesRepository> _noteRepository = new();
    private readonly Mock<ILogger<DrawApprovalNotesService>> _loggerMock = new();

    public DrawApprovalNotesServiceTests()
    {
        _mapper = new Mapper(new MapperConfiguration(cfg => cfg.AddProfile(new ModelsProfile())));
        _notesService = new DrawApprovalNotesService(_noteRepository.Object, _mapper, _loggerMock.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetByDrawApprovalId_ValidRequest_ShouldGetList(string id, List<DrawApprovalNoteDocument> documents)
    {
        _noteRepository.Setup(x => x.GetByDrawApprovalId(id, default)).ReturnsAsync(documents);
        var result = (await _notesService.GetByDrawApprovalId(id, default)).ToList();

        _noteRepository.Verify(x => x.GetByDrawApprovalId(id, default), Times.Once);
        result.ShouldBeUnique();
        result.ShouldAllBe(res => documents.Any(x => x.Id == res.Id));

        _noteRepository.Verify(x => x.GetByDrawApprovalId(id, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Add_ShouldCreateNote(CreateDrawApprovalNote createNote)
    {
        var document = _mapper.Map<DrawApprovalNoteDocument>(createNote);
        _noteRepository.Setup(x => x.Add(It.Is<DrawApprovalNoteDocument>(
            x => x.CreatedBy == createNote.CreatedBy
                 && x.DrawApprovalId == createNote.DrawApprovalId
                 && x.CreatedAt == createNote.CreatedAt
                 && x.Note == createNote.Note), default)).ReturnsAsync(document);

        var result = await _notesService.Add(createNote, default);

        result.ShouldNotBeNull();
        result.DrawApprovalId.ShouldBe(createNote.DrawApprovalId);
        result.Note.ShouldBe(createNote.Note);
        result.CreatedBy.ShouldBe(createNote.CreatedBy);

        _noteRepository.Verify(x => x.Add(It.IsAny<DrawApprovalNoteDocument>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Delete_ValidRequest_ShouldSetupDeletedAtAndDeletedBy(DeleteDrawApprovalNote deleteData)
    {
        _noteRepository.Setup(x => x.SoftDelete(deleteData.Id, deleteData.UserId, default));
        await _notesService.Delete(deleteData, default);
        _noteRepository.Verify(x => x.SoftDelete(deleteData.Id, deleteData.UserId, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Patch_ValidRequest_ShouldPatch(PatchDrawApprovalNote patchModel, DrawApprovalNoteDocument document)
    {
        _noteRepository.Setup(x => x.GetById(patchModel.Id, default)).ReturnsAsync(document);
        _noteRepository.Setup(x => x.Update(It.Is<DrawApprovalNoteDocument>(x =>
            x.DrawApprovalId == document.DrawApprovalId &&
            x.Note == patchModel.Note &&
            x.CreatedBy == document.CreatedBy &&
            x.CreatedAt == document.CreatedAt &&
            x.DeletedAt == document.DeletedAt &&
            x.DeletedBy == document.DeletedBy &&
            x.Id == document.Id), default)).ReturnsAsync(document);
        await _notesService.Patch(patchModel, default);
        _noteRepository.Verify(x => x.GetById(patchModel.Id, default), Times.Once);
        _noteRepository.Verify(x => x.Update(It.IsAny<DrawApprovalNoteDocument>(), default), Times.Once);
    }
}