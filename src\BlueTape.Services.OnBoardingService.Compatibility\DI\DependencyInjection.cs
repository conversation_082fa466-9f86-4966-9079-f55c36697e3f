﻿using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Services.AWSMessaging.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Models;
using BlueTape.Services.OnBoardingService.Compatibility.Models.ConnectorNotifications;
using BlueTape.Services.OnBoardingService.Compatibility.Services;
using BlueTape.Services.OnBoardingService.Compatibility.Services.Bluetape;
using BlueTape.Services.OnBoardingService.Compatibility.Services.Decision;
using BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout;
using BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;
using BlueTape.Services.OnBoardingService.Compatibility.Services.Notifications;
using BlueTape.Services.OnBoardingService.Compatibility.Services.Process;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace BlueTape.Services.OnBoardingService.Compatibility.DI;

public static class DependencyInjection
{
    public static void UseCompatibilityService(this IServiceCollection services, IConfiguration config)
    {
        services.AddMemoryCache();

        services.AddScoped<IOutputsManagerService, OutputsManagerService>();
        services.AddScoped<ICompatibilityService, CompatibilityService>();
        services.AddScoped<IDecisionService, DecisionService>();
        services.AddScoped<IKnockoutService, KnockoutService>();
        services.AddScoped<IBluetapeService, BluetapeService>();
        services.AddScoped<IManualDataService, ManualDataService>();
        services.AddScoped<IPlaidDataService, PlaidDataService>();

        var objectSerializer = new ObjectSerializer(ObjectSerializer.AllAllowedTypes);
        BsonSerializer.RegisterSerializer(objectSerializer);

        services.AddScoped<FraudPointScorer>();
        services.AddScoped<EmailAgeScorer>();
        services.AddScoped<BviScorer>();
        services.AddScoped<BriScorer>();
        services.AddScoped<CviScorer>();
        services.AddScoped<CriScorer>();
        services.AddScoped<B2EScorer>();
        services.AddScoped<ReliabilityScorer>();
        services.AddScoped<CompanyBankruptcyScorer>();
        services.AddScoped<JudgmentsScorer>();
        services.AddScoped<LiensScorer>();
        services.AddScoped<FicoScorer>();
        services.AddScoped<PersonalBankruptcyScorer>();
        services.AddScoped<SixtyPlusDebtScorer>();
        services.AddScoped<YearsInBusinessScorer>();

        services.AddScoped<IKeyVaultService, KeyVaultService>();

        services.Configure<SlackNotificationOptions>(config.GetSection(SlackNotificationOptions.SectionName));

        services.Configure<ConnectorQueueOptions>(config.GetSection(nameof(ConnectorQueueOptions)));
        services.AddSqsEndpoint<ConnectorMessageBody, ConnectorQueueOptions>(options => options.TopicName);
        services.AddTransient<IConnectorNotificationService, ConnectorNotificationService>();
        
        services.AddScoped<IDictionary<string, IScoring>>(sp =>
            new Dictionary<string, IScoring>
            {
                { "FraudPointScorer", sp.GetRequiredService<FraudPointScorer>() },
                { "EmailAgeScorer", sp.GetRequiredService<EmailAgeScorer>() },
                { "BviScorer", sp.GetRequiredService<BviScorer>() },
                { "BriScorer", sp.GetRequiredService<BriScorer>() },
                { "CviScorer", sp.GetRequiredService<CviScorer>() },
                { "CriScorer", sp.GetRequiredService<CriScorer>() },
                { "B2EScorer", sp.GetRequiredService<B2EScorer>() },
                { "ReliabilityScorer", sp.GetRequiredService<ReliabilityScorer>() },
                { "CompanyBankruptcyScorer", sp.GetRequiredService<CompanyBankruptcyScorer>() },
                { "JudgmentsScorer", sp.GetRequiredService<JudgmentsScorer>() },
                { "LiensScorer", sp.GetRequiredService<LiensScorer>() },
                { "FicoScorer", sp.GetRequiredService<FicoScorer>() },
                { "PersonalBankruptcyScorer", sp.GetRequiredService<PersonalBankruptcyScorer>() },
                { "SixtyPlusDebtScorer", sp.GetRequiredService<SixtyPlusDebtScorer>() },
                { "YearsInBusinessScorerV", sp.GetRequiredService<YearsInBusinessScorer>() },
            });
    }
}