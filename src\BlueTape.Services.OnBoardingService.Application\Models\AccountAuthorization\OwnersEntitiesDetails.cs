﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
public class OwnersEntitiesDetails : OwnerDetailsBase
{

    public string AuthRepLastName = string.Empty;

    public string AuthRepFirstName = string.Empty;

    public string EinHash = string.Empty;

    public string EntityName = string.Empty;

    public DateOnly? LastEINRejectionDate { get; set; }

    public IEnumerable<string?> BRICodes { get; set; } = Enumerable.Empty<string?>();

    public string? BVI { get; set; }

    public decimal? BusinessOutstandingBalance { get; set; }

    public decimal? DBT60PlusAmount { get; set; }

    public decimal? DBT60PlusPercentage { get; set; }

    public DateOnly? FirstReportedTradeLineDate { get; set; }

    public bool? JudgmentIndicator { get; set; }

    public DateOnly? LastJudgmentDate { get; set; }

    public decimal? JudgmentBalance { get; set; }

    public bool? BankruptcyIndicator { get; set; }

    public DateOnly? LastBankruptcyDate { get; set; }

    public bool? LienIndicator { get; set; }

    public DateOnly? LastLienDate { get; set; }

    public decimal? LienBalance { get; set; }

    public decimal? TradeLinesPercentage { get; set; }

    public decimal? TotalTradeLines { get; set; }

    public string? ReliabilityCode { get; set; }

}