namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;

public class DecisionEngineStepsQueryModel
{
    public string? CompanyId { get; set; }
    public string[]? CreditApplicationIds { get; set; }
    public string[]? CreditApplicationIdsForCompanyFilter { get; set; }
    public string[]? DrawApprovalIds { get; set; }
    public string[]? ExecutionIds { get; set; }
    public string[]? AccountAuthorizationDetailIds { get; set; }
    public string[]? AccountAuthorizationDetailIdsForCompanyFilter { get; set; }
    public string[]? ExecutionTypes { get; set; }
    public string[]? StepNames { get; set; }
    public string[]? Statuses { get; set; }
}