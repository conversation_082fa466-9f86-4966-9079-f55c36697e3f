﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class ReviewDrawApprovalModel
{
    public string Id { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
    public AdminDrawApprovalStatusUpdate NewStatus { get; set; }
    public DebtInvestorType? DebtInvestor { get; set; }
    public decimal? ApprovedCreditLimit { get; set; }
    public string? Code { get; set; }
    public string? Note { get; set; }
}
