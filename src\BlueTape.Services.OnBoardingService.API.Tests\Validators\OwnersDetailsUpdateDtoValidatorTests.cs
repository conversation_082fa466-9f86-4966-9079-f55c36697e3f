﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.UpdateAccountAuthorization;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.API.Tests.Validators;
public class OwnersDetailsUpdateDtoValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new OwnersDetailsUpdateDtoValidator();

        var model = new OwnersDetailsUpdateDto()
        {
            Identifier = "string",
            PercentOwned = 10,
            Birthday = new DateOnly(2023, 10, 3),
            SsnHash = "string",
            FirstName = "string",
            LastName = "string",
            Address = new AddressDto()
            {
                Address = "string",
                City = "string",
                State = "string",
                UnitNumber = "string",
                Zip = "string"
            },
            IsPrincipal = true
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }
    [Fact]
    public void Validate_InvalidModel_ReturnsFalse()
    {
        var validator = new OwnersDetailsUpdateDtoValidator();

        var model = new OwnersDetailsUpdateDto()
        {
            Identifier = "string",
            PercentOwned = 10,
            SsnHash = "string",
            FirstName = "string",
            LastName = "string",
            Address = new AddressDto()
            {
                Address = "string",
                City = "string",
                State = "string",
                UnitNumber = "string",
                Zip = "string"
            },
            IsPrincipal = true
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }
}
