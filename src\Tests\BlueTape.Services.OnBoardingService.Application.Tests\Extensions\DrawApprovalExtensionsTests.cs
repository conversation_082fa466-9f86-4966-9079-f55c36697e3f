using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Extensions;

public class DrawApprovalExtensionsTests
{
    private const string PaymentPlanId = "paymentPlanId"; 
    
    [Theory]
    [InlineData(LoanOrigin.Express, "express")]
    [InlineData(LoanOrigin.Factoring, "factoring")]
    [InlineData(LoanOrigin.Quote, "quote")]
    [InlineData(LoanOrigin.Normal, PaymentPlanId)]
    public void GetDrawApprovalType_ValidData_ShouldGetType(LoanOrigin type, string expectedType)
    {
        var result = DrawApprovalExtensions.GetDrawApprovalType(type, PaymentPlanId);
        result.ShouldBe(expectedType);
    }
}