﻿using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.Credit;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;
using CompanyDebtInvestorType = BlueTape.CompanyService.Common.Enums.DebtInvestorType;
using DebtInvestorType = BlueTape.OBS.Enums.DebtInvestorType;
using UpdateCompanyModel = BlueTape.CompanyService.Companies.UpdateCompanyModel;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Strategies.CreditApplicationStatusChange;

public class ApproveCreditApplicationStrategyTests
{
    private readonly ApproveCreditApplicationStrategy _strategy;

    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IAccountStatusService> _accountStatusServiceMock = new();
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock = new();
    private readonly Mock<ILoanService> _loanServiceMock = new();
    private readonly Mock<ICreditApplicationSyncService> _creditApplicationSyncServiceMock = new();
    private readonly Mock<ICompanyExternalService> _companyExternalServiceMock = new();
    private readonly Mock<ICreditApplicationNotesService> _creditApplicationNotesServiceMock = new();

    public ApproveCreditApplicationStrategyTests()
    {
        _dateProviderMock = new();
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        var mapper = new Mapper(mapperConfig);
        Mock<ILogger<ApproveCreditApplicationStrategy>> loggerMock = new();

        _strategy = new ApproveCreditApplicationStrategy(
            _dateProviderMock.Object,
            mapper,
            _accountStatusServiceMock.Object,
            _creditApplicationRepositoryMock.Object,
            _loanServiceMock.Object,
            _creditApplicationSyncServiceMock.Object,
            _companyExternalServiceMock.Object,
            _creditApplicationNotesServiceMock.Object,
            loggerMock.Object);
    }

    [Fact]
    public void IsApplicable_ApprovedStatus_ReturnsTrue()
    {
        var result = _strategy.IsApplicable(CreditApplicationStatus.Approved.ToString());

        result.ShouldBe(true);
    }

    [Fact]
    public void IsApplicable_NotApprovedStatus_ReturnsFalse()
    {
        var result = _strategy.IsApplicable(CreditApplicationStatus.Processed.ToString());

        result.ShouldBe(false);
    }

    [Theory, CustomAutoData]
    public async Task Review_Approve_ShouldThrowValidationError_WhenCreditLimitIsTooSmall(CreditApplicationDocument creditApplication)
    {
        creditApplication.Status = CreditApplicationStatus.Processed.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString(), ApprovedCreditLimit = -1 };

        var act = () => _strategy.ChangeStatus(creditApplication, dto, "userId", default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("approved credit limit");
    }

    [Theory, CustomAutoData]
    public async Task Review_Approve_ShouldThrowValidationError_WhenRevenueFallPercentageIsTooSmall(CreditApplicationDocument creditApplication)
    {
        creditApplication.Status = CreditApplicationStatus.Processed.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);

        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString(), ApprovedCreditLimit = 100, RevenueFallPercentage = -1 };

        var act = () => _strategy.ChangeStatus(creditApplication, dto, "userId", default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("revenue fall percentage");
    }

    [Theory, CustomAutoData]
    public async Task Review_Approve_ShouldThrowValidationError_WhenRevenueFallPercentageIsTooLarge(CreditApplicationDocument creditApplication)
    {
        creditApplication.Status = CreditApplicationStatus.Processed.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);

        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString(), ApprovedCreditLimit = 101, RevenueFallPercentage = -1 };

        var act = () => _strategy.ChangeStatus(creditApplication, dto, "userId", default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("revenue fall percentage");
    }

    [Theory, CustomAutoData]
    public async Task Review_Approve_ShouldThrowValidationError_WhenPurchaseTypeIsNotProvided(CreditApplicationDocument creditApplication)
    {
        creditApplication.Type = CreditApplicationType.LineOfCredit.ToString();
        creditApplication.Status = CreditApplicationStatus.Processed.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);

        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString(), ApprovedCreditLimit = 101, RevenueFallPercentage = 10 };

        var act = () => _strategy.ChangeStatus(creditApplication, dto, "userId", default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("purchase type");
    }

    [Theory, CustomAutoData]
    public async Task Review_Approve_ShouldSaveAndCreateCredit(CreditApplicationDocument creditApplication)
    {
        var now = DateTime.UtcNow;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);

        creditApplication.Status = CreditApplicationStatus.Approved.ToString();
        _creditApplicationRepositoryMock
            .Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _creditApplicationRepositoryMock
            .Setup(x => x.Update(creditApplication, It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplication);

        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString(), ApprovedCreditLimit = 101, RevenueFallPercentage = 10, PurchaseType = PurchaseTypeOption.Inventory.ToString() };

        var res = await _strategy.ChangeStatus(creditApplication, dto, "userId", default);
        res.ShouldNotBeNull();

        res.Status.ShouldBe(CreditApplicationStatus.Approved.ToString().ToLower());

        res.ApprovedCreditLimit.ShouldBe(dto.ApprovedCreditLimit.Value);
        res.RevenueFallPercentage.ShouldBe(dto.RevenueFallPercentage.Value);
        res.PurchaseTypeOption.ShouldBe(dto.PurchaseType);

        res.ApprovedBy.ShouldBe("userId");
        res.ApprovedAt.ShouldBe(now);
    }

    [Theory, CustomAutoData]
    public async Task Review_ApproveGetPaid_ShouldChangeStatus(CreditApplicationDocument creditApplication)
    {
        creditApplication.Type = CreditApplicationType.GetPaid.ToString();
        var now = DateTime.UtcNow;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);

        creditApplication.Status = CreditApplicationStatus.Approved.ToString();
        _creditApplicationRepositoryMock
            .Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _creditApplicationRepositoryMock
            .Setup(x => x.Update(creditApplication, It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplication);

        var dto = new ReviewCreditApplicationDto
        {
            NewStatus = CreditApplicationStatus.Approved.ToString(),
            ApprovedCreditLimit = 101,
            RevenueFallPercentage = 10,
            IsAchDelay = true,
            CardPricingPackageId = "card_id",
            LoanPricingPackageId = "loan_id",
            PurchaseType = PurchaseTypeOption.Inventory.ToString(),
            Note = "note"
        };
        var res = await _strategy.ChangeStatus(creditApplication, dto, "userId", default);
        res.ShouldNotBeNull();

        res.Status.ShouldBe(CreditApplicationStatus.Approved.ToString().ToLower());

        res.ApprovedCreditLimit.ShouldBeNull();
        res.RevenueFallPercentage.ShouldBeNull();
        res.PurchaseTypeOption.ShouldBeNull();
        res.MerchantSettings?.IsAchDelay.ShouldBe(dto.IsAchDelay);
        res.MerchantSettings?.CardPricingPackageId.ShouldBe(dto.CardPricingPackageId);
        res.MerchantSettings?.LoanPricingPackageId.ShouldBe(dto.LoanPricingPackageId);
        res.ApprovedBy.ShouldBe("userId");
        res.ApprovedAt.ShouldBe(now);

        _loanServiceMock.Verify(x => x.CreateCredit(It.IsAny<CreateCreditModel>(), It.IsAny<CancellationToken>()), Times.Never);
        _accountStatusServiceMock.Verify(x => x.ChangeAccountStatus(It.IsAny<CreditApplicationDocument>(), default), Times.Once);
        _creditApplicationSyncServiceMock.Verify(x => x.SyncApplicableCreditApplication(creditApplication, default), Times.Once);
        _creditApplicationNotesServiceMock.Verify(x => x.Add(It.IsAny<CreateCreditApplicationNote>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Review_GetPaidType_UpdatesCompanyDebtInvestorSettings(CreditApplicationDocument creditApplication)
    {
        // Arrange
        creditApplication.Type = CreditApplicationType.GetPaid.ToString();
        creditApplication.CompanyId = "testCompanyId";
        var userId = "testUserId";
        var debtInvestorValue = DebtInvestorType.Raistone;

        var dto = new ReviewCreditApplicationDto
        {
            NewStatus = CreditApplicationStatus.Approved.ToString(),
            DebtInvestor = debtInvestorValue,
            LoanPricingPackageId = "loan_id"
        };

        _creditApplicationRepositoryMock
            .Setup(x => x.Update(creditApplication, It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplication);

        // Act
        var result = await _strategy.ChangeStatus(creditApplication, dto, userId, default);

        // Assert
        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(
            creditApplication.CompanyId,
            userId,
            It.Is<UpdateCompanyModel>(m =>
                m.Settings != null &&
                m.Settings.DefaultDebtInvestorTradeCredit == (CompanyDebtInvestorType)debtInvestorValue),
            default),
            Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Review_ARAdvanceType_UpdatesCompanyDebtInvestorSettings(CreditApplicationDocument creditApplication)
    {
        // Arrange
        creditApplication.Type = CreditApplicationType.ARAdvance.ToString();
        creditApplication.CompanyId = "testCompanyId";
        var userId = "testUserId";
        var debtInvestorValue = DebtInvestorType.Raistone;

        var dto = new ReviewCreditApplicationDto
        {
            NewStatus = CreditApplicationStatus.Approved.ToString(),
            DebtInvestor = debtInvestorValue,
            LoanPricingPackageId = "loan_id",
            ApprovedCreditLimit = 100
        };

        _creditApplicationRepositoryMock
            .Setup(x => x.Update(creditApplication, It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplication);

        // Act
        var result = await _strategy.ChangeStatus(creditApplication, dto, userId, default);

        // Assert
        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(
            creditApplication.CompanyId,
            userId,
            It.Is<UpdateCompanyModel>(m =>
                m.Settings != null &&
                m.Settings.ARAdvance != null &&
                m.Settings.ARAdvance.DefaultDebtInvestor == (CompanyDebtInvestorType)debtInvestorValue),
            default),
            Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Review_LineOfCreditType_DoesNotUpdateCompanyDebtInvestorSettings(CreditApplicationDocument creditApplication)
    {
        // Arrange
        creditApplication.Type = CreditApplicationType.LineOfCredit.ToString();
        creditApplication.CompanyId = "testCompanyId";
        var userId = "testUserId";

        var dto = new ReviewCreditApplicationDto
        {
            NewStatus = CreditApplicationStatus.Approved.ToString(),
            DebtInvestor = DebtInvestorType.Raistone,
            ApprovedCreditLimit = 100,
            RevenueFallPercentage = 10,
            PurchaseType = PurchaseTypeOption.Inventory.ToString()
        };

        _creditApplicationRepositoryMock
            .Setup(x => x.Update(creditApplication, It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplication);

        // Act
        var result = await _strategy.ChangeStatus(creditApplication, dto, userId, default);

        // Assert
        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<UpdateCompanyModel>(),
            default),
            Times.Never);
    }
}
