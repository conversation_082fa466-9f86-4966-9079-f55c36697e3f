{
  "Serilog": {
    "Serilog": {
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "AWSSDK": "Warning",
          "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error",
          "BlueTape.Services.Utilities.AWS": "Warning",
          "Microsoft.AspNetCore": "Information",
          "Microsoft.AspNetCore.DataProtection": "Error",
          "System.Net.Http.HttpClient": "Information",
          "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"
        }
      }
    },
    "Seq": {
      "Address": "http://localhost:5341"
    }
  },
  "AllowedHosts": "*",
  "BlueTapeOptions": {
    "AwsSecretName": "bluetape_keys_dev"
  },
  "StepsReportOptions": {
    "BucketName": "dev.uw1.linqpal-temp-assets"
  },
  "NET_LOAN_SERVICE_API_URL": "https://localhost:7038",
  "SlackNotification": {
    "ErrorSnsTopicName": "obs-notifications-dev"
  },
  "AuthorizationDetailsDetectorMessagingOptions": {
    "ScheduledPeriodDurationBetweenMessagesInMinutes": 2,
    "MaxSimultaneouslySentMessagesCount": 2,
    "MaxMessagesInBatchCount": 300,
    "MaxCompaniesRefreshedCount": 150
  },
  "AuthorizationDetailsRefreshConfiguration": {
    "checks": [
      {
        "scheduledUpdate": "businessChecks",
        "frequencyInDays": 90,
        "creditApplicationTypes": [
          "loc",
          "ihc",
          "getpaid",
          "aradvance"
        ],
        "stepsIncluded": [
          "KYB",
          "KYC"
        ]
      },
      {
        "scheduledUpdate": "annualRevenueChecks",
        "frequencyInDays": 30,
        "creditApplicationTypes": [
          "loc",
          "aradvance"
        ],
        "stepsIncluded": [
          "CashFlow",
          "CashFlowReview"
        ]
      },
      {
        "scheduledUpdate": "creditRatingChecks",
        "frequencyInDays": 90,
        "creditApplicationTypes": [
          "loc",
          "aradvance"
        ],
        "stepsIncluded": [
          "BusinessCreditRating",
          "CoOwnersCreditRating"
        ]
      },
      {
        "scheduledUpdate": "inactivityChecks",
        "frequencyInDays": 1,
        "creditApplicationTypes": [
          "loc",
          "ihc",
          "aradvance"
        ],
        "stepsIncluded": [
          "CreditReview" // NOT EXISTS YET
        ]
      }
    ]
  }
}