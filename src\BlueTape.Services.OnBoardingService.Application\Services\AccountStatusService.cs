﻿using BlueTape.CompanyService.Common.Functions.AccountStatus;
using BlueTape.CompanyService.Common.Functions.AccountStatus.Constants.AccountStatusChangeEvents;
using BlueTape.CompanyService.Common.Senders;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Utilities.Providers;

namespace BlueTape.Services.OnBoardingService.Application.Services;
public class AccountStatusService : IAccountStatusService
{
    private readonly IAccountStatusChangeQueueSender _accountStatusChangeQueueSender;
    private readonly IDateProvider _dateProvider;

    public AccountStatusService(IAccountStatusChangeQueueSender accountStatusChangeQueueSender, IDateProvider dateProvider)
    {
        _accountStatusChangeQueueSender = accountStatusChangeQueueSender;
        _dateProvider = dateProvider;
    }

    public async Task ChangeAccountStatus(CreditApplicationDocument creditApplication, CancellationToken ct)
    {
        var eventType = GetEventTypeByNewStatus(creditApplication.Status);
        if (eventType is null) return;

        var message = new ChangeAccountStatusModel()
        {
            CreatedAt = _dateProvider.CurrentDateTime,
            CreatedBy = "OnBoardingService",
            EventType = eventType,
            Details = new ChangeAccountStatusDetailsModel()
            {
                CompanyId = creditApplication.CompanyId!,
                Id = creditApplication.Id,
                Status = creditApplication.Status!
            }
        };

        await _accountStatusChangeQueueSender.SendMessage(new ServiceBusMessageBt<ChangeAccountStatusModel>(message), ct: ct);
    }

    private static string? GetEventTypeByNewStatus(string? status)
    {
        if (IsIncompleteEvent(status)) return IncompleteEvents.CreditApplicationIncomplete;
        if (IsUnderReviewEvent(status)) return UnderReviewEvents.CreditApplicationUnderReview;
        if (IsUnderStipulationEvent(status)) return UnderStipulationEvents.CreditApplicationUnderStipulation;
        if (IsApprovedEvent(status)) return GoodStandingEvents.CreditApplicationApproved;
        if (IsCanceledEvent(status)) return CanceledEvents.CreditApplicationCanceled;
        if (IsRejectedEvent(status)) return RejectedEvents.CreditApplicationRejected;
        return null;
    }

    private static bool IsIncompleteEvent(string? status) =>
        string.Equals(status, CreditApplicationStatus.New.ToString(), StringComparison.OrdinalIgnoreCase) ||
        string.Equals(status, CreditApplicationStatus.Processing.ToString(), StringComparison.OrdinalIgnoreCase);

    private static bool IsUnderReviewEvent(string? status) =>
        string.Equals(status, CreditApplicationStatus.Processed.ToString(), StringComparison.OrdinalIgnoreCase) ||
        string.Equals(status, CreditApplicationStatus.Review.ToString(), StringComparison.OrdinalIgnoreCase);

    private static bool IsUnderStipulationEvent(string? status) =>
        string.Equals(status, CreditApplicationStatus.SentBack.ToString(), StringComparison.OrdinalIgnoreCase);

    private static bool IsApprovedEvent(string? status) =>
        string.Equals(status, CreditApplicationStatus.Approved.ToString(), StringComparison.OrdinalIgnoreCase);

    private static bool IsCanceledEvent(string? status) =>
        string.Equals(status, CreditApplicationStatus.Canceled.ToString(), StringComparison.OrdinalIgnoreCase);

    private static bool IsRejectedEvent(string? status) =>
        string.Equals(status, CreditApplicationStatus.Rejected.ToString(), StringComparison.OrdinalIgnoreCase);
}
