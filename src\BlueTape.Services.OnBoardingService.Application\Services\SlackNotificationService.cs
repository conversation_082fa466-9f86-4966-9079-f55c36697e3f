﻿using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.SNS.SlackNotification;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Globalization;
using BlueTape.Services.OnBoardingService.Application.Models;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class SlackNotificationService(
    ILogger<SlackNotificationService> logger,
    ISnsEndpoint snsEndpoint,
    IOptions<SlackNotificationOptions> options,
    IConfiguration configuration)
    : ISlackNotificationService
{
    private const string ServiceName = "OnBoardingService";

    public Task Notify(string message, string eventName, EventLevel eventLevel, string traceId, CancellationToken ctx)
    {
        return Notify(new EventMessageBody
        {
            Message = message,
            EventLevel = eventLevel,
            EventName = eventName,
            EventSource = EnvironmentExtensions.GetExecutionEnvironment(),
            ServiceName = ServiceName,
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
        }, traceId, ctx);
    }

    public async Task Notify(EventMessageBody message, string traceId, CancellationToken ctx)
    {
        logger.LogInformation("Started building OnBoardingService notification message: {EventName}",
            message.EventName);

        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
        if (env is EnvironmentConstants.Development or EnvironmentConstants.Local)
        {
            logger.LogInformation("Notify: skip sending messages in environment: {Env}", env);
            return;
        }

        var correlationId = traceId;
        message.Message += $"\nBlueTapeCorrelationId: {correlationId}";
        var awsAccountId = configuration["AWS-ACCOUNT-ID"];
        var region = configuration["AWS-DEFAULT-REGION"];
        var topicName = options.Value.ErrorSnsTopicName;

        var topic = $"arn:aws:sns:{region}:{awsAccountId}:{topicName}";

        logger.LogInformation("Started publishing {ServiceName} message: {EventName} to SNS topic {TopicArn}",
            ServiceName, message.EventName, topic);

        await snsEndpoint.PublishSlackNotificationAsync(
            message,
            topic,
            $"{ServiceName} notification triggered",
            ctx);
    }
}