﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;

public class MerchantSettings
{
    public string? CardPricingPackageId { get; set; }
    public string? LoanPricingPackageId { get; set; }
    public string? CardPricingPackageName { get; set; }
    public string? LoanPricingPackageName { get; set; }
    public bool? IsAchDelay { get; set; }
    public DebtInvestorType? DebtInvestor { get; set; }
}
