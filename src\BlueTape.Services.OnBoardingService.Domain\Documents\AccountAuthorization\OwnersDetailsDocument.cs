﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;

public class OwnersDetailsDocument : OwnerDetailsBaseDocument
{

    [BsonElement("FraudpointScore")]
    public string? FraudpointScore { get; set; }

    [BsonElement("EmailRiskScore")]
    public string? EmailRiskScore { get; set; }

    [BsonElement("birthday")]
    public DateTime? Birthday { get; set; }

    [BsonElement("ssnHash")]
    public string SsnHash { get; set; } = string.Empty;

    [BsonElement("firstName")]
    public string FirstName { get; set; } = string.Empty;

    [BsonElement("lastName")]
    public string LastName { get; set; } = string.Empty;

    [BsonElement("B2ELinkIndex")]
    public string? B2ELinkIndex { get; set; }

    [BsonElement("LastSSNRejectionDate")]
    public DateTime? LastSSNRejectionDate { get; set; }

    [BsonElement("CRICodes")]
    public IEnumerable<string?> CRICodes { get; set; } = new List<string>();

    [BsonElement("CVI")]
    public string? CVI { get; set; }

    [BsonElement("FICOScore")]
    public string? FICOScore { get; set; }

    [BsonElement("LastPersonalBankruptcyDate")]
    public DateTime? LastPersonalBankruptcyDate { get; set; }

    [BsonElement("IPRiskLevel")]
    public string? IPRiskLevel { get; set; }

    [BsonElement("DomainRiskLevel")]
    public string? DomainRiskLevel { get; set; }

    [BsonElement("LastEINRejectionDate")]
    public DateOnly? LastEINRejectionDate { get; set; }
}