﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Draft;

[BsonIgnoreExtraElements]
public class ItemDocument
{
    [BsonElement("identifier")]
    public string? Identifier { get; set; }

    [BsonElement("title")]
    public string? Title { get; set; }

    [BsonElement("filled")]
    public bool? Filled { get; set; }

    [BsonElement("content")]
    public object? Content { get; set; }
}