namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;

public class DrawApprovalNote
{
    public string Id { get; set; } = string.Empty;
    public string DrawApprovalId { get; set; } = string.Empty;
    public string Note { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }
}