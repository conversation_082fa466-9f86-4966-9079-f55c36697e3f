﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Extensions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class BviScorer(IConfiguration config) : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        int passThreshold = config.GetValue<int?>("ScoringThresholds:BviThresholdPass") ?? 30;
        var result = new List<OwnerScore>();

        if (kyb?.KYB != null && kyb.KYB.Any())
        {
            foreach (var item in kyb.KYB)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                int.TryParse(item.BVI, out var bvi);

                ownerScore.Scores?.Add(KnockoutCalculationExtension.Calculate("BVI", bvi, passThreshold));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = KnockoutCalculationExtension.Calculate("BVI", kyb?.BVI, passThreshold);
        return [new OwnerScore { Scores = [score] }];
    }
}
