using AutoFixture;
using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CreditApplicationNotesServiceTests
{
    private const string AppId = "123";

    private readonly Mock<ICreditApplicationNotesRepository> _repository = new();
    private readonly Fixture _fixture = new();
    private readonly Mapper _mapper;

    private readonly CreditApplicationNotesService _service;

    public CreditApplicationNotesServiceTests()
    {
        _mapper = new Mapper(new MapperConfiguration(
            config => config.AddProfile(new ModelsProfile())
        ));

        _service = new CreditApplicationNotesService(_repository.Object, _mapper);
    }

    [Fact]
    public async Task GetByApplicationId_ReturnsNotes()
    {
        var notes = _fixture.CreateMany<CreditApplicationNoteDocument>(3).ToList();

        _repository
            .Setup(x => x.GetByApplicationId(AppId, default))
            .ReturnsAsync(notes);

        var result = (await _service.GetByApplicationId(AppId, default)).ToList();

        _repository.Verify(r => r.GetByApplicationId(AppId, default), Times.Once);

        result.ShouldBeUnique();
        result.ShouldAllBe(res => notes.Any(note => note.Id == res.Id));
    }

    [Fact]
    public async Task GetByApplicationId_ReturnsEmptyList_WhenNotFound()
    {
        var notes = Enumerable.Empty<CreditApplicationNoteDocument>().ToList();

        _repository
            .Setup(x => x.GetByApplicationId(AppId, default))
            .ReturnsAsync(notes);

        var result = (await _service.GetByApplicationId(AppId, default)).ToList();

        _repository.Verify(r => r.GetByApplicationId(AppId, default), Times.Once);

        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task Add_ShouldCreateNote()
    {
        var requestedNote = _fixture.Create<CreateCreditApplicationNote>();
        var noteDocument = _mapper.Map<CreditApplicationNoteDocument>(requestedNote);
        _repository
            .Setup(r => r.Add(It.IsAny<CreditApplicationNoteDocument>(), default))
            .ReturnsAsync(noteDocument);

        var result = await _service.Add(requestedNote, default);

        _repository.Verify(r => r.Add(It.Is<CreditApplicationNoteDocument>(x => x.IsSystemGenerated == false), default), Times.Once);

        result.ShouldNotBeNull();
        result.CreditApplicationId.ShouldBe(requestedNote.CreditApplicationId);
        result.Note.ShouldBe(requestedNote.Note);
        result.CreatedBy.ShouldBe(requestedNote.CreatedBy);
    }

    [Fact]
    public async Task AddRange_ShouldCreateNotes()
    {
        var requestedNotes = _fixture.Create<IEnumerable<CreateCreditApplicationNote>>();
        var noteDocuments = _mapper.Map<IEnumerable<CreditApplicationNoteDocument>>(requestedNotes);
        _repository
            .Setup(r => r.AddRange(It.IsAny<IEnumerable<CreditApplicationNoteDocument>>(), default))
            .ReturnsAsync(noteDocuments);

        var notes = await _service.AddRange(requestedNotes, default);

        _repository.Verify(r => r.AddRange(It.IsAny<IEnumerable<CreditApplicationNoteDocument>>(), default), Times.Once);

        notes.ShouldNotBeNull();

        foreach (var (note, requested) in notes.Zip(requestedNotes, (r, n) => (r, n)))
        {
            note.CreditApplicationId.ShouldBe(requested.CreditApplicationId);
            note.Note.ShouldBe(requested.Note);
            note.CreatedBy.ShouldBe(requested.CreatedBy);
        }
    }

    [Fact]
    public async Task AddRange_NotesAbsent_ShouldntCreateNotes()
    {
        var requestedNotes = new List<CreateCreditApplicationNote>();
        var noteDocuments = _mapper.Map<IEnumerable<CreditApplicationNoteDocument>>(requestedNotes);
        _repository
            .Setup(r => r.AddRange(It.IsAny<IEnumerable<CreditApplicationNoteDocument>>(), default))
            .ReturnsAsync(noteDocuments);

        var notes = await _service.AddRange(requestedNotes, default);

        _repository.Verify(r => r.AddRange(It.IsAny<IEnumerable<CreditApplicationNoteDocument>>(), default), Times.Never);

        notes.ShouldBeEmpty();
    }
  
    [Theory, CustomAutoData]
    public async Task Delete_ValidRequest_ShouldSetupDeletedAtAndDeletedBy(DeleteCreditApplicationNote deleteData, CreditApplicationNoteDocument note)
    {
        note.IsSystemGenerated = false;
        _repository.Setup(x => x.GetById(deleteData.Id, default)).ReturnsAsync(note);
        _repository.Setup(x => x.SoftDelete(deleteData.Id, deleteData.UserId, default));
        await _service.Delete(deleteData, default);
        _repository.Verify(x => x.SoftDelete(deleteData.Id, deleteData.UserId, default), Times.Once);
    }


    [Theory, CustomAutoData]
    public Task Delete_SystemGeneratedNote_ThrowsValidationException(DeleteCreditApplicationNote deleteData)
    {
        _repository.Setup(x => x.GetById(deleteData.Id, default)).ReturnsAsync(new CreditApplicationNoteDocument()
        {
            IsSystemGenerated = true
        });

        var act = async () => await _service.Delete(deleteData, default);

        return act.ShouldThrowAsync<ValidationException>();
    }


    [Theory, CustomAutoData]
    public async Task Patch_ValidRequest_ShouldPatch(PatchCreditApplicationNote patchModel, CreditApplicationNoteDocument document)
    {
        document.IsSystemGenerated = false;
        _repository.Setup(x => x.GetById(patchModel.Id, default)).ReturnsAsync(document);
        _repository.Setup(x => x.Update(It.Is<CreditApplicationNoteDocument>(x =>
            x.CreditApplicationId == document.CreditApplicationId &&
            x.Note == patchModel.Note &&
            x.CreatedBy == document.CreatedBy &&
            x.CreatedAt == document.CreatedAt &&
            x.DeletedAt == document.DeletedAt &&
            x.DeletedBy == document.DeletedBy &&
            x.Id == document.Id), default)).ReturnsAsync(document);
        await _service.Patch(patchModel, default);
        _repository.Verify(x => x.GetById(patchModel.Id, default), Times.Once);
        _repository.Verify(x => x.Update(It.IsAny<CreditApplicationNoteDocument>(), default), Times.Once);
    }


    [Theory, CustomAutoData]
    public Task Patch_SystemGeneratedNote_ThrowsValidationException(PatchCreditApplicationNote patchModel)
    {
        _repository.Setup(x => x.GetById(patchModel.Id, default)).ReturnsAsync(new CreditApplicationNoteDocument()
        {
            IsSystemGenerated = true
        });

        var act = async () => await _service.Patch(patchModel, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task AddSystemNote_CreditLimitChanged_ShouldCreateNote()
    {
        var requestedNote = _fixture.Create<CreateSystemCreditApplicationNote>();
        requestedNote.SystemNoteTemplate = CreditApplicationNotesTemplateType.CreditLimitManuallyChanged;
        var noteDocument = _mapper.Map<CreditApplicationNoteDocument>(requestedNote);

        _repository
            .Setup(r => r.Add(It.IsAny<CreditApplicationNoteDocument>(), default))
            .ReturnsAsync(noteDocument);
        var note = $"${requestedNote.NewValue}. (Original: ${requestedNote.OldValue})";
        var result = await _service.AddSystemNote(requestedNote, default);

        _repository.Verify(r => r.Add(It.Is<CreditApplicationNoteDocument>(x =>
            x.Note!.Contains(note) && x.IsSystemGenerated == true), default), Times.Once);

        result.ShouldNotBeNull();
        result.CreditApplicationId.ShouldBe(requestedNote.CreditApplicationId);
        result.CreatedBy.ShouldBe(requestedNote.CreatedBy);
    }
}
