﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.PaymentPlan;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class LoanPaymentPlanRepository : GenericRepository<LoanPaymentPlanDocument>, ILoanPaymentPlanRepository
{
    public LoanPaymentPlanRepository(IObsMongoDBContext context, ILogger<GenericRepository<LoanPaymentPlanDocument>> logger) : base(context, logger)
    {
    }
}