﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.CustomerAccount;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class InvoiceRepository(IObsMongoDBContext context) : IInvoiceRepository
{
    private readonly IMongoCollection<InvoiceDocument> _collection = context.GetCollection<InvoiceDocument>();

    public Task<InvoiceDocument> GetById(string id, CancellationToken cancellationToken)
    {
        var result = _collection.Find(filter: x => x.Id == id);
        return result.FirstOrDefaultAsync(cancellationToken);
    }
}
