﻿using AutoFixture;
using AutoFixture.Xunit2;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Credit;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using MongoDB.Bson;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Attributes;

public class CustomAutoDataAttribute : AutoDataAttribute
{
    public CustomAutoDataAttribute() : base(() =>
    {
        var fixture = new Fixture();
        fixture.Customize<DateOnly>(opt => opt.FromFactory<DateTime>(DateOnly.FromDateTime));
        fixture.Customize<Document>(composer =>
            composer.With(x => x.Id, ObjectId.GenerateNewId().ToString()));

        fixture.Customize<DraftDocument>(composer =>
            composer.With(x => x.Id, ObjectId.GenerateNewId().ToString()));

        fixture.Customize<AccountAuthorizationDocument>(composer =>
            composer.With(x => x.CreatedAt, DateTime.UtcNow)
                .With(x => x.UpdatedAt, DateTime.UtcNow));
        fixture.Customize<OwnersDetails>(composer =>
            composer.With(x => x.Birthday, DateOnly.FromDayNumber(12))
                .With(x => x.LastSSNRejectionDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastPersonalBankruptcyDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<BusinessDetails>(composer =>
            composer.With(x => x.LastEINRejectionDate, DateOnly.FromDayNumber(12))
                .With(x => x.LoansLastDefaultedDate, DateOnly.FromDayNumber(12))
                .With(x => x.BusinessStartDate, DateOnly.FromDayNumber(12))
                .With(x => x.FirstReportedTradeLineDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastBankruptcyDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastJudgmentDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastLienDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<OwnersDetailsUpdate>(composer =>
            composer.With(x => x.Birthday, DateOnly.FromDayNumber(12))
                .With(x => x.LastSSNRejectionDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastPersonalBankruptcyDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<BusinessDetailsUpdate>(composer =>
            composer.With(x => x.LastEINRejectionDate, DateOnly.FromDayNumber(12))
                .With(x => x.LoansLastDefaultedDate, DateOnly.FromDayNumber(12))
                .With(x => x.BusinessStartDate, DateOnly.FromDayNumber(12))
                .With(x => x.FirstReportedTradeLineDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastBankruptcyDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastJudgmentDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastLienDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<OwnersDetailsPatch>(composer =>
            composer.With(x => x.Birthday, DateOnly.FromDayNumber(12))
                .With(x => x.LastSSNRejectionDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastPersonalBankruptcyDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<BusinessDetailsPatch>(composer =>
            composer.With(x => x.LastEINRejectionDate, DateOnly.FromDayNumber(12))
                .With(x => x.LoansLastDefaultedDate, DateOnly.FromDayNumber(12))
                .With(x => x.BusinessStartDate, DateOnly.FromDayNumber(12))
                .With(x => x.FirstReportedTradeLineDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastBankruptcyDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastJudgmentDate, DateOnly.FromDayNumber(12))
                .With(x => x.LastLienDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<CreateCreditModel>(composer =>
            composer.With(x => x.StartDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<CreditDto>(composer =>
            composer.With(x => x.StartDate, DateOnly.FromDayNumber(12))
                .With(x => x.CloseDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<CreditDetailsDto>(composer =>
            composer.With(x => x.OldestDueOrPastDueDate, DateOnly.FromDayNumber(12)));
        fixture.Customize<PatchDrawDetails>(composer =>
            composer.With(x => x.ProjectEndDate, DateOnly.FromDayNumber(12))
                .With(x => x.LoansLastDefaultedDate, DateOnly.FromDayNumber(12)));

        fixture.Customize<DrawApprovalDocument>(composer =>
            composer.With(x => x.DrawAmountRiskLevel, fixture.Create<DrawAmountRiskLevel>().ToString())
                .With(x => x.AutomatedDecisionResult, fixture.Create<AutomatedDecisionResult>().ToString())
                .With(x => x.AutomatedApprovalResult, fixture.Create<AutomatedApprovalResult>().ToString())
                .With(x => x.Type, fixture.Create<DrawApprovalType>().ToString())
                .With(x => x.Status, fixture.Create<DrawApprovalStatus>().ToString()));
        
        fixture.Customize<FactoringDetailsDocument>(composer => composer
            .With(x => x.InHouseCreditStatus, fixture.Create<CreditStatus>().ToString()));
        fixture.Customize<DrawDetailsDocument>(composer => composer
            .With(x => x.ProjectApprovalStatus, fixture.Create<ProjectApprovalStatus>().ToString())
            .With(x => x.ProjectApprovalStatus, fixture.Create<ProjectApprovalStatus>().ToString())
            .With(x => x.CreditPurchaseType, fixture.Create<CreditPurchaseType>().ToString())
            .With(x => x.AccountStatus, fixture.Create<CreditStatus>().ToString()));
        fixture.Customize<UpdateDrawDetails>(composer =>
            composer.With(x => x.ProjectEndDate, DateOnly.FromDayNumber(12))
                .With(x => x.LoansLastDefaultedDate, DateOnly.FromDayNumber(12)));

        fixture.Customize<GetDrawApprovalsQueryWithPagination>(composer => composer
            .With(x => x.AppDateFrom, DateOnly.FromDayNumber(12))
            .With(x => x.AppDateTo, DateOnly.FromDayNumber(12)));
        
        fixture.Customize<ObjectId>(opt => opt.FromFactory(ObjectId.GenerateNewId));
        fixture.Register(ObjectId.GenerateNewId);

        return fixture;
    })
    {
    }
}