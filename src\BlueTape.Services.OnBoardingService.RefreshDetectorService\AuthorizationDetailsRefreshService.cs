﻿using Amazon.SimpleNotificationService.Model;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using Microsoft.Extensions.Logging;
using Serilog.Context;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService;

public class AuthorizationDetailsRefreshService(
    IDecisionEngineExecutionService decisionEngineExecutionService,
    ILogger<AuthorizationDetailsRefreshService> logger) : IAuthorizationDetailsRefreshService
{
    public async Task Process(ScheduledUpdateEvent scheduledUpdateEvent, CancellationToken cancellationToken)
    {
        using (LogContext.PushProperty("approvedCreditApplicationId", scheduledUpdateEvent.Details.ApprovedCreditApplicationId))
        {
            logger.LogInformation("Generating request for Decision Engine execution. Scheduled update: {scheduledUpdateEvent}", scheduledUpdateEvent.EventType);

            var jobId = Guid.NewGuid();

            await decisionEngineExecutionService.StartCreditApplicationInitializationStep(
                new CreditApplicationInitializationStepStartRequest()
                {
                    JobId = jobId,
                    CorrelationId = scheduledUpdateEvent.BlueTapeCorrelationId,
                    CreditApplicationId = scheduledUpdateEvent.Details.ApprovedCreditApplicationId,
                    ScheduledUpdate = GetScheduledUpdateType(scheduledUpdateEvent),
                    CreatedBy = scheduledUpdateEvent.UserId,
                    ScheduleMode = scheduledUpdateEvent.Details.ScheduleMode.ToString()
                }, cancellationToken);

            logger.LogInformation("Triggered Decision Engine execution. Scheduled update: {scheduledUpdateEvent}", scheduledUpdateEvent.EventType);
        }
    }

    private static string GetScheduledUpdateType(ScheduledUpdateEvent scheduledUpdateEvent)
    {
        var scheduledUpdateType = scheduledUpdateEvent.EventType.Split(".").LastOrDefault();
        if (string.IsNullOrEmpty(scheduledUpdateType))
            throw new ValidationException($"Invalid scheduled update type: {scheduledUpdateEvent.EventType}");

        return scheduledUpdateType;
    }
}
