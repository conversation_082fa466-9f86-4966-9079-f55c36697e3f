using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;

public static class DrawApprovalExtensions
{
    public static string GetDrawApprovalType(LoanOrigin type, string? paymentPlanType)
    {
        return type switch
        {
            LoanOrigin.Express => DrawApprovalType.Express.ToString().ToLower(),
            LoanOrigin.Quote => DrawApprovalType.Quote.ToString().ToLower(),
            LoanOrigin.Factoring => DrawApprovalType.Factoring.ToString().ToLower(),
            LoanOrigin.NoSupplier => DrawApprovalType.NoSupplier.ToString().ToLower(),
            _ => paymentPlanType ?? string.Empty
        };
    }

    public static string GetCreditApplicationTypeByDrawType(DrawApprovalType type)
    {
        return type switch
        {
            DrawApprovalType.Custom => CreditApplicationType.LineOfCredit.ToString(),
            DrawApprovalType.Regular => CreditApplicationType.LineOfCredit.ToString(),
            DrawApprovalType.Express => CreditApplicationType.LineOfCredit.ToString(),
            DrawApprovalType.NoSupplier => CreditApplicationType.LineOfCredit.ToString(),
            DrawApprovalType.VirtualCard => CreditApplicationType.LineOfCredit.ToString(),
            DrawApprovalType.Quote => CreditApplicationType.LineOfCredit.ToString(),
            DrawApprovalType.Factoring => CreditApplicationType.InHouseCredit.ToString(),
            _ => throw new ValidationException("Unsupported draw approval type")
        };
    }

    public static bool IsFactoring(this DrawApprovalDocument drawApproval)
    {
        return string.Equals(drawApproval.Type, DrawApprovalType.Factoring.ToString(), StringComparison.InvariantCultureIgnoreCase);
    }
}