﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.Application.Tests.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using Moq;
using Newtonsoft.Json;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Strategies.Reports;

public class InitializationStepDataRetrievingStrategyTests
{
    private readonly InitializationStepDataRetrievingStrategy _strategy;

    private readonly Mock<IDecisionEngineStepsBviResultsService> _decisionEngineStepsBviResultsServiceMock = new();
    private readonly Mock<IDecisionEngineStepsRepository> _decisionEngineStepsRepository = new();
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepository = new();
    private readonly Mock<ICreditApplicationAuthorizationDetailsService> _accountAuthorizationService = new();

    public InitializationStepDataRetrievingStrategyTests()
    {
        _strategy = new InitializationStepDataRetrievingStrategy(_decisionEngineStepsRepository.Object,
            _creditApplicationRepository.Object, _decisionEngineStepsBviResultsServiceMock.Object, _accountAuthorizationService.Object);
    }

    [Theory, CustomAutoData]
    public async Task CollectReportStepData_ValidData_ReturnsDataForReport(CreditApplicationDocument creditApplication)
    {
        creditApplication.Id = TestDataConstants.CreditApplicationId;
        creditApplication.CompanyId = TestDataConstants.CompanyId;
        var inputString = JsonConvert.SerializeObject(new
        {
            creditApplication.DraftId,
        }, Formatting.Indented);
        var outputString = JsonConvert.SerializeObject(creditApplication, Formatting.Indented);

        _creditApplicationRepository.Setup(x => x.GetById(creditApplication.Id, default))
            .ReturnsAsync(creditApplication);

        var result = await _strategy.CollectReportStepData(TestDataConstants.CreditApplicationId, TestDataConstants.CompanyId, default);

        result.ShouldNotBeNull();
        result.CompanyId.ShouldBe(creditApplication.CompanyId);
        result.StepName.ShouldBe(StepName.Initialization);
        result.CreditApplicationId.ShouldBe(creditApplication.Id);
        result.StepInput.ShouldBe(inputString);
        result.StepOutput.ShouldBe(outputString);
        result.AccountAuthorizationDetails.ShouldBeNull();
        result.BviResults.ShouldBeEmpty();
    }

    [Fact]
    public void IsApplicable_ApplicableStepName_ReturnsTrue()
    {
        var result = _strategy.IsApplicable(StepName.Initialization);

        result.ShouldBeTrue();
    }

    [Fact]
    public void IsApplicable_NotApplicableStepName_ReturnsFalse()
    {
        var result = _strategy.IsApplicable(StepName.AffordabilityAssessment);

        result.ShouldBeFalse();
    }
}
