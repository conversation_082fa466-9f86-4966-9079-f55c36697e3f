﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Utilities.Providers;
using Serilog;
using System.Linq.Expressions;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class AccountAuthorizationsService : IAccountAuthorizationsService
{
    private readonly IAccountAuthorizationsRepository _accountAuthorizationsRepository;
    private readonly IAccountAuthorizationDetailsChangesService _accountAuthorizationDetailsChangesService;
    private readonly IMapper _mapper;
    private readonly IDateProvider _dateProvider;

    public AccountAuthorizationsService(
        IAccountAuthorizationsRepository accountAuthorizationsRepository,
        IAccountAuthorizationDetailsChangesService accountAuthorizationDetailsChangesService,
        IMapper mapper, IDateProvider dateProvider)
    {
        _accountAuthorizationsRepository = accountAuthorizationsRepository;
        _accountAuthorizationDetailsChangesService = accountAuthorizationDetailsChangesService;
        _mapper = mapper;
        _dateProvider = dateProvider;
    }

    public async Task<IEnumerable<AccountAuthorization>> GetAllByFilters(string? id, string? companyId, string? einHash, string? ssnHash,
        CancellationToken ct)
    {
        return _mapper.Map<IEnumerable<AccountAuthorization>>(
            await _accountAuthorizationsRepository.GetAllByFilters(id, companyId, einHash, ssnHash, ct));
    }

    public async Task<IEnumerable<AccountAuthorization>> GetAll(CancellationToken ct)
    {
        var accountAuthorizations = await _accountAuthorizationsRepository.GetAll(ct);

        return _mapper.Map<IEnumerable<AccountAuthorization>>(accountAuthorizations);
    }

    public async Task<IEnumerable<AccountAuthorization>> GetAll(Expression<Func<AccountAuthorizationDocument, bool>> predicate, CancellationToken ct)
    {
        var accountAuthorizations = await _accountAuthorizationsRepository.GetAll(predicate, ct);

        return _mapper.Map<IEnumerable<AccountAuthorization>>(accountAuthorizations);
    }

    public async Task<AccountAuthorization> Create(CreateAccountAuthorization model, CancellationToken ct)
    {
        Log.Information("Started creation of account authorization details");

        var existingCompanyAccountAuthorizationDetails =
            (await _accountAuthorizationsRepository.GetAllByFilters(null, model.CompanyId, model.EinHash, null, ct)).ToList();

        if (existingCompanyAccountAuthorizationDetails.Any())
        {
            Log.Information("Account authorization details already exists. Returning existing entry");
            return _mapper.Map<AccountAuthorization>(existingCompanyAccountAuthorizationDetails.Single());
        }

        var accountAuthorizationDto = _mapper.Map<AccountAuthorizationDocument>(model);
        var accountAuthorization = await _accountAuthorizationsRepository.Add(accountAuthorizationDto, ct);

        Log.Information("Finished creation of account authorization details: {id}", accountAuthorization.Id);

        return _mapper.Map<AccountAuthorization>(accountAuthorization);
    }

    public async Task<IEnumerable<AccountAuthorization>> CreateRange(IEnumerable<CreateAccountAuthorization> models, CancellationToken ct)
    {
        Log.Information("Started creation of account authorization details range");

        var einHashes = models.Select(x => x.EinHash).ToArray();
        var existingCompanyAccountAuthorizationDetails = (await _accountAuthorizationsRepository.GetByEinHashes(einHashes, ct))?.ToList();
        var existingAccountAuthorizationDetailsEinHashes = existingCompanyAccountAuthorizationDetails?.Select(x => x.EinHash);
        var accountAuthorizationDetailsToInsert = existingAccountAuthorizationDetailsEinHashes == null ? models :
            models.Where(model => !existingAccountAuthorizationDetailsEinHashes.Contains(model.EinHash));

        var accountAuthorizationDtos = _mapper.Map<IEnumerable<AccountAuthorizationDocument>>(accountAuthorizationDetailsToInsert);
        var accountAuthorizations = await _accountAuthorizationsRepository.AddRange(accountAuthorizationDtos, ct);

        Log.Information("Finished creation of account authorization details range");

        return _mapper.Map<IEnumerable<AccountAuthorization>>(accountAuthorizations);
    }

    public async Task<AccountAuthorization> GetById(string id, CancellationToken ct)
    {
        var accountAuthorization = await _accountAuthorizationsRepository.GetById(id, ct);

        return _mapper.Map<AccountAuthorization>(accountAuthorization);
    }

    public async Task<AccountAuthorization> Update(UpdateAccountAuthorization model, CancellationToken ct)
    {
        Log.Information("Started update of account authorization details {id}", model.Id);

        var accountAuthorizationDocument = await _accountAuthorizationsRepository.GetById(model.Id, ct);
        if (accountAuthorizationDocument == null)
            throw new VariableNullException($"Account authorization with id {model.Id} not found");

        var initialAccountAuthDetails = ObjectExtensions.DeepCopyJson(accountAuthorizationDocument);

        _mapper.Map(model, accountAuthorizationDocument);

        var updatedAccountAuthorization = await UpdateDocument(accountAuthorizationDocument, ct);

        Log.Information("Finished update of account authorization details {id}", model.Id);

        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialAccountAuthDetails!, updatedAccountAuthorization, model.CreditApplicationId, ExecutionChangeType.CreditApplication, ct);

        return _mapper.Map<AccountAuthorization>(updatedAccountAuthorization);
    }

    public async Task<AccountAuthorization> Patch(PatchAccountAuthorization model, CancellationToken ct)
    {
        Log.Information("Started patch of account authorization details {id}", model.Id);

        var accountAuthorizationDocument = await _accountAuthorizationsRepository.GetById(model.Id, ct);
        if (accountAuthorizationDocument == null)
            throw new VariableNullException($"Account authorization with id {model.Id} not found");

        var initialAccountAuthDetails = ObjectExtensions.DeepCopyJson(accountAuthorizationDocument);

        var documentToUpdate = MapPatchAccountAuthorizationDetailsToDocument(accountAuthorizationDocument, model);
        var updatedAccountAuthorization = await UpdateDocument(documentToUpdate, ct);

        Log.Information("Finished patch of account authorization details {id}", model.Id);

        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialAccountAuthDetails!, updatedAccountAuthorization, model.CreditApplicationId, ExecutionChangeType.CreditApplication, ct);

        return _mapper.Map<AccountAuthorization>(updatedAccountAuthorization);
    }

    public async Task<AccountAuthorization> Nullify(string id, NullifyAccountAuthorization model, CancellationToken ct)
    {
        Log.Information("Started nullifying of account authorization {id}", id);
        var accountAuthorizationDocument = await _accountAuthorizationsRepository.GetById(id, ct);

        if (accountAuthorizationDocument is null)
            throw new VariableNullException($"Account authorization with id {id} not found");

        var initialAccountAuth = ObjectExtensions.DeepCopyJson(accountAuthorizationDocument);

        NullifyAccAuth(accountAuthorizationDocument, model.UpdatedBy);
        var updatedAccountAuthorization = await UpdateDocument(accountAuthorizationDocument, ct);

        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialAccountAuth!, updatedAccountAuthorization, model.CreditApplicationId, ExecutionChangeType.CreditApplication, ct);

        return _mapper.Map<AccountAuthorization>(updatedAccountAuthorization);
    }

    public async Task RejectOwners(string companyId, string creditApplicationId, string userId, CancellationToken ct)
    {
        var accountAuthDocuments = (await _accountAuthorizationsRepository.GetAllByFilters(null, companyId, null, null, ct));
        var accountAuthorizationDocument = accountAuthDocuments.MaxBy(x => x.CreatedAt);
        if (accountAuthorizationDocument?.OwnersDetails?.Count() == 0 || accountAuthorizationDocument is null) return;
        Log.Information("Start reject owners of account authorization: {id}", accountAuthorizationDocument.Id);
        var initialAccountAuthDetails = ObjectExtensions.DeepCopyJson(accountAuthorizationDocument);

        var currentDate = _dateProvider.CurrentDateTime;
        foreach (var owner in accountAuthorizationDocument.OwnersDetails!)
        {
            owner.LastSSNRejectionDate = currentDate;
        }
        accountAuthorizationDocument.UpdatedBy = userId;
        accountAuthorizationDocument.UpdatedAt = currentDate;

        var updatedAccountAuthorization = await UpdateDocument(accountAuthorizationDocument, ct);

        Log.Information("Finished reject owners of account authorization {id}", accountAuthorizationDocument.Id);

        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialAccountAuthDetails!, updatedAccountAuthorization, creditApplicationId, ExecutionChangeType.CreditApplication, ct);
    }

    public async Task<IEnumerable<AccountAuthorization>> GetByEinList(string[] einHashes, CancellationToken ct)
        => _mapper.Map<IEnumerable<AccountAuthorization>>(await _accountAuthorizationsRepository.GetByEinHashes(einHashes, ct));

    public async Task<IEnumerable<AccountAuthorization>> GetBySsnList(string[] ssnHashes, CancellationToken ct)
    {
        var result = await _accountAuthorizationsRepository.GetBySsnHashes(ssnHashes, ct);
        return _mapper.Map<IEnumerable<AccountAuthorization>>(result);
    }

    private AccountAuthorizationDocument MapPatchAccountAuthorizationDetailsToDocument(AccountAuthorizationDocument document, PatchAccountAuthorization model)
    {
        document.UpdatedBy = model.UpdatedBy;
        document.IsSentBack = model.IsSentBack ?? document.IsSentBack;

        if (model.BusinessDetails != null)
            MapPatchBusinessDetails(document, model.BusinessDetails);

        if (model.OwnersDetails != null && model.OwnersDetails.Any())
            MapPatchOwnersDetails(document, model.OwnersDetails);

        if (model.OwnersEntitiesDetails != null && model.OwnersEntitiesDetails.Any())
            MapPatchOwnersEntitiesDetails(document, model.OwnersEntitiesDetails);

        if (model.BankAccountDetails != null && model.BankAccountDetails.Any())
            MapPatchBankAccountDetails(document, model.BankAccountDetails);

        if (model.FactoringOverallDetails != null)
            MapFactoringOverallDetails(document, model.FactoringOverallDetails);

        if (model.CreditDetails != null)
            MapCreditDetails(document, model.CreditDetails);

        return document;
    }

    private void MapPatchBusinessDetails(AccountAuthorizationDocument document, BusinessDetailsPatch businessDetailsPatch)
    {
        businessDetailsPatch.BRICodes = businessDetailsPatch.BRICodes != null && businessDetailsPatch.BRICodes.Any()
            ? businessDetailsPatch.BRICodes
            : document.BusinessDetails.BRICodes;
        document.BusinessDetails = _mapper.Map(businessDetailsPatch, document.BusinessDetails);
    }

    private void MapCreditDetails(AccountAuthorizationDocument document, AccountAuthorizationCreditDetails details)
        => _mapper.Map(details, document.CreditDetails);

    private void MapFactoringOverallDetails(AccountAuthorizationDocument document, AccountAuthorizationFactoringOverallDetails details)
        => _mapper.Map(details, document.FactoringOverallDetails);

    private void MapPatchOwnersDetails(AccountAuthorizationDocument document, IEnumerable<OwnersDetailsPatch> ownersDetailsPatch)
    {
        var existingOwnerDetails = document.OwnersDetails.ToList();

        foreach (var ownerDetails in ownersDetailsPatch)
        {
            var detailsIndex = existingOwnerDetails.FindIndex(x => x.Identifier == ownerDetails.Identifier);
            if (detailsIndex == -1) existingOwnerDetails.Add(_mapper.Map<OwnersDetailsDocument>(ownerDetails));
            else
            {
                ownerDetails.CRICodes = ownerDetails.CRICodes != null && ownerDetails.CRICodes.Any() ? ownerDetails.CRICodes : existingOwnerDetails[detailsIndex].CRICodes;
                existingOwnerDetails[detailsIndex] = _mapper.Map(ownerDetails, existingOwnerDetails[detailsIndex]);
            }
        }

        document.OwnersDetails = existingOwnerDetails;
    }

    private void MapPatchOwnersEntitiesDetails(AccountAuthorizationDocument document, IEnumerable<OwnersEntitiesDetailsPatch> ownersDetailsPatch)
    {
        var existingOwnerDetails = document.OwnersEntitiesDetails.ToList();

        foreach (var ownerDetails in ownersDetailsPatch)
        {
            var detailsIndex = existingOwnerDetails.FindIndex(x => x.Identifier == ownerDetails.Identifier);
            if (detailsIndex == -1) existingOwnerDetails.Add(_mapper.Map<OwnersEntitiesDetailsDocument>(ownerDetails));
            else
            {
                var existingOwnerDetailsItem = existingOwnerDetails[detailsIndex];
                ownerDetails.BRICodes = ownerDetails.BRICodes != null && ownerDetails.BRICodes.Any()
                    ? ownerDetails.BRICodes
                    : existingOwnerDetailsItem.BRICodes;

                existingOwnerDetails[detailsIndex] = _mapper.Map(ownerDetails, existingOwnerDetailsItem);
            }
        }

        document.OwnersEntitiesDetails = existingOwnerDetails;
    }

    private void MapPatchBankAccountDetails(AccountAuthorizationDocument document, IEnumerable<BankAccountDetailsPatch> bankAccountDetailsPatch)
    {
        var existingBankAccountDetails = document.BankAccountDetails.ToList();

        foreach (var bankAccountDetails in bankAccountDetailsPatch)
        {
            var detailsIndex = existingBankAccountDetails.FindIndex(x => x.Id == bankAccountDetails.Id);
            if (detailsIndex == -1)
            {
                var bankAccountDetailsToAdd = _mapper.Map<BankAccountDetailsDocument>(bankAccountDetails);
                bankAccountDetailsToAdd.Identifier = $"account{existingBankAccountDetails.Count + 1}";
                existingBankAccountDetails.Add(bankAccountDetailsToAdd);
            }
            else
            {
                existingBankAccountDetails[detailsIndex] = _mapper.Map(bankAccountDetails, existingBankAccountDetails[detailsIndex]);
            }
        }

        document.BankAccountDetails = existingBankAccountDetails;
    }

    private async Task<AccountAuthorizationDocument> UpdateDocument(AccountAuthorizationDocument document, CancellationToken ct)
    {
        var accountAuthorization = await _accountAuthorizationsRepository.Update(document, ct);

        return accountAuthorization;
    }

    private static void NullifyAccAuth(AccountAuthorizationDocument document, string updatedBy)
    {
        document.UpdatedBy = updatedBy;
        document.UpdatedAt = DateTime.UtcNow;
        NullifyBusinessDetails(document);
        NullifyBankDetails(document);
        NullifyOwnerDetails(document);
    }

    private static void NullifyBusinessDetails(AccountAuthorizationDocument document)
    {
        var lastEinRejectionDate = document.BusinessDetails.LastEINRejectionDate;

        document.BusinessDetails = new BusinessDetailsDocument
        {
            LastEINRejectionDate = lastEinRejectionDate,
        };
    }

    private static void NullifyBankDetails(AccountAuthorizationDocument document)
    {
        document.BankAccountDetails = [];
    }

    private static void NullifyOwnerDetails(AccountAuthorizationDocument document)
    {
        var owners = document.OwnersDetails.Select(owner => new OwnersDetailsDocument
        {
            Id = owner.Id,
            Identifier = owner.Identifier,
            PercentOwned = owner.PercentOwned,
            SsnHash = owner.SsnHash,
            IsPrincipal = owner.IsPrincipal,
            LastSSNRejectionDate = owner.LastSSNRejectionDate,
        })
            .ToList();

        document.OwnersDetails = owners;
    }
}