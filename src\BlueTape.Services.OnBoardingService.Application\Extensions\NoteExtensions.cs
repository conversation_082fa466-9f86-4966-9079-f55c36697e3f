﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Constants;
using System.Globalization;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;

public static class NoteExtensions
{
    public static string FormatLimitChange(decimal? oldValue, decimal? newValue)
    {
        var culture = CultureInfo.CreateSpecificCulture("en-US");

        var oldFormatted = oldValue.HasValue
            ? $"{oldValue.Value.ToString("C2", culture)}"
            : "$0.00";

        var newFormatted = newValue?.ToString("C2", culture) ?? "$0.00";

        return $"{oldFormatted} > {newFormatted}";
    }

    public static string ToCreditApplicationCaption(this string? type) =>
        type switch
        {
            not null when string.Equals(type, CreditApplicationType.InHouseCredit.ToString(),
                StringComparison.OrdinalIgnoreCase) => CaptionConstants.InHouseCredit,
            not null when string.Equals(type, CreditApplicationType.LineOfCredit.ToString(),
                StringComparison.OrdinalIgnoreCase) => CaptionConstants.LineOfCredit,
            not null when string.Equals(type, CreditApplicationType.ARAdvance.ToString(), StringComparison.OrdinalIgnoreCase)
                => CaptionConstants.ARAdvance,
            _ => CaptionConstants.Default
        };
}
