﻿using AutoMapper;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Messages;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.AuthorizationPeriods;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Loan;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.Compatibility;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using CreditStatus = BlueTape.LS.Domain.Enums.CreditStatus;
using VariableNullException = BlueTape.Common.ExceptionHandling.Exceptions.VariableNullException;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class DrawApprovalService(
    IDrawApprovalRepository drawApprovalRepository,
    ICreditApplicationRepository creditApplicationRepository,
    ILoanApplicationRepository loanApplicationRepository,
    IMapper mapper,
    IDateProvider dateProvider,
    ILoanPaymentPlanRepository paymentPlanRepository,
    IInvoiceExternalService invoiceExternalService,
    ICompanyExternalService companyExternalService,
    IDraftService draftService,
    ILoanApplicationSyncMessageSender laSyncMessageSender,
    IInvoiceSyncMessageSender invoiceSyncMessageSender,
    ILogger<DrawApprovalService> logger,
    INodeExternalService nodeExternalService,
    IDrawApprovalNotesService notesService,
    IDecisionEngineExecutionService decisionEngineExecutionService,
    ILoanExternalService loanExternalService,
    ILinqPalInteractionService integrationService)
    : IDrawApprovalService
{
    public async Task<IEnumerable<DrawApproval>> Get(CancellationToken ct)
    {
        var drawApprovals = await drawApprovalRepository.GetAll(ct);
        return mapper.Map<IEnumerable<DrawApproval>>(drawApprovals);
    }

    public async Task<GetQueryWithPaginationResultModel<DrawApproval>> GetByFilter(GetDrawApprovalsQueryWithPagination query, CancellationToken ct)
    {
        EnrichQueryWithAdditionalProperties(query);
        var drawApprovals = await drawApprovalRepository.GetAllByFiltersWithPagination(query, ct);

        return mapper.Map<GetQueryWithPaginationResultModel<DrawApproval>>(drawApprovals);
    }

    public async Task<DrawApproval?> GetById(string id, CancellationToken ct)
    {
        var drawApproval = await drawApprovalRepository.GetById(id, ct);
        return mapper.Map<DrawApproval>(drawApproval);
    }

    public async Task<DrawApproval?> GetByInvoicesIds(string[] invoicesIds, CancellationToken ct)
    {
        var result = await drawApprovalRepository.GetByInvoicesIds(invoicesIds, ct);
        return mapper.Map<DrawApproval?>(result);
    }

    public async Task<IEnumerable<DrawApproval?>> GetManyByInvoicesIds(string[] invoicesIds, CancellationToken ct)
    {
        var result = await drawApprovalRepository.GetManyByInvoicesIds(invoicesIds, ct);
        return mapper.Map<IEnumerable<DrawApproval?>>(result);
    }

    public async Task<DrawApproval> Patch(PatchDrawApproval patchDrawApproval, CancellationToken ct)
    {
        logger.LogInformation("Started patch of draw approval {id}", patchDrawApproval.Id);
        var currentDateTime = dateProvider.CurrentDateTime;

        var drawApproval = await drawApprovalRepository.GetById(patchDrawApproval.Id, ct);

        if (drawApproval is null) throw new VariableNullException($"Draw approval {patchDrawApproval.Id} was not found");
        if (!CanDrawApprovalStatusBeChanged(drawApproval, patchDrawApproval.Status.ToString()))
        {
            logger.LogWarning("Tried to patch Draw approval {id} which is already in status {status}", patchDrawApproval.Id, patchDrawApproval.Status);
            return mapper.Map<DrawApproval>(drawApproval);
        }

        mapper.Map(patchDrawApproval, drawApproval);

        if (patchDrawApproval.Status.HasValue)
        {
            drawApproval.LastStatusChangedAt = currentDateTime;
            drawApproval.LastStatusChangedBy = drawApproval.UpdatedBy;
        }

        if (string.IsNullOrEmpty(drawApproval.CreditApplicationId) && string.IsNullOrEmpty(patchDrawApproval.CreditApplicationId))
        {
            Enum.TryParse(drawApproval.Type, true, out DrawApprovalType drawType);
            var query = new GetCreditApplicationQuery
            {
                CompanyId = drawApproval.CompanyId,
                Status = [CreditApplicationStatus.Processing.ToString().ToLower(),
                    CreditApplicationStatus.Processed.ToString().ToLower(),
                    CreditApplicationStatus.Approved.ToString().ToLower(),
                    CreditApplicationStatus.Rejected.ToString().ToLower()],
                Type = [DrawApprovalExtensions.GetCreditApplicationTypeByDrawType(drawType)],
            };

            if (drawType is DrawApprovalType.Factoring) query.MerchantId = drawApproval.MerchantId;
            var creditApplications = (await creditApplicationRepository.GetAllByFilters(query, ct)).ToList();

            if (creditApplications.Count > 0)
            {
                drawApproval.CreditApplicationId = creditApplications.MaxBy(x => x.CreatedAt)?.Id;
            }
        }

        var updatedDrawApproval = await drawApprovalRepository.Update(drawApproval, ct);
        await SyncDrawApprovalChanges(drawApproval, ct);

        logger.LogInformation("Finished update of draw approval {id}. New Status: {status}", updatedDrawApproval.Id, updatedDrawApproval.Status);

        return mapper.Map<DrawApproval>(updatedDrawApproval);
    }

    public async Task<DrawApproval> ReviewDrawApproval(ReviewDrawApprovalModel review, CancellationToken ct)
    {
        logger.LogInformation("Started updating final status of draw approval {id}", review.Id);

        ValidateChangeDrawApprovalFinalStatusModel(review);
        var drawApproval = await drawApprovalRepository.GetById(review.Id, ct);

        logger.LogInformation("Started updating final status of draw approval {draw}", JsonSerializer.Serialize(drawApproval));

        if (!IsQuote(drawApproval) && !string.Equals(drawApproval.Status, DrawApprovalStatus.Processed.ToString(), StringComparison.OrdinalIgnoreCase))
        {
            throw new ValidationException("The draw approval is not in the correct state to be reviewed");
        }

        if (!CanDrawApprovalStatusBeChanged(drawApproval, review.NewStatus.ToString()))
        {
            logger.LogWarning("Tried to patch Draw approval {id} which is already in status {status}", drawApproval.Id, drawApproval.Status);
            return mapper.Map<DrawApproval>(drawApproval);
        }
        if (review.NewStatus == AdminDrawApprovalStatusUpdate.Approved) await ValidateApproving(drawApproval, ct);

        mapper.Map(review, drawApproval);

        UpdateDrawApprovalStatus(review, drawApproval);
        var updatedDrawApproval = await drawApprovalRepository.Update(drawApproval, ct);

        await SyncDrawApprovalChanges(drawApproval, ct);

        logger.LogInformation("Finished update of draw approval {id}. New status: {status}",
            review.Id, review.NewStatus);

        return mapper.Map<DrawApproval>(updatedDrawApproval);
    }

    public async Task<DrawApproval> Create(CreateDrawApproval createDrawApproval, CancellationToken ctx)
    {
        logger.LogInformation("Create draw approval: started creation process. Request: {@CreateDrawApproval}", createDrawApproval);

        var currentDateTime = dateProvider.CurrentDateTime;
        var invoiceId = createDrawApproval.Payables.FirstOrDefault()?.Id;

        var paymentPlanRequest = paymentPlanRepository.GetById(createDrawApproval.PaymentPlanId, ctx);
        var invoiceRequest = invoiceExternalService.GetById(invoiceId ?? string.Empty, ctx);
        await Task.WhenAll(paymentPlanRequest, invoiceRequest);

        var invoice = await invoiceRequest;
        var paymentPlan = await paymentPlanRequest;
        var merchantName = "";

        if (!string.IsNullOrEmpty(createDrawApproval.MerchantId) || !string.IsNullOrEmpty(invoice?.CompanyId) && invoice.Status != "draft")
            merchantName = await GetCompanyNameWithDba(createDrawApproval.MerchantId ?? invoice?.CompanyId ?? string.Empty, ctx);

        var companyName = await GetCompanyNameWithDba(createDrawApproval.CompanyId, ctx);

        var drawApproval = new DrawApprovalDocument
        {
            DrawAmount = createDrawApproval.Payables.Sum(x => x.Amount),
            CreatedBy = DrawAuthorizationConstants.OnBoardingService,
            ApplicationDate = currentDateTime,
            Status = DrawApprovalStatus.New.ToString(),
            Type = DrawApprovalExtensions.GetDrawApprovalType(createDrawApproval.LoanOrigin, paymentPlan.Type),
            LastStatusChangedAt = currentDateTime,
            InHouseCreditId = createDrawApproval.InHouseCreditId,
            ArAdvanceCreditId = createDrawApproval.ArAdvanceCreditId,
            LastStatusChangedBy = DrawAuthorizationConstants.OnBoardingService,
            MerchantId = !string.IsNullOrEmpty(createDrawApproval.MerchantId) ? createDrawApproval.MerchantId : invoice?.CompanyId ?? string.Empty,
            MerchantName = merchantName,
            CompanyName = companyName,
            ApplicantName = createDrawApproval.ApplicantName,
            CreditHoldAmount = createDrawApproval.CreditHoldAmount,
            ExpirationDate = createDrawApproval.ExpirationDate,
            LoanOrigin = createDrawApproval.LoanOrigin.ToString().ToLower(),
        };

        mapper.Map(createDrawApproval, drawApproval);

        if (string.IsNullOrEmpty(drawApproval.CreditApplicationId))
        {
            Enum.TryParse(drawApproval.Type, true, out DrawApprovalType drawType);
            var query = new GetCreditApplicationQuery
            {
                CompanyId = createDrawApproval.CompanyId,
                Status = [CreditApplicationStatus.Processing.ToString().ToLower(),
                    CreditApplicationStatus.Processed.ToString().ToLower(),
                    CreditApplicationStatus.Approved.ToString().ToLower(),
                    CreditApplicationStatus.Rejected.ToString().ToLower()],
                Type = [DrawApprovalExtensions.GetCreditApplicationTypeByDrawType(drawType)]
            };

            if (drawType is DrawApprovalType.Factoring) query.MerchantId = drawApproval.MerchantId;

            var creditApplications = (await creditApplicationRepository.GetAllByFilters(query, ctx)).ToList();

            if (creditApplications.Count > 0)
            {
                drawApproval.CreditApplicationId = creditApplications.MaxBy(x => x.CreatedAt)?.Id;
            }
        }

        var drawApprovalDocument = await drawApprovalRepository.Add(drawApproval, ctx);

        logger.LogInformation("Create draw approval: Draw approval {Id} created. Sending message to compatibility.", drawApprovalDocument.Id);
        await SyncDrawApprovalChanges(drawApproval, ctx);

        return mapper.Map<DrawApproval>(drawApprovalDocument);
    }

    public async Task<DrawApproval> PostTransaction(string id, string userId, NoteModel model, CancellationToken ct)
    {
        logger.LogInformation("Started PostTransaction of draw approval {id}.", id);
        var drawApproval = await drawApprovalRepository.GetById(id, ct);
        if (drawApproval is null) throw new VariableNullException($"Draw approval {id} was not found");

        var currentDateTime = dateProvider.CurrentDateTime;
        if (!string.IsNullOrEmpty(model.Note))
        {
            await notesService.Add(new CreateDrawApprovalNote
            {
                DrawApprovalId = drawApproval.Id,
                Note = model.Note,
                CreatedBy = userId,
                CreatedAt = currentDateTime
            }, ct);
        }

        var quote = drawApproval.Payables.FirstOrDefault(x => x.Type == PayableType.Quote.ToString());
        if (quote is null)
        {
            logger.LogWarning("Draw approval:{id} doesn't have a payable with quote type", drawApproval.Id);
            return mapper.Map<DrawApproval>(drawApproval);
        }

        drawApproval.UpdatedAt = currentDateTime;
        drawApproval.UpdatedBy = userId;
        drawApproval.InvoicedAt = currentDateTime;
        drawApproval.InvoicedBy = userId;
        var result = mapper.Map<DrawApproval>(await drawApprovalRepository.Update(drawApproval, ct));
        await nodeExternalService.PostTransaction(new PostTransactionRequest
        {
            QuoteId = quote.Id,
            Amount = quote.Amount
        }, ct);

        logger.LogInformation("Finished PostTransaction of draw approval {id}.", id);

        return result;
    }

    public async Task<DrawApproval> PatchExpirationDate(PatchExpirationDateAdminModel model, CancellationToken ct)
    {
        logger.LogInformation("Started patch ExpirationDate of draw approval {id}. New ExpirationDate is {date}", model.Id, model.NewExpirationDate);
        var drawApproval = await drawApprovalRepository.GetById(model.Id, ct);
        if (drawApproval is null) throw new VariableNullException($"Draw approval {model.Id} was not found");

        var quote = drawApproval.Payables.FirstOrDefault(x => x.Type == PayableType.Quote.ToString());
        if (quote is null)
        {
            logger.LogWarning("Draw approval:{id} doesn't have a quote", drawApproval.Id);
            return mapper.Map<DrawApproval>(drawApproval);
        }

        drawApproval.UpdatedBy = model.UserId;
        drawApproval.UpdatedAt = dateProvider.CurrentDateTime;
        drawApproval.ExpirationDate = model.NewExpirationDate;
        var updatedDrawApproval = await drawApprovalRepository.Update(drawApproval, ct);
        await nodeExternalService.UpdateAuthorization(new UpdateAuthorizationRequest
        {
            QuoteId = quote.Id,
            AuthorizationDeadline = model.NewExpirationDate.ToString("MM/dd/yyyy")
        }, ct);

        logger.LogInformation("Finished update of draw approval {id} with new ExpirationDate: {date}", updatedDrawApproval.Id, updatedDrawApproval.ExpirationDate);

        return mapper.Map<DrawApproval>(updatedDrawApproval);
    }

    public async Task<DrawApproval> PatchDrawApprovalDetails(string id, PatchInternalDrawApproval patchDrawApproval, CancellationToken ctx)
    {
        var existingDrawApproval = await drawApprovalRepository.GetById(id, ctx) ?? throw new VariableNullException($"Draw approval {id} does not exist.");
        if (patchDrawApproval.NewStatus.HasValue)
        {
            existingDrawApproval.Status = patchDrawApproval.NewStatus!.Value.ToString();
        }

        existingDrawApproval.UpdatedBy = DrawAuthorizationConstants.OnBoardingService;

        existingDrawApproval.DrawDetails = mapper.Map(patchDrawApproval.DrawDetails, existingDrawApproval.DrawDetails);

        if (patchDrawApproval.PayNowDetails is not null)
            existingDrawApproval.PayNowDetails = mapper.Map(patchDrawApproval.PayNowDetails, existingDrawApproval.PayNowDetails);

        if (patchDrawApproval.FactoringDetails is not null)
            existingDrawApproval.FactoringDetails = mapper.Map(patchDrawApproval.FactoringDetails, existingDrawApproval.FactoringDetails);

        if (patchDrawApproval.FactoringOverallDetails is not null)
            existingDrawApproval.FactoringOverallDetails = mapper.Map(patchDrawApproval.FactoringOverallDetails, existingDrawApproval.FactoringOverallDetails);

        var result = await drawApprovalRepository.Update(existingDrawApproval, ctx);

        await SyncDrawApprovalChanges(existingDrawApproval, ctx);

        return mapper.Map<DrawApproval>(result);
    }

    public async Task<StepFunctionsExecutionResponse> RunDrawApprovalDecisionEngineExecution(DrawApprovalDecisionEngineExecutionRequest request, CancellationToken ctx)
    {
        logger.LogInformation("Starting execution for draw approval company: {CompanyId}, inHouseCreditId: {IhCreditId}, arAdvanceCreditId: {ArCreditId}",
            request.CompanyId, request.InHouseCreditId, request.ArAdvanceCreditId);

        if (!string.IsNullOrEmpty(request.CreditId) && !Guid.TryParse(request.CreditId, out _) && request.LoanOrigin is not LoanOrigin.Factoring)
        {
            request.CreditId = (await loanExternalService.GetCreditsByFilters(new CreditFilterDto
            {
                CreditApplicationId = [request.CreditId],
                Product = ProductType.LineOfCredit,
                Status = CreditStatus.Active
            }, ctx)).FirstOrDefault()?.Id.ToString();
        }

        decisionEngineExecutionService.PreValidateDrawApprovalDecisionEngineExecution(request);
        var initializationStepRequest = mapper.Map<DrawApprovalInitializationStepStartRequest>(request);

        initializationStepRequest.JobId = Guid.NewGuid();
        var result = await decisionEngineExecutionService.StartDrawApprovalInitializationStep(initializationStepRequest, ctx);

        logger.LogInformation("Execution for draw approval company: {CompanyId}, inHouseCreditId: {IhCreditId}, arAdvanceCreditId: {ArCreditId}",
            request.CompanyId, request.InHouseCreditId, request.ArAdvanceCreditId);

        return result;
    }

    public async Task<StepFunctionsExecutionResponse> RunDecisionEngineInitializationStepForDrawApproval(DrawApprovalInitializationStepStartRequest request, CancellationToken ctx)
    {
        logger.LogInformation("Starting initialization step for draw approval: {DrawApprovalId}",
           request.DrawApprovalId);

        request.JobId = Guid.NewGuid();
        var result = await decisionEngineExecutionService.StartDrawApprovalInitializationStep(request, ctx);

        return result;
    }

    public async Task MigrateDrawApprovalCreditAndAccountStatuses(CancellationToken ct)
    {
        var drawApprovals = await drawApprovalRepository.GetAll(ct);
        foreach (var drawApproval in drawApprovals)
        {
            if (string.Equals(drawApproval.Type, DrawApprovalType.Factoring.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                if (!Guid.TryParse(drawApproval.InHouseCreditId, out var guidCreditId))
                {
                    logger.LogInformation("Unable to migrate Credit and Account status of drawApproval with id: {id}", drawApproval.Id);
                    continue;
                }

                var credit = await GetCredit(guidCreditId, ct);
                if (credit is null) continue;
                drawApproval.FactoringDetails.InHouseCreditStatus = credit?.Status.ToString();
            }
            else
            {
                var company = await companyExternalService.GetById(drawApproval.CompanyId, ct);
                if (!Guid.TryParse(drawApproval.CreditId, out var guidCreditId))
                {
                    var creditByFilters = (await loanExternalService.GetCreditsByFilters(new CreditFilterDto
                    {
                        CompanyId = company!.Id,
                        MerchantId = null,
                        CreditApplicationId = [drawApproval.CreditApplicationId],
                        Product = ProductType.LineOfCredit
                    }, ct)).FirstOrDefault();

                    if (creditByFilters is null)
                    {
                        logger.LogInformation("Unable to migrate Credit and Account status of drawApproval with id: {id}", drawApproval.Id);
                        continue;
                    }

                    drawApproval.DrawDetails.AccountStatus = creditByFilters.Status.ToString();
                }
                else
                {
                    var credit = await GetCredit(guidCreditId, ct);
                    if (credit is null) continue;
                    drawApproval.DrawDetails.AccountStatus = credit?.Status.ToString();
                }
            }

            await drawApprovalRepository.Update(drawApproval, ct);
        }
    }

    private async Task<CreditDto?> GetCredit(Guid creditId, CancellationToken ct)
    {
        try
        {
            return await loanExternalService.GetCreditById(creditId, false, ct);
        }
        catch (Exception e)
        {
            logger.LogWarning($"Unable to get Credit with Id: {creditId}");
            return null;
        }
    }

    public async Task<DrawApproval> PatchDrawApprovalPaymentPlan(PatchDrawPaymentPlanAdminModel model, CancellationToken ct)
    {
        logger.LogInformation("Started patch PaymentPlanId of draw approval {id}. New PaymentPlanId: {paymentPlanId}", model.Id, model.NewPaymentPlanId);
        var drawApproval = await drawApprovalRepository.GetById(model.Id, ct);
        if (drawApproval is null) throw new VariableNullException($"Draw approval {model.Id} was not found");

        var currentDateTime = dateProvider.CurrentDateTime;

        drawApproval.UpdatedAt = currentDateTime;
        drawApproval.UpdatedBy = model.UserId;
        drawApproval.PaymentPlanId = model.NewPaymentPlanId;

        var patchedDrawApproval = await drawApprovalRepository.Update(drawApproval, ct);
        logger.LogInformation("Finished update of draw approval {id} with PaymentPlanId: {paymentPlanId}", drawApproval.Id, drawApproval.PaymentPlanId);

        return mapper.Map<DrawApproval>(patchedDrawApproval);
    }

    public async Task<DrawApproval> UpdateDrawApproval(string id, UpdateDrawApproval updateDrawApproval, CancellationToken ct)
    {
        logger.LogInformation("Started update of draw approval {id}", id);
        var currentDateTime = dateProvider.CurrentDateTime;

        var drawApproval = await drawApprovalRepository.GetById(id, ct);
        if (drawApproval is null) throw new VariableNullException($"Draw approval {id} was not found");

        var invoices = await invoiceExternalService.GetByIds(updateDrawApproval.InvoiceIds.ToArray(), ct);
        if (invoices is null) throw new VariableNullException("Invoices was not found");
        var payables = mapper.Map<List<PayableItemDocument>>(invoices);
        mapper.Map(updateDrawApproval, drawApproval);

        if (string.IsNullOrEmpty(drawApproval.CreditApplicationId))
        {
            Enum.TryParse(drawApproval.Type, true, out DrawApprovalType drawType);
            var query = new GetCreditApplicationQuery
            {
                CompanyId = drawApproval.CompanyId,
                Status = [CreditApplicationStatus.Processing.ToString().ToLower(),
                    CreditApplicationStatus.Processed.ToString().ToLower(),
                    CreditApplicationStatus.Approved.ToString().ToLower(),
                    CreditApplicationStatus.Rejected.ToString().ToLower()],
                Type = [DrawApprovalExtensions.GetCreditApplicationTypeByDrawType(drawType)]
            };
            if (drawType is DrawApprovalType.Factoring) query.MerchantId = drawApproval.MerchantId;

            var creditApplications = (await creditApplicationRepository.GetAllByFilters(query, ct)).ToList();

            if (creditApplications.Count > 0)
            {
                drawApproval.CreditApplicationId = creditApplications.MaxBy(x => x.CreatedAt)?.Id;
            }
        }

        drawApproval.LastStatusChangedAt = currentDateTime;
        drawApproval.LastStatusChangedBy = drawApproval.UpdatedBy;
        drawApproval.Payables = payables;

        var updatedDrawApproval = await drawApprovalRepository.Update(drawApproval, ct);
        await SyncDrawApprovalChanges(drawApproval, ct);

        logger.LogInformation("Finished update of draw approval {id}. New Status: {status}",
            id, updatedDrawApproval.Status);

        return mapper.Map<DrawApproval>(updatedDrawApproval);
    }

    public async Task<DrawApproval> AddInvoices(string id, List<PayableItem> payables, CancellationToken ct)
    {
        var currentDateTime = dateProvider.CurrentDateTime;
        logger.LogInformation("Started adding Invoices to draw approval Id: {id}.", id);
        var drawApproval = await drawApprovalRepository.GetById(id, ct);
        if (drawApproval is null) throw new VariableNullException($"Draw approval with Id: {id} was not found");

        var mappedPayables = mapper.Map<List<PayableItemDocument>>(payables.Where(x => !drawApproval.Payables.Select(p => p.Id).Contains(x.Id)));
        drawApproval.InvoicedAt = currentDateTime;
        drawApproval.Payables = drawApproval.Payables.Concat(mappedPayables);
        drawApproval.DrawAmount = drawApproval.Payables.Where(x => x.Type == PayableType.Invoice.ToString()).Sum(x => x.Amount);

        logger.LogInformation(JsonSerializer.Serialize(drawApproval));
        var patchedDrawApproval = await drawApprovalRepository.Update(drawApproval, ct);

        logger.LogInformation("Finished adding Invoices to draw approval {id}", id);

        return mapper.Map<DrawApproval>(patchedDrawApproval);
    }

    public async Task<string?> GetProjectIdByDrawId(string id, CancellationToken ctx)
    {
        var loanApplication = await loanApplicationRepository.GetByDrawId(id, ctx);

        if (loanApplication?.InvoiceDetails?.InvoiceIds is null) return null;

        var invoices = await invoiceExternalService.GetByIds(loanApplication.InvoiceDetails.InvoiceIds.ToArray(), ctx);

        if (invoices is null) return null;

        var projectIds = invoices.Select(x => x.ProjectId).Distinct().ToList();

        return projectIds.Count > 1 ? null : projectIds.FirstOrDefault();
    }
    public async Task<DrawApproval> PatchAuthorizationPeriod(string drawApprovalId, string authPeriodId, CancellationToken ct)
    {
        logger.LogInformation("Started update of draw approval {id} with AuthorizationPeriodId: {authPeriodId}", drawApprovalId, authPeriodId);
        var drawApprovalDocument = await drawApprovalRepository.GetById(drawApprovalId, ct);
        if (drawApprovalDocument is null) throw new VariableNullException($"Draw approval {drawApprovalRepository} was not found");

        drawApprovalDocument.AuthorizationPeriodId = authPeriodId;

        logger.LogInformation("Finish update of draw approval {id} with AuthorizationPeriodId: {authPeriodId}", drawApprovalId, authPeriodId);
        return mapper.Map<DrawApproval>(await drawApprovalRepository.Update(drawApprovalDocument, ct));
    }

    private void UpdateDrawApprovalStatus(ReviewDrawApprovalModel review,
        DrawApprovalDocument drawApproval)
    {
        var currentDateTime = dateProvider.CurrentDateTime;

        drawApproval.LastStatusChangedAt = currentDateTime;
        drawApproval.LastStatusChangedBy = drawApproval.UpdatedBy;

        switch (review.NewStatus)
        {
            case AdminDrawApprovalStatusUpdate.Rejected:
                drawApproval.RejectedAt = currentDateTime;
                drawApproval.RejectedBy = drawApproval.UpdatedBy;
                drawApproval.StatusCode = review.Code;
                drawApproval.StatusNote = review.Note;
                return;
            case AdminDrawApprovalStatusUpdate.Approved:
                drawApproval.ApprovedAt = currentDateTime;
                drawApproval.ApprovedBy = drawApproval.UpdatedBy;
                return;
            case AdminDrawApprovalStatusUpdate.Canceled:
                drawApproval.CanceledAt = currentDateTime;
                drawApproval.CanceledBy = drawApproval.UpdatedBy;
                return;
            default:
                drawApproval.LastStatusChangedAt = currentDateTime;
                drawApproval.LastStatusChangedBy = drawApproval.UpdatedBy;
                return;
        }
    }

    private async Task<string> GetCompanyNameWithDba(string companyId, CancellationToken ctx)
    {
        var company = await companyExternalService.GetById(companyId, ctx);
        if (company == null) throw new VariableNullException(nameof(company));

        var companyDraft = (await draftService.GetAllByFilters(new DraftFilter()
        {
            CompanyId = companyId
        }, ctx)).FirstOrDefault(x => x.Type == "general_application");

        var companyDbaContent = companyDraft?.Data?.BusinessInfo?.Items?.FirstOrDefault(x => x.Identifier == "businessName")?.Content;
        var companyDbaToken = companyDbaContent == null ? string.Empty : JObject.FromObject(companyDbaContent)["dba"];
        var companyDba = companyDbaToken?.ToString();

        if (string.IsNullOrEmpty(companyDba)) return company.LegalName;

        return $"{company.LegalName} / {companyDba}";
    }

    private const string OtherRejectionCode = "P10";
    private static void ValidateChangeDrawApprovalFinalStatusModel(ReviewDrawApprovalModel review)
    {
        if (review.NewStatus != AdminDrawApprovalStatusUpdate.Rejected)
            return;

        if (review.NewStatus == AdminDrawApprovalStatusUpdate.Approved && review.ApprovedCreditLimit == null)
            throw new ValidationException("The Approved Credit Limit is not specified");

        if (string.IsNullOrEmpty(review.Code))
            throw new ValidationException("The rejection code has to be specified when draw approval final status is Rejected");

        if (string.Equals(review.Code, OtherRejectionCode, StringComparison.OrdinalIgnoreCase) && string.IsNullOrEmpty(review.Note))
            throw new ValidationException("The rejection note has to be specified when the rejection reason is other");
    }

    public async Task SyncDrawApprovalChanges(string drawApprovalId, CancellationToken ct)
    {
        logger.LogInformation("Starting synchronization of draw approval changes for ID: {id}", drawApprovalId);
        var drawApproval = await drawApprovalRepository.GetById(drawApprovalId, ct);

        if (drawApproval == null)
            throw new InvalidOperationException($"Draw approval with ID {drawApprovalId} not found.");

        await SyncDrawApprovalChanges(drawApproval, ct);
    }

    private async Task SyncDrawApprovalChanges(DrawApprovalDocument drawApproval, CancellationToken ct)
    {
        try
        {
            if (Enum.TryParse(typeof(DrawApprovalStatus), drawApproval.Status, true, out var drawApprovalStatusObj)
                && drawApprovalStatusObj is DrawApprovalStatus drawApprovalStatus)
            {
                var isFactoringQuote = drawApproval.IsFactoring() && IsQuote(drawApproval);

                if (drawApprovalStatus == DrawApprovalStatus.Approved &&
                    (drawApproval.ApprovedStatusSynchronizedAt == null ||
                    drawApproval.ApprovedStatusSynchronizedAt == DateTime.MinValue))
                {
                    if (isFactoringQuote)
                        await FactoringDrawApprovalApproved(drawApproval, ct);

                    var loanApplication = await loanApplicationRepository.GetByInvoicesIds(drawApproval.Payables.Select(x => x.Id), ct);

                    if (IsLoanApplicationQuote(loanApplication) && !drawApproval.IsFactoring())
                    {
                        if (drawApproval.AuthorizationPeriodId.IsNullOrEmpty())
                        {
                            var arAdvanceCreditHold = await CreateCreditHold(drawApproval, drawApproval.CreditId!, ct);
                            drawApproval.AuthorizationPeriodId = arAdvanceCreditHold.Id.ToString();
                        }
                    }

                    await StartIssueLoan(drawApproval, loanApplication, ct);

                    drawApproval.ApprovedStatusSynchronizedAt = DateTime.Now;
                    await drawApprovalRepository.Update(drawApproval, ct);
                }
                else if (drawApprovalStatus == DrawApprovalStatus.Rejected)
                {
                    await integrationService.SendIhcDrawApprovalRejectedOrApprovedNotification(drawApproval, ct);
                }

                if (isFactoringQuote)
                {
                    if (drawApprovalStatus == DrawApprovalStatus.Canceled)
                        await FactoringDrawApprovalCanceled(drawApproval, ct);

                    if (drawApprovalStatus == DrawApprovalStatus.Rejected)
                        await FactoringDrawApprovalRejected(drawApproval, drawApproval.StatusCode, ct);
                }

                try
                {
                    var messages = drawApproval.Payables
                        .Select(payable =>
                            new ServiceBusMessageBt<SyncInvoiceMessagePayload>(
                                new SyncInvoiceMessagePayload
                                {
                                    InvoiceId = payable.Id,
                                    SyncType = InvoiceSyncType.SyncDrawApprovalStatus,
                                    DrawApprovalStatus = drawApprovalStatus
                                }
                            )
                        )
                        .ToList();

                    if (messages.Any())
                    {
                        await invoiceSyncMessageSender.SendMessages(messages, ct);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Error during invoice synchronizations message sending, error message: {message}", ex.Message);
                }
            }
        }
        finally
        {
            await laSyncMessageSender.SendMessage(new ServiceBusMessageBt<SyncLoanApplicationMessagePayload>(new()
            {
                DrawApprovalId = drawApproval.Id,
                SyncType = SyncType.SyncDrawApproval
            }), ct);
        }
    }



    private async Task StartIssueLoan(DrawApprovalDocument drawApproval, LoanApplicationDocument? loanApplication, CancellationToken ct)
    {
        if (drawApproval.IsFactoring())
        {
            var isFactoringWithQuote = Enum.TryParse<LoanOrigin>(drawApproval.LoanOrigin, true, out var loanOrigin) &&
                loanOrigin is LoanOrigin.Factoring && IsQuote(drawApproval);

            if (!isFactoringWithQuote)
            {
                await integrationService.StartIssueLoanProcess(drawApproval, ct);
                await integrationService.SendIhcDrawApprovalRejectedOrApprovedNotification(drawApproval, ct);
            }
            else
            {
                await integrationService.StartHumanApprovalProcess(loanApplication, drawApproval, ct);
            }
        }
        else
        {
            await integrationService.StartHumanApprovalProcess(loanApplication, drawApproval, ct);
            await integrationService.SendUserApprovalNotification(loanApplication?.Id, drawApproval.Id, IsQuote(drawApproval), ct);
        }
    }

    private static void EnrichQueryWithAdditionalProperties(GetDrawApprovalsQueryWithPagination? query)
    {
        if (query is null) return;
        var types = query.Type?.ToList() ?? new List<string>();

        if (types.Exists(type => string.Equals(type, DrawApprovalType.Regular.ToString(), StringComparison.InvariantCultureIgnoreCase)))
            types.Add(DrawApprovalType.Custom.ToString());
        query!.Type = types.ToArray();
    }

    private async Task ValidateApproving(DrawApprovalDocument document, CancellationToken ct)
    {
        var invoicesIds = document.Payables.Select(x => x.Id).ToArray();

        var invoicesStatuses = (await nodeExternalService.GetInvoiceStatuses(new InvoiceStatusesRequest
        {
            InvoiceIds = invoicesIds.ToList()
        }, ct))?.ToList();

        var invoices = await invoiceExternalService.GetByIds(invoicesIds, ct);

        if (invoices?.Sum(x => x.TotalAmount) != document.DrawAmount)
            throw new ValidationException("The approval process cannot continue because the total amount of the invoices does not match the DrawAmount");

        if (invoicesStatuses!.Exists(x => string.Equals(x.Status, InvoiceStatusConstants.Cancelled, StringComparison.OrdinalIgnoreCase)))
            throw new ValidationException("The approval process cannot continue because one of the invoices has been canceled.");

        if (invoicesStatuses.Exists(x => string.Equals(x.Status, InvoiceStatusConstants.Paid, StringComparison.OrdinalIgnoreCase)
                                      || invoicesStatuses.Exists(x => string.Equals(x.Status, InvoiceStatusConstants.PaymentProcessing, StringComparison.OrdinalIgnoreCase))))
            throw new ValidationException("The approval process cannot continue because one of the invoices is in the process of being paid or has already been paid.");
    }

    private bool IsLoanApplicationQuote(LoanApplicationDocument? loanApplication) =>
        string.Equals(loanApplication?.Type, LoanApplicationConstant.QuoteType, StringComparison.OrdinalIgnoreCase);

    private static bool CanDrawApprovalStatusBeChanged(DrawApprovalDocument drawApproval, string? newStatus)
    {
        return !string.Equals(drawApproval.Status, newStatus, StringComparison.InvariantCultureIgnoreCase);
    }

    private static bool IsQuote(DrawApprovalDocument drawApproval)
    {
        return drawApproval.Payables.All(payable =>
                                       Enum.TryParse<InvoiceType>(payable.Type, true, out var invoiceType) &&
                                       invoiceType == InvoiceType.Quote);
    }

    private Task FactoringDrawApprovalCanceled(DrawApprovalDocument drawApprovalDocument, CancellationToken ct)
    {
        return integrationService.StartCancelLoanApplicationProcess(null, drawApprovalDocument.Id, ct, true);
    }

    private Task FactoringDrawApprovalRejected(DrawApprovalDocument drawApprovalDocument, string? reason, CancellationToken ct)
    {
        reason ??= string.Empty;

        return integrationService.StartRejectLoanProcess(null, drawApprovalDocument.Id, reason, ct);
    }

    private async Task FactoringDrawApprovalApproved(DrawApprovalDocument drawApprovalDocument, CancellationToken ct)
    {
        logger.LogInformation("Start creating Authorization Period for Draw Approval: {id}", drawApprovalDocument.Id);

        try
        {
            if (drawApprovalDocument.ArAdvanceAuthorizationPeriodId.IsNullOrEmpty())
            {
                var arAdvanceCreditHold = await CreateCreditHold(drawApprovalDocument, drawApprovalDocument.ArAdvanceCreditId!, ct);
                drawApprovalDocument.ArAdvanceAuthorizationPeriodId = arAdvanceCreditHold.Id.ToString();
            }

            if (drawApprovalDocument.AuthorizationPeriodId.IsNullOrEmpty())
            {
                var inHouseCreditHold = await CreateCreditHold(drawApprovalDocument, drawApprovalDocument.InHouseCreditId!, ct);
                drawApprovalDocument.AuthorizationPeriodId = inHouseCreditHold.Id.ToString();
            }

            logger.LogInformation("Updated draw approval document with authorization period ID");
        }
        catch (Exception)
        {
            logger.LogError("Creation of AuthorizationPeriod failed, drawApprovalId: {id}", drawApprovalDocument.Id);
        }
    }

    private async Task<AuthorizationPeriodDto> CreateCreditHold(DrawApprovalDocument drawApprovalDocument, string creditId, CancellationToken ct)
    {
        var createCreditHold = new CreateAuthorizationPeriodDto
        {
            CreditId = Guid.Parse(creditId!),
            CreditHoldAmount = drawApprovalDocument.CreditHoldAmount!.Value,
            StartDate = dateProvider.CurrentDateTime,
            EndDate = drawApprovalDocument.ExpirationDate!.Value,
            DrawApprovalId = drawApprovalDocument.Id,
        };

        var creditHold = await loanExternalService.CreateCreditHold(createCreditHold, ct);
        logger.LogInformation("Created authorization period with ID: {id}", creditHold.Id);
        return creditHold;
    }
}
