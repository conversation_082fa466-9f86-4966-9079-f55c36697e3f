﻿using Amazon.SimpleNotificationService.Model;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.RefreshDetectorService;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorTests;

public class AuthorizationDetailsRefreshServiceTests
{
    private readonly AuthorizationDetailsRefreshService _refreshService;

    private readonly Mock<IDecisionEngineExecutionService> _executionService;
    private readonly Mock<ILogger<AuthorizationDetailsRefreshService>> _logger;

    public AuthorizationDetailsRefreshServiceTests()
    {
        _executionService = new();
        _logger = new();

        _refreshService = new AuthorizationDetailsRefreshService(_executionService.Object, _logger.Object);
    }

    [Theory]
    [InlineData("ScheduledUpdate.businessChecks")]
    [InlineData("businessChecks")]
    public async Task Process_ValidEvent_ExecutesDecisionEngine(string eventType)
    {
        var eventBody = new ScheduledUpdateEvent()
        {
            EventType = eventType,
            Details = new ScheduledUpdateDetails()
            {
                ApprovedCreditApplicationId = "ApprovedCreditApplicationId"
            }
        };

        var act = async () => await _refreshService.Process(eventBody, default);

        await act.ShouldNotThrowAsync();
        _executionService.Verify(x => x.StartCreditApplicationInitializationStep(It.Is<CreditApplicationInitializationStepStartRequest>(
            request => request.CreditApplicationId == "ApprovedCreditApplicationId" && request.ScheduledUpdate == "businessChecks" && request.JobId != Guid.Empty), default), Times.Once);
    }

    [Theory]
    [InlineData("ScheduledUpdate.")]
    [InlineData(".")]
    public async Task Process_InvalidEventType_ThrowsValidationException(string eventType)
    {
        var eventBody = new ScheduledUpdateEvent()
        {
            BlueTapeCorrelationId = "correlationId",
            EventType = eventType,
            Details = new ScheduledUpdateDetails()
            {
                ApprovedCreditApplicationId = "ApprovedCreditApplicationId"
            }
        };

        var act = async () => await _refreshService.Process(eventBody, default);

        await act.ShouldThrowAsync<ValidationException>();
        _executionService.Verify(x => x.StartCreditApplicationInitializationStep(It.Is<CreditApplicationInitializationStepStartRequest>(
            request => request.CreditApplicationId == "ApprovedCreditApplicationId" &&
                             request.ScheduledUpdate == "businessChecks" &&
                             request.CorrelationId == eventBody.BlueTapeCorrelationId), default), Times.Never);
    }
}
