using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.User;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class UserServiceTests
{
    private readonly UserService _userService;
    private readonly Mock<IUserRepository> _userRepository = new();
    private readonly Mock<IUserRoleRepository> _userRoleRepository = new();
    private readonly Mock<ILogger<UserService>> _logger = new();

    public UserServiceTests()
    {
        var mapper = new Mapper(new MapperConfiguration(
            config => config.AddProfile(new ModelsProfile())
        ));
        _userService = new UserService(_userRepository.Object, _userRoleRepository.Object, mapper, _logger.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyId_ValidCompanyId_ShouldReturnValidResponse(string companyId, UserRoleDocument document, UserDocument userDocument)
    {
        _userRoleRepository.Setup(x => x.GetByCompanyId(companyId, default)).ReturnsAsync(document);
        _userRepository.Setup(x => x.GetBySub(document.Sub!, default)).ReturnsAsync(userDocument);

        var result = await _userService.GetByCompanyId(companyId, default);
        result!.Ip.ShouldBe(userDocument.Settings!.Ip);
        result.Sub.ShouldBe(document.Sub);
        result.Login.ShouldBe(userDocument.Login);

        _userRoleRepository.Verify(x => x.GetByCompanyId(companyId, default), Times.Once);
        _userRepository.Verify(x => x.GetBySub(document.Sub!, default), Times.Once);
    }


    [Theory, CustomAutoData]
    public async Task GetByCompanyId_UserRoleNotFound_ReturnsNull(string companyId)
    {
        _userRoleRepository.Setup(x => x.GetByCompanyId(companyId, default)).ReturnsAsync((UserRoleDocument?)null);

        var result = await _userService.GetByCompanyId(companyId, default);

        result.ShouldBeNull();
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyId_VUserNot_ReturnsNull(string companyId, UserRoleDocument document)
    {
        _userRoleRepository.Setup(x => x.GetByCompanyId(companyId, default)).ReturnsAsync(document);
        _userRepository.Setup(x => x.GetBySub(document.Sub!, default)).ReturnsAsync((UserDocument?)null);

        var result = await _userService.GetByCompanyId(companyId, default);

        result.ShouldBeNull();
    }
}