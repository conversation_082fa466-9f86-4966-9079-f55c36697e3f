﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Reports;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Services.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class ReportsServiceTests
{
    private readonly IReportsService _reportsService;

    private readonly Mock<ICreditApplicationService> _creditApplicationServiceMock = new();
    private readonly Mock<IEnumerable<StepDataRetrievingStrategyBase>> _stepDataRetrievingStrategies = new();
    private readonly Mock<InitializationStepDataRetrievingStrategy> _initializationSteps = new(new Mock<IDecisionEngineStepsRepository>().Object,
        new Mock<ICreditApplicationRepository>().Object,
        new Mock<IDecisionEngineStepsBviResultsService>().Object,
        new Mock<ICreditApplicationAuthorizationDetailsService>().Object);
    private readonly Mock<PreliminaryStepDataRetrievingStrategy> _preliminaryStep = new(new Mock<IDecisionEngineStepsRepository>().Object,
        new Mock<IDecisionEngineStepsBviResultsService>().Object,
        new Mock<ICreditApplicationAuthorizationDetailsService>().Object);
    private readonly Mock<ILogger<ReportsService>> _loggerMock = new();

    public ReportsServiceTests()
    {
        _reportsService = new ReportsService(_stepDataRetrievingStrategies.Object, _creditApplicationServiceMock.Object, _loggerMock.Object);
    }

    [Fact]
    public void RetrieveDataForReportsCreation_CreditApplicationNull_ThrowsValidationException()
    {
        _creditApplicationServiceMock.Setup(x => x.GetById(It.IsAny<string>(), default))!.ReturnsAsync((CreditApplication?)null);

        var act = () => _reportsService.RetrieveDataForReportsCreation(Guid.NewGuid().ToString(), default);

        act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public async Task RetrieveDataForReportsCreation_ValidData_ReturnsCreateStepModel(CreditApplication creditApplication, ReportStepDataModel reportStepDataModel)
    {
        reportStepDataModel.CreditApplicationId = creditApplication.Id; ;
        _creditApplicationServiceMock.Setup(x => x.GetById(It.IsAny<string>(), default))!.ReturnsAsync(creditApplication);
        _stepDataRetrievingStrategies.Setup(strategies => strategies.GetEnumerator()).Returns(new List<StepDataRetrievingStrategyBase>()
        {
            _initializationSteps.Object,
        }.GetEnumerator());
        _initializationSteps.Setup(x => x.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default))
            .ReturnsAsync(reportStepDataModel);
        var result = await _reportsService.RetrieveDataForReportsCreation(creditApplication.Id, default);

        result.ReportStepsData.First().CreditApplicationId.ShouldBe(creditApplication.Id);
        result.ReportStepsData.Length.ShouldBe(1);
    }

    [Fact]
    public void RetrieveExecutionDetails_CreditApplicationNull_ThrowsValidationException()
    {
        _creditApplicationServiceMock.Setup(x => x.GetById(It.IsAny<string>(), default))!.ReturnsAsync((CreditApplication?)null);

        var act = () => _reportsService.RetrieveExecutionDetails(Guid.NewGuid().ToString(), null, default);

        act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public async Task RetrieveExecutionDetails_StepNameIsNull_ReturnsStepsData(CreditApplication creditApplication,
        ReportStepDataModel initializationStepDataModel, ReportStepDataModel preliminaryStepDataModel)
    {
        initializationStepDataModel.CreditApplicationId = creditApplication.Id;
        preliminaryStepDataModel.CreditApplicationId = creditApplication.Id;
        _creditApplicationServiceMock.Setup(x => x.GetById(It.IsAny<string>(), default))!.ReturnsAsync(creditApplication);
        _stepDataRetrievingStrategies.Setup(strategies => strategies.GetEnumerator()).Returns(new List<StepDataRetrievingStrategyBase>()
        {
            _initializationSteps.Object,
            _preliminaryStep.Object,
        }.GetEnumerator());
        _initializationSteps.Setup(x => x.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default))
            .ReturnsAsync(initializationStepDataModel);
        _preliminaryStep.Setup(x => x.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default))
            .ReturnsAsync(preliminaryStepDataModel);
        var result = await _reportsService.RetrieveExecutionDetails(creditApplication.Id, null, default);

        result.Count().ShouldBe(2);
        result.All(x => x.CreditApplicationId == creditApplication.Id).ShouldBeTrue();
    }

    [Theory, CustomAutoData]
    public async Task RetrieveExecutionDetails_StepNameIsNotNull_ReturnsStepData(CreditApplication creditApplication,
        ReportStepDataModel initializationStepDataModel, ReportStepDataModel preliminaryStepDataModel)
    {
        initializationStepDataModel.CreditApplicationId = creditApplication.Id;
        initializationStepDataModel.StepName = StepName.Initialization;
        preliminaryStepDataModel.CreditApplicationId = creditApplication.Id;
        _creditApplicationServiceMock.Setup(x => x.GetById(It.IsAny<string>(), default))!.ReturnsAsync(creditApplication);
        _stepDataRetrievingStrategies.Setup(strategies => strategies.GetEnumerator()).Returns(new List<StepDataRetrievingStrategyBase>()
        {
            _initializationSteps.Object,
            _preliminaryStep.Object,
        }.GetEnumerator());
        _initializationSteps.Setup(x => x.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default))
            .ReturnsAsync(initializationStepDataModel);
        _preliminaryStep.Setup(x => x.CollectReportStepData(creditApplication.Id, creditApplication.CompanyId, default))
            .ReturnsAsync(preliminaryStepDataModel);
        var result = await _reportsService.RetrieveExecutionDetails(creditApplication.Id, StepName.Initialization, default);

        result.Count().ShouldBe(1);
        result.All(x => x.CreditApplicationId == creditApplication.Id).ShouldBeTrue();
    }
}
