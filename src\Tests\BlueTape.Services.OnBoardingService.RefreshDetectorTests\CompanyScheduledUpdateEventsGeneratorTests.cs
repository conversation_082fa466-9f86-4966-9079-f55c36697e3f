﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorTests;

public class CompanyScheduledUpdateEventsGeneratorTests
{
    private readonly Mock<IDateProvider> _mockDateProvider;
    private readonly Mock<ITraceIdAccessor> _mockTraceIdAccessor;
    private readonly Mock<ILogger<CompanyScheduledUpdateEventsGenerator>> _logger;
    private readonly CompanyScheduledUpdateEventsGenerator _generator;

    public CompanyScheduledUpdateEventsGeneratorTests()
    {
        _mockDateProvider = new();
        _mockTraceIdAccessor = new();
        _logger = new();
        _generator = new CompanyScheduledUpdateEventsGenerator(
            _mockDateProvider.Object,
            _mockTraceIdAccessor.Object,
            _logger.Object
        );
    }

    [Fact]
    public void GenerateScheduledUpdateEvents_ReturnsEmptyList_WhenNoQualifyingCreditApplications()
    {
        var company = new CompanyModel() { Id = "Company1" };
        var scheduledCheck = new RefreshCheckConfiguration
        {
            CreditApplicationTypes = new List<CreditApplicationType> { CreditApplicationType.LineOfCredit },
            StepsIncluded = new List<string> { "Step1" },
            FrequencyInDays = 30
        };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "1", CompanyId = "Company2", Type = "TypeB" }
        };

        var result = _generator.GenerateScheduledUpdateEvents(company, scheduledCheck, approvedCreditApplications, new List<DecisionEngineSteps>());

        result.ShouldBeEmpty();
    }

    [Fact]
    public void GenerateScheduledUpdateEvents_ReturnsEvent_WhenFrequencyExceededAndStepsIncluded()
    {
        var company = new CompanyModel { Id = "Company1" };
        var scheduledCheck = new RefreshCheckConfiguration
        {
            CreditApplicationTypes = new List<CreditApplicationType> { CreditApplicationType.LineOfCredit, CreditApplicationType.GetPaid },
            StepsIncluded = new List<string> { "Step1" },
            FrequencyInDays = 10
        };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1", Type = "GetPaid" }
        };
        var lastCompanyExecutions = new List<DecisionEngineSteps>
        {
            new() { Step = "Step1", UpdatedAt = DateTime.UtcNow.AddDays(-15) }
        };
        _mockDateProvider.Setup(dateProvider => dateProvider.CurrentDateTime).Returns(DateTime.UtcNow);
        _mockTraceIdAccessor.Setup(traceId => traceId.TraceId).Returns("TestCorrelationId");

        var result = _generator.GenerateScheduledUpdateEvents(company, scheduledCheck, approvedCreditApplications, lastCompanyExecutions);

        result.ShouldNotBeEmpty();
        result.Count.ShouldBe(1);
        result[0].MessageBody.BlueTapeCorrelationId.ShouldBe("TestCorrelationId");
        result[0].MessageBody.Details.ApprovedCreditApplicationId.ShouldBe("CreditApp1");
        result[0].MessageBody.Details.ScheduleMode.ShouldBe(ScheduleMode.CreateNew);
    }

    [Fact]
    public void GenerateScheduledUpdateEvents_ReturnsEmptyList_WhenFrequencyNotExceeded()
    {
        var company = new CompanyModel { Id = "Company1" };
        var scheduledCheck = new RefreshCheckConfiguration
        {
            CreditApplicationTypes = new List<CreditApplicationType> { CreditApplicationType.LineOfCredit },
            StepsIncluded = new List<string> { "Step1" },
            FrequencyInDays = 30
        };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1", Type = "LineOfCredit" }
        };
        var lastCompanyExecutions = new List<DecisionEngineSteps>
        {
            new() { Step = "Step1", UpdatedAt = DateTime.UtcNow.AddDays(-10) }
        };
        _mockDateProvider.Setup(dateProvider => dateProvider.CurrentDateTime).Returns(DateTime.UtcNow);

        var result = _generator.GenerateScheduledUpdateEvents(company, scheduledCheck, approvedCreditApplications, lastCompanyExecutions);

        result.ShouldBeEmpty();
    }
}