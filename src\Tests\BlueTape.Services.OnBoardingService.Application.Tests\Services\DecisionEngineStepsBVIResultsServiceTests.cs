﻿using AutoMapper;
using BlueTape.MongoDB.DTO.Base;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Models.IntegrationLogs;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class DecisionEngineStepsBVIResultsServiceTests
{
    private readonly DecisionEngineStepsBviResultsService _decisionEngineStepsBviResultsService;

    private readonly Mock<IDecisionEngineStepsBviResultsRepository> _decisionEngineStepsBviResultsRepositoryMock = new();
    private readonly Mock<IMapper> _mapperMock = new();

    public DecisionEngineStepsBVIResultsServiceTests()
    {
        _decisionEngineStepsBviResultsService = new DecisionEngineStepsBviResultsService(_decisionEngineStepsBviResultsRepositoryMock.Object, _mapperMock.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetLexisNexisRawData_ResponseExists_ReturnsFormattedResponse(string creditApplicationId, RequestResponseBaseModel requestResponseBaseModel, RequestResponseBaseDocument requestResponseBaseDocument)
    {
        var type = LexisNexisSourceType.Fraudpoint;
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetLexisNexisResponse(creditApplicationId, type, requestResponseBaseModel.Reference, default))
            .ReturnsAsync(requestResponseBaseDocument);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(requestResponseBaseDocument)).Returns(requestResponseBaseModel);

        var result = await _decisionEngineStepsBviResultsService.GetLexisNexisRawData(creditApplicationId, type, requestResponseBaseModel.Reference, default);

        result.ShouldNotBeNullOrEmpty();
    }

    [Theory, CustomAutoData]
    public async Task GetExperianRawData_ResponseExists_ReturnsFormattedResponse(string creditApplicationId, RequestResponseBaseModel requestResponseBaseModel, RequestResponseBaseDocument requestResponseBaseDocument)
    {
        var type = ExperianSourceType.BusinessBankruptcies;
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
                x.GetExperianResponse(creditApplicationId, type, default))
            .ReturnsAsync(requestResponseBaseDocument);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(requestResponseBaseDocument)).Returns(requestResponseBaseModel);

        var result = await _decisionEngineStepsBviResultsService.GetExperianRawData(creditApplicationId, type, default);

        result.ShouldNotBeNullOrEmpty();
    }

    [Theory, CustomAutoData]
    public async Task GetGiactRawData_ResponseExists_ReturnsFormattedResponse(string creditApplicationId,
        RequestResponseBaseModel requestResponseBaseModel,
        RequestResponseBaseDocument requestResponseBaseDocument)
    {
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
                x.GetGiactResponse(creditApplicationId, default))
            .ReturnsAsync(requestResponseBaseDocument);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(requestResponseBaseDocument)).Returns(requestResponseBaseModel);

        var result = await _decisionEngineStepsBviResultsService.GetGiactRawData(creditApplicationId, default);

        result.ShouldNotBeNullOrEmpty();
    }

    [Theory, CustomAutoData]
    public async Task AddRange_ValidData_ReturnsRange(List<CreateDecisionEngineStepsBviResultsModel> createModels, List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsDocument>>(createModels)).Returns(documents);
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x => x.AddRange(documents, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService.AddRange(createModels, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_FilterById_ReturnsById(List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetAllByFilters(id, null, null, null, null, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetAllByFilters(id, null, null, null, null, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_FilterByExecutionId_ReturnsByExecutionId(List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetAllByFilters(null, id, null, null, null, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetAllByFilters(null, id, null, null, null, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_FilterByLogId_ReturnsByLogId(List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetAllByFilters(null, null, id, null, null, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetAllByFilters(null, null, id, null, null, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_FilterByStepId_ReturnsByStepId(List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetAllByFilters(null, null, null, id, null, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetAllByFilters(null, null, null, id, null, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_FilterByCreditApplicationId_ReturnsByCreditApplicationId(List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetAllByFilters(null, null, null, null, id, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetAllByFilters(null, null, null, null, id, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_FilterByMultipleFilters_ReturnsMultipleFilters(List<DecisionEngineStepsBviResultsDocument> documents,
        List<DecisionEngineStepsBviResultsModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetAllByFilters(id, null, null, id, id, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetAllByFilters(id, null, null, id, id, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetBviResponsesByStepId_ValidStepId_ReturnsBviResponses(List<RequestResponseBaseDocument> documents,
        List<RequestResponseBaseModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x =>
            x.GetBviResponsesByStepId(id, default)).ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<IEnumerable<RequestResponseBaseModel?>>(documents)).Returns(models);

        var result = await _decisionEngineStepsBviResultsService
            .GetBviResponsesByStepId(id, default);

        result.ShouldBe(models);
    }

    [Theory, CustomAutoData]
    public async Task GetLexisNexisRawData_ResponseExists_ReturnsResponseModel(string creditApplicationId,
        RequestResponseBaseDocument? requestResponseBaseDocument, RequestResponseBaseModel? requestResponseBaseModel)
    {
        var type = LexisNexisSourceType.Fraudpoint;

        _decisionEngineStepsBviResultsRepositoryMock.Setup(x => x.GetLexisNexisResponse(creditApplicationId, type, requestResponseBaseModel.Reference, default))
            .ReturnsAsync(requestResponseBaseDocument);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(requestResponseBaseDocument))
            .Returns(requestResponseBaseModel);

        var result = await _decisionEngineStepsBviResultsService.GetLexisNexisResponse(creditApplicationId, type, requestResponseBaseModel?.Reference, default);

        result.ShouldBe(requestResponseBaseModel);
    }

    [Theory, CustomAutoData]
    public async Task GetExperianRawData_ResponseExists_ReturnsResponseModel(string creditApplicationId,
        RequestResponseBaseDocument? requestResponseBaseDocument, RequestResponseBaseModel? requestResponseBaseModel)
    {
        var type = ExperianSourceType.BusinessTrades;

        _decisionEngineStepsBviResultsRepositoryMock.Setup(x => x.GetExperianResponse(creditApplicationId, type, default))
            .ReturnsAsync(requestResponseBaseDocument);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(requestResponseBaseDocument))
            .Returns(requestResponseBaseModel);

        var result = await _decisionEngineStepsBviResultsService.GetExperianResponse(creditApplicationId, type, default);

        result.ShouldBe(requestResponseBaseModel);
    }

    [Theory, CustomAutoData]
    public async Task GetGiactRawData_ResponseExists_ReturnsResponseModel(string creditApplicationId,
        RequestResponseBaseDocument? requestResponseBaseDocument, RequestResponseBaseModel? requestResponseBaseModel)
    {
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x => x.GetGiactResponse(creditApplicationId, default))
            .ReturnsAsync(requestResponseBaseDocument);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(requestResponseBaseDocument))
            .Returns(requestResponseBaseModel);

        var result = await _decisionEngineStepsBviResultsService.GetGiactResponse(creditApplicationId, default);

        result.ShouldBe(requestResponseBaseModel);
    }

    [Theory, CustomAutoData]
    public async Task GetGiactRawData_ResponseDoesNotExists_ReturnsNull(string creditApplicationId)
    {
        _decisionEngineStepsBviResultsRepositoryMock.Setup(x => x.GetGiactResponse(creditApplicationId, default))
            .ReturnsAsync((RequestResponseBaseDocument?)null);
        _mapperMock.Setup(x => x.Map<RequestResponseBaseModel?>(null))
            .Returns((RequestResponseBaseModel?)null);

        var result = await _decisionEngineStepsBviResultsService.GetGiactResponse(creditApplicationId, default);

        result.ShouldBeNull();
    }
}