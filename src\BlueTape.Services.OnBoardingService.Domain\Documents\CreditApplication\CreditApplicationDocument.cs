﻿using BlueTape.MongoDB.Attributes;
using BlueTape.Services.OnBoardingService.Domain.Documents.CustomerAccount;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;

[BsonIgnoreExtraElements]
[MongoCollection("creditApplications")]
public class CreditApplicationDocument : Document
{
    [BsonElement("createdBy")]
    public string? CreatedBy { get; set; }

    [BsonElement("updatedBy")]
    public string? UpdatedBy { get; set; }

    [BsonElement("companyId")]
    public string? CompanyId { get; set; }

    [BsonElement("einHash")]
    public string? EinHash { get; set; }

    [BsonElement("draftId")]
    public string? DraftId { get; set; }

    [BsonElement("applicationDate")]
    public DateTime? ApplicationDate { get; set; }

    [BsonElement("type")]
    public string? Type { get; set; }

    [BsonElement("isARAdvanceRequested")]
    public bool? IsArAdvanceRequested { get; set; }

    [BsonElement("arAdvanceApplicationId")]
    public string? ArAdvanceApplicationId { get; set; }

    [BsonElement("merchantId")]
    public string? MerchantId { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("purchaseTypeOption")]
    public string? PurchaseTypeOption { get; set; }

    [BsonElement("lastStatusChangedAt")]
    public DateTime? LastStatusChangedAt { get; set; }

    [BsonElement("lastStatusChangedBy")]
    public string? LastStatusChangedBy { get; set; }

    [BsonElement("creditLimit")]
    public decimal? CreditLimit { get; set; }

    [BsonElement("approvedCreditLimit")]
    public decimal? ApprovedCreditLimit { get; set; }

    [BsonElement("businessName")]
    public string? BusinessName { get; set; }

    [BsonElement("businessDba")]
    public string? BusinessDba { get; set; }

    [BsonElement("businessCategory")]
    public string? BusinessCategory { get; set; }

    [BsonElement("applicantName")]
    public string? ApplicantName { get; set; }

    [BsonElement("supplierName")]
    public string? SupplierName { get; set; }

    [BsonElement("requestedAmount")]
    public decimal? RequestedAmount { get; set; }

    [BsonElement("automatedDecisionResult")]
    public string? AutomatedDecisionResult { get; set; }

    [BsonElement("executionId")]
    public string? ExecutionId { get; set; }

    [BsonElement("revenueFallPercentage")]
    public double? RevenueFallPercentage { get; set; }

    [BsonElement("approvedAt")]
    public DateTime? ApprovedAt { get; set; }

    [BsonElement("approvedBy")]
    public string? ApprovedBy { get; set; }

    [BsonElement("rejectedAt")]
    public DateTime? RejectedAt { get; set; }

    [BsonElement("rejectedBy")]
    public string? RejectedBy { get; set; }

    [BsonElement("canceledAt")]
    public DateTime? CanceledAt { get; set; }

    [BsonElement("canceledBy")]
    public string? CanceledBy { get; set; }

    [BsonElement("statusCode")]
    public string? StatusCode { get; set; }

    [BsonElement("statusNote")]
    public string? StatusNote { get; set; }

    [BsonElement("bankAccountType")]
    public string? BankAccountType { get; set; }

    [BsonElement("merchantSettings")]
    public MerchantSettingsDocument? MerchantSettings { get; set; }

    [BsonElement("isInHouseCreditEnabled")]
    [BsonIgnoreIfNull]
    public bool? IsInHouseCreditEnabled { get; set; }

    [BsonElement("customerAccount")]
    public CustomerAccountDocument? CustomerAccount { get; set; }

    [BsonElement("ssnHashes")]
    [BsonIgnoreIfNull]
    public string[]? SsnHashes { get; set; }

    [BsonElement("shouldIgnoreCaching")]
    [BsonIgnoreIfNull]
    public bool? ShouldIgnoreCaching { get; set; }

    [BsonElement("isSecured")]
    public bool? IsSecured { get; set; }

    [BsonElement("depositAmount")]
    public decimal? DepositAmount { get; set; }
}