﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;

public class AddressDocument
{
    [BsonElement("address")]
    public string? Address { get; set; }

    [BsonElement("city")]
    public string? City { get; set; }

    [BsonElement("state")]
    public string? State { get; set; }

    [BsonElement("zip")]
    public string? Zip { get; set; } 

    [BsonElement("unitNumber")]
    public string? UnitNumber { get; set; }
}
