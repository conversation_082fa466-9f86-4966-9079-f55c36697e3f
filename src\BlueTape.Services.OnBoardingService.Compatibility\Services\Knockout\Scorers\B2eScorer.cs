﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Extensions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class B2EScorer(IConfiguration config) : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        int passThreshold = config.GetValue<int?>("ScoringThresholds:BusinessToExecThresholdPass") ?? 30;
        var result = new List<OwnerScore>();

        if (kyb?.KYC != null && kyb.KYC.Any())
        {
            foreach (var item in kyb.KYC)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                int.TryParse(item.B2E, out var b2e);

                ownerScore.Scores?.Add(KnockoutCalculationExtension.Calculate("b2eLink", b2e, passThreshold));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = KnockoutCalculationExtension.Calculate("b2eLink", kyb?.B2E, passThreshold);
        return [new OwnerScore { Scores = [score] }];
    }
}
