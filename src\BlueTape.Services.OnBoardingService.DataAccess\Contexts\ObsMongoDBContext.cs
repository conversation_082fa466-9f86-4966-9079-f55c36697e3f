﻿using BlueTape.MongoDB;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.DataAccess.Contexts
{
    public class ObsMongoDBContext : MongoAzDbContext, IObsMongoDBContext
    {
        public ObsMongoDBContext(
            IConfiguration keyVaultService,
            ILogger<MongoAzDbContext> logger) : base(keyVaultService, logger)
        {
        }
    }
}
