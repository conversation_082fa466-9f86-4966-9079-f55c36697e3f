﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Draft;

[BsonIgnoreExtraElements]
public class DataDocument
{
    [BsonElement("businessInfo")]
    public BusinessInfoDocument? BusinessInfo { get; set; }

    [BsonElement("businessOwner")]
    public BusinessOwnerDocument? BusinessOwner { get; set; }

    [BsonElement("finance")]
    public FinanceDocument? Finance { get; set; }

    [BsonElement("bank")]
    public BankDocument? Bank { get; set; }

    [BsonElement("coOwnerInfo")]
    public CoOwnerInfoDocument? CoOwnerInfo { get; set; }

    [BsonElement("personalInfo")]
    public PersonalInfoDocument? PersonalInfo { get; set; }

    [BsonElement("business")]
    public BusinessDocument? Business { get; set; }

    [BsonElement("ownership")]
    public OwnershipDocument? Ownership { get; set; }

    [BsonElement("registered")]
    public RegisteredDocument? Registered { get; set; }
}