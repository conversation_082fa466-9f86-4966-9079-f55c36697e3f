﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Extensions;
using BlueTape.Services.OnBoardingService.DataAccess.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.OnBoardingService.DataAccess.DI;

public static class DependencyRegistrar
{
    public static void AddDataAccessDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IObsMongoDBContext, ObsMongoDBContext>();

        services.AddScoped<IDraftRepository, DraftRepository>();
        services.AddScoped<ICreditApplicationRepository, CreditApplicationRepository>();
        services.AddScoped<IAccountAuthorizationsRepository, AccountAuthorizationsRepository>();
        services.AddScoped<IDecisionEngineStepsRepository, DecisionEngineStepsRepository>();
        services.AddScoped<IAccountAuthorizationsChangesRepository, AccountAuthorizationsChangesRepository>();
        services.AddScoped<IDecisionEngineStepsBviResultsRepository, DecisionEngineStepsBviResultsRepository>();
        services.AddScoped<ICreditApplicationNotesRepository, CreditApplicationNotesRepository>();
        services.AddScoped<ICreditApplicationAuthorizationDetailsRepository, CreditApplicationAuthorizationDetailsRepository>();
        services.AddScoped<IDrawApprovalRepository, DrawApprovalRepository>();
        services.AddScoped<IDrawApprovalNotesRepository, DrawApprovalNotesRepository>();
        services.AddScoped<ILoanPaymentPlanRepository, LoanPaymentPlanRepository>();
        services.AddScoped<ILoanApplicationRepository, LoanApplicationRepository>();
        services.AddScoped<ISettingsRepository, SettingsRepository>();
        services.AddScoped<IUserRoleRepository, UserRoleRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<ICustomerAccountRepository, CustomerAccountRepository>();
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();
        services.AddScoped<ICompanyRepository, CompanyRepository>();
        services.AddScoped<ICardPricingPackageRepository, CardPricingPackageRepository>();
        services.AddScoped<ILoanPricingPackageRepository, LoanPricingPackageRepository>();
        services.AddScoped<IParsedDraftRepository, ParsedDraftRepository>();
        services.AddScoped<IAuthorizationDetailsRefreshConfigurationRepository, AuthorizationDetailsRefreshConfigurationRepository>();
        services.AddNodeBlueTapeServiceDependencies(configuration);
    }
}
