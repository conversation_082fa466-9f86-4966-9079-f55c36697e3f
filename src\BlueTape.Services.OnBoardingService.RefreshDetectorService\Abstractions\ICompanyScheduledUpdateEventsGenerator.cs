﻿using BlueTape.CompanyService.Companies;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;

public interface ICompanyScheduledUpdateEventsGenerator
{
    IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> GenerateScheduledUpdateEvents(
        CompanyModel company,
        RefreshCheckConfiguration scheduledCheck,
        IReadOnlyList<LightCreditApplicationDocument> approvedCreditApplications,
        IReadOnlyList<DecisionEngineSteps> lastCompanyExecutions);
}
