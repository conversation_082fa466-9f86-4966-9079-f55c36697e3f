﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.UpdateAccountAuthorization;

public class OwnersDetailsUpdateDtoValidator : AbstractValidator<OwnersDetailsUpdateDto>
{
    public OwnersDetailsUpdateDtoValidator()
    {
        RuleFor(x => x.Identifier).NotEmpty();
        RuleFor(x => x.PercentOwned).NotEmpty();
        RuleFor(x => x.Birthday).NotEmpty();
        RuleFor(x => x.SsnHash).NotEmpty();
        RuleFor(x => x.FirstName).NotEmpty();
        RuleFor(x => x.LastName).NotEmpty();
        RuleFor(x => x.Address).NotEmpty();
        RuleFor(x => x.IsPrincipal).NotEmpty();
    }
}