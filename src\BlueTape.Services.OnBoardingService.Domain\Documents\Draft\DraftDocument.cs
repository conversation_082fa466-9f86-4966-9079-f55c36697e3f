﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Draft;

[BsonIgnoreExtraElements]
[MongoCollection("drafts")]
public class DraftDocument : Document
{
    [BsonElement("sub")]
    public string? Sub { get; set; }

    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("type")]
    public string? Type { get; set; }

    [BsonElement("data")]
    public DataDocument? Data { get; set; }

    [BsonElement("current")]
    public string? Current { get; set; }

    [BsonElement("filled")]
    public IEnumerable<string>? Filled { get; set; }

    [BsonElement("loanApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("submitDate")]
    public DateTime? SubmitDate { get; set; }

    [BsonElement("normalized")]
    public bool? Normalized { get; set; }

    [BsonElement("businessOwner_ssn")]
    public object? BusinessOwnerSsn { get; set; }

    [BsonElement("businessInfo_ein")]
    public object BusinessInfoEin { get; set; }
}