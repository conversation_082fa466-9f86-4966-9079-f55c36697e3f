﻿using MongoDB.Bson;
using MongoDB.Driver;
using System.Linq.Expressions;
using System.Text.RegularExpressions;

namespace BlueTape.Services.OnBoardingService.DataAccess.Extensions;

public static class FiltersExtensions
{
    public static BsonRegularExpression? BuildRegexDefinition(this string? searchString)
    {
        var nameRegex = string.IsNullOrWhiteSpace(searchString)
            ? null
            : new BsonRegularExpression(".*" + searchString, "i");
        return nameRegex;
    }

    public static List<FilterDefinition<T>> ConvertToCaseInsensitiveRegexFiltersList<T>(this IEnumerable<string> source, Expression<Func<T, string>> field)
    {
        var expFieldDef = new ExpressionFieldDefinition<T>(field);

        return source.Select(range => Builders<T>.Filter.Regex(expFieldDef, new BsonRegularExpression(range, "i"))).ToList();
    }


    public static FilterDefinition<T> ToCaseInsensitiveRegexFilter<T>(this string source, Expression<Func<T, string>> field)
    {
        var expFieldDef = new ExpressionFieldDefinition<T>(field);

        return Builders<T>.Filter.Regex(expFieldDef, new BsonRegularExpression(source, "i"));
    }

    public static FilterDefinition<T> ConvertToEqualsFiltersList<T>(this IEnumerable<string> source, Expression<Func<T, string>> field)
    {
        return Builders<T>.Filter.In(field, source);
    }

    public static FilterDefinition<T> ToCaseInsensitiveRegexFilterForCollection<T>(
        this IEnumerable<string> source,
        Expression<Func<T, string>> field)
    {
        var expFieldDef = new ExpressionFieldDefinition<T>(field);
        var regexFilters = source.Select(range => Builders<T>.Filter.Regex(expFieldDef, new BsonRegularExpression(range, "i"))).ToList();
        return Builders<T>.Filter.Or(regexFilters);
    }
}