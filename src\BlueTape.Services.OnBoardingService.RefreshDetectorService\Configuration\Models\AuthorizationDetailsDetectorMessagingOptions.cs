﻿namespace BlueTape.Services.OnBoardingService.RefreshDetectorService.Configuration.Models;

public class AuthorizationDetailsDetectorMessagingOptions
{
    public int ScheduledPeriodDurationBetweenMessagesInMinutes { get; set; }
    public int MaxSimultaneouslySentMessagesCount { get; set; }
    public int MaxMessagesInBatchCount { get; set; }
    public int MaxCompaniesRefreshedCount { get; set; }
}
