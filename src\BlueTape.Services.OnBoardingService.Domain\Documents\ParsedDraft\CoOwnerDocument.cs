﻿using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.ParsedDraft;

[BsonIgnoreExtraElements]
public class CoOwnerDocument : AddressDocument
{
    [BsonElement("id")]
    public string Id { get; set; } = null!;

    [BsonElement("identifier")]
    public string? Identifier { get; set; }

    [BsonElement("phone")]
    public string Phone { get; set; } = null!;

    [BsonElement("firstName")]
    public string FirstName { get; set; } = null!;

    [BsonElement("lastName")]
    public string LastName { get; set; } = null!;

    [BsonElement("email")]
    public string Email { get; set; } = null!;

    [BsonElement("birthday")]
    public string BirthDate { get; set; } = null!;

    [BsonElement("ssn")]
    public CipherDocument? Ssn { get; set; }

    [BsonElement("type")]
    public string Type { get; set; } = null!;

    [BsonElement("percentOwned")]
    public int PercentOwned { get; set; }

    [BsonElement("isPrincipal")]
    public bool? IsPrincipal { get; set; }

    [BsonElement("entityName")]
    public string? EntityName { get; set; }

    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("ein")]
    public CipherDocument? Ein { get; set; }

}
