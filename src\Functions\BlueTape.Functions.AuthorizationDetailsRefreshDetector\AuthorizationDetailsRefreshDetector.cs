using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Functions.AuthorizationDetailsRefreshDetector;

[ExcludeFromCodeCoverage]
public class AuthorizationDetailsRefreshDetector(
    ILogger<AuthorizationDetailsRefreshDetector> logger,
    ITraceIdAccessor traceIdAccessor,
    IAuthorizationDetailsRefreshDetectorService detector,
    ISlackNotificationService notificationService)
{
    [Function(nameof(AuthorizationDetailsRefreshDetector))]
    public async Task Run([TimerTrigger("0 0 6 * * *")] TimerInfo myTimer, CancellationToken ctx)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(AuthorizationDetailsRefreshDetector)}";

        using (GlobalLogContext.PushProperty("functionName", nameof(AuthorizationDetailsRefreshDetector)))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", traceIdAccessor.TraceId))
        {
            logger.LogInformation("Authorization Details Refresh Detector: started function execution");

            try
            {
                await detector.Run(ctx);

                logger.LogInformation("Authorization Details Refresh Detector: successfully finished function execution");
            }
            catch (Exception ex)
            {
                logger.LogError("Authorization Details Refresh Detector: got exception occurred while execution, \n  Exception: {ex}", ex);
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "OnBoardingService"), traceIdAccessor.TraceId, ctx);
                throw;
            }
        }

    }
}
