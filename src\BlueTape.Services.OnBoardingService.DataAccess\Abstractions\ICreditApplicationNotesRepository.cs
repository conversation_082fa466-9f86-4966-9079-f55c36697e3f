using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationNotes;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

public interface ICreditApplicationNotesRepository : IGenericRepository<CreditApplicationNoteDocument>
{
    Task<IEnumerable<CreditApplicationNoteDocument>> GetByApplicationId(string id, CancellationToken ct);
    Task SoftDelete(string id, string userId, CancellationToken ct);
}
