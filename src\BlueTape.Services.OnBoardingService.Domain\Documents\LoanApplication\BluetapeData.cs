﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class BluetapeData
{
    [BsonElement("failed_applications")]
    public double? FailedApplications { get; set; }

    [BsonElement("defaulted")]
    public double? Defaulted { get; set; }

    [BsonElement("current_late")]
    public double? CurrentLate { get; set; }

    [BsonElement("aggregate_credit_line")]
    public double? AggregateCreditLine { get; set; }

    [BsonElement("business_outstanding_balance")]
    public double? BusinessOutstandingBalance { get; set; }

    [BsonElement("principal_outstanding_balance")]
    public double? PrincipalOutstandingBalance { get; set; }
    /*
    [BsonElement("einMatches")]
    public List<BsonDocument>? EinMatches { get; set; }

    [BsonElement("ssnMatches")]
    public List<BsonDocument>? SsnMatches { get; set; }
    */
    [BsonElement("rejected_applications_matching_ssn_ein")]
    public double? RejectedApplicationsMatchingSsnEin { get; set; }
}
