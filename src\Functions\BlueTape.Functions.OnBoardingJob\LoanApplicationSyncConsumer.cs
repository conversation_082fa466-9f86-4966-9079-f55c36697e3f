using Azure.Messaging.ServiceBus;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using System.Text.Json.Serialization;
using EnvironmentExtensions = BlueTape.Common.ExceptionHandling.Extensions.EnvironmentExtensions;

namespace BlueTape.Functions.OnBoardingJob;

[ExcludeFromCodeCoverage]
public class LoanApplicationSyncConsumer(
    ILogger<LoanApplicationSyncConsumer> logger,
    ISlackNotificationService notificationService,
    ICompatibilityService compatibilityService,
    ITraceIdAccessor traceIdAccessor)
{
    private readonly JsonSerializerOptions _serializerOptions = new(JsonSerializerDefaults.Web)
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
    };

    [Function(nameof(LoanApplicationSyncConsumer))]
    public async Task Run([
            ServiceBusTrigger($"%{InfrastructureConstants.LoanApplicationSyncQueueName}%",
            Connection = $"{InfrastructureConstants.LoanApplicationSyncQueueConnectionString}")]
        ServiceBusReceivedMessage message,
        ServiceBusMessageActions messageActions,
        CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(LoanApplicationSyncConsumer)}";

        using (GlobalLogContext.PushProperty("functionName", nameof(LoanApplicationSyncConsumer)))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", traceIdAccessor.TraceId))
        {
            logger.LogInformation("Compatibility function LoanApplicationSyncConsumer, got message from the queue: Session: {sessionId}, \n Message: {messageId}. Body: {@message}", message.SessionId, message.MessageId, message.Body);

            try
            {
                var parsedMessage = message.Body.ToObjectFromJson<SyncLoanApplicationMessagePayload?>(_serializerOptions);

                if (parsedMessage is null)
                {
                    logger.LogError(
                        "Compatibility function LoanApplicationSyncConsumer, unable to process message with empty body or invalid fields. Session: {sessionId}, \n Message: {messageId}",
                        message.SessionId, message.MessageId);
                    throw new ArgumentNullException(nameof(message));
                }

                logger.LogInformation("Compatibility function LoanApplicationSyncConsumer, started operation sync for creditApplicationId: {creditApplicationId}, drawApprovalId: {drawApprovalId}, syncType: {syncType}",
                    parsedMessage.CreditApplicationId, parsedMessage.DrawApprovalId, parsedMessage.SyncType);


                await (parsedMessage.SyncType switch
                {
                    SyncType.SyncCreditApplication => compatibilityService.SyncCreditApplication(parsedMessage.CreditApplicationId, ct),
                    SyncType.SyncDrawApproval => compatibilityService.SyncDrawApproval(parsedMessage.DrawApprovalId, ct),
                    _ => throw new ArgumentOutOfRangeException(nameof(parsedMessage.SyncType))
                });

                logger.LogInformation("Compatibility function LoanApplicationSyncConsumer, loan application sync for creditApplicationId: {creditApplicationId}, drawApprovalId: {drawApprovalId}, syncType: {syncType} was processed successfully",
                    parsedMessage.CreditApplicationId, parsedMessage.DrawApprovalId, parsedMessage.SyncType);
            }
            catch (Exception ex)
            {
                logger.LogError("Compatibility function LoanApplicationSyncConsumer got exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "OnBoardingService"), traceIdAccessor.TraceId, ct);
                throw;
            }
        }
    }
}
