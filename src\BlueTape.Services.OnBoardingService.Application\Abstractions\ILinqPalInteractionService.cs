﻿using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface ILinqPalInteractionService
{
    Task SendUserApprovalNotification(string loanApplicationId, string drawApprovalId, bool isQuote, CancellationToken ctx);
    Task StartRejectLoanProcess(LoanApplicationDocument? loanApplication, string? drawApprovalId, string reason, CancellationToken ctx);
    Task SendOpsTeamNotification(LoanApplicationDocument? loanApplication, CancellationToken ctx);
    Task SendSentBackUserNotification(LoanApplicationDocument loanApplication, CancellationToken ctx);
    Task StartHumanApprovalProcess(LoanApplicationDocument? loanApplication, DrawApprovalDocument drawApproval, CancellationToken ctx);
    Task StartIssueLoanProcess(DrawApprovalDocument drawApproval, CancellationToken ctx);
    Task StartIssueLoanPrequalifiedProcess(LoanApplicationDocument loanApplication, decimal approvedAmount, CancellationToken ctx);
    Task SendLoanCancellationCustomerNotification(LoanApplicationDocument loanApplication, CancellationToken ctx);
    Task StartCancelLoanApplicationProcess(LoanApplicationDocument? loanApplication, string? drawApprovalId, CancellationToken ctx, bool notify = false);
    Task SendIhcDrawApprovalRejectedOrApprovedNotification(DrawApprovalDocument document, CancellationToken ct);
}
