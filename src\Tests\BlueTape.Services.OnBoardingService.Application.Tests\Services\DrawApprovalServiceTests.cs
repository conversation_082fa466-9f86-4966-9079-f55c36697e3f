﻿using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Credit;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.Compatibility;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.PaymentPlan;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;
using CreditStatus = BlueTape.LS.Domain.Enums.CreditStatus;
using VariableNullException = BlueTape.Common.ExceptionHandling.Exceptions.VariableNullException;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class DrawApprovalServiceTests
{
    private readonly DrawApprovalService _drawApprovalService;

    private readonly Mock<IDrawApprovalRepository> _drawApprovalRepositoryMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();
    private readonly Mock<ILoanApplicationRepository> _loanApplicationRepositoryMock = new();
    private readonly Mock<ILoanPaymentPlanRepository> _paymentPlanRepositoryMock = new();
    private readonly Mock<IInvoiceExternalService> _invoiceExternalServiceMock = new();
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepository = new();
    private readonly Mock<ICompanyExternalService> _companyExternalServiceMock = new();
    private readonly Mock<IDraftService> _draftServiceMock = new();
    private readonly Mock<ILoanApplicationSyncMessageSender> _laSyncMessageSender = new();
    private readonly Mock<IInvoiceSyncMessageSender> _invoiceSyncMessageSender = new();
    private readonly Mock<ILoanApplicationRepository> _loanAppRepo = new();
    private readonly Mock<INodeExternalService> _nodeExternalService = new();
    private readonly Mock<IDrawApprovalNotesService> _noteService = new();
    private readonly Mock<ILogger<DrawApprovalService>> _loggerMock = new();
    private readonly Mock<IDecisionEngineExecutionService> _decisionEngineExecutionServiceMock = new();
    private readonly Mock<ILoanExternalService> _loanExternalServiceMock = new();
    private readonly Mock<ILinqPalInteractionService> _integrationServiceMock = new();

    public DrawApprovalServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        IMapper mapper = new Mapper(mapperConfig);
        _drawApprovalService = new DrawApprovalService(
            _drawApprovalRepositoryMock.Object,
            _creditApplicationRepository.Object,
            _loanApplicationRepositoryMock.Object,
            mapper,
            _dateProviderMock.Object,
            _paymentPlanRepositoryMock.Object,
            _invoiceExternalServiceMock.Object,
            _companyExternalServiceMock.Object,
            _draftServiceMock.Object,
            _laSyncMessageSender.Object,
            _invoiceSyncMessageSender.Object,
            _loggerMock.Object,
            _nodeExternalService.Object,
            _noteService.Object,
            _decisionEngineExecutionServiceMock.Object,
            _loanExternalServiceMock.Object,
            _integrationServiceMock.Object
            );
    }

    [Theory, CustomAutoData]
    public async Task Create_ValidRequest_CreatesDrawApproval(CreateDrawApproval createDrawApproval,
        List<PayableItem> payables, LoanPaymentPlanDocument paymentPlan, InvoiceModel invoice,
        CompanyModel merchant, CompanyModel company, Draft draft)
    {
        var utcNow = DateTime.UtcNow;
        createDrawApproval.Payables = payables;
        var drawAmount = payables.Sum(x => x.Amount);
        _paymentPlanRepositoryMock.Setup(x => x.GetById(createDrawApproval.PaymentPlanId, CancellationToken.None)).ReturnsAsync(paymentPlan);
        _invoiceExternalServiceMock.Setup(x => x.GetById(payables[0].Id, CancellationToken.None)).ReturnsAsync(invoice);
        _companyExternalServiceMock.Setup(x => x.GetById(createDrawApproval.CompanyId, CancellationToken.None)).ReturnsAsync(company);
        _companyExternalServiceMock.Setup(x => x.GetById(createDrawApproval.MerchantId!, CancellationToken.None)).ReturnsAsync(merchant);
        _drawApprovalRepositoryMock.Setup(x => x.Add(It.IsAny<DrawApprovalDocument>(), CancellationToken.None))
            .ReturnsAsync((DrawApprovalDocument x, CancellationToken _) => x);

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(utcNow);
        _draftServiceMock.Setup(x => x.GetAllByFilters(new DraftFilter()
        {
            CompanyId = merchant.Id
        }, CancellationToken.None)).ReturnsAsync(new List<Draft>()
        {
            draft
        });

        await _drawApprovalService.Create(createDrawApproval, CancellationToken.None);

        _drawApprovalRepositoryMock.Verify(x => x.Add(It.Is<DrawApprovalDocument>(
            x => x.DrawAmount == drawAmount
                 && x.CreditId == createDrawApproval.CreditId
                 && x.CreatedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.Status == DrawApprovalStatus.New.ToString()
                 && x.Type == paymentPlan.Type
                 && x.LastStatusChangedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.MerchantId == createDrawApproval.MerchantId
                 && x.ApplicationDate == utcNow
                 && x.LastStatusChangedAt == utcNow
                 && x.CompanyName == $"{company.LegalName}"
                 && x.MerchantName == $"{merchant.LegalName}"
                 && x.NoSupplierDetails!.BusinessName == createDrawApproval.NoSupplierDetails!.BusinessName
                 && x.NoSupplierDetails.ContactName == createDrawApproval.NoSupplierDetails!.ContactName
                 && x.NoSupplierDetails.Address!.City == createDrawApproval.NoSupplierDetails!.Address!.City
                 && x.NoSupplierDetails.Address.State == createDrawApproval.NoSupplierDetails!.Address!.State
                 && x.NoSupplierDetails.Address.Address == createDrawApproval.NoSupplierDetails!.Address!.Address
                 && x.NoSupplierDetails.BankDetails!.RoutingNumber == createDrawApproval.NoSupplierDetails!.BankDetails!.RoutingNumber)
            , CancellationToken.None), Times.Once);

        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(),
            CancellationToken.None), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Create_ValidRequestCreditApplicationIdNull_CreatesDrawApproval(CreateDrawApproval createDrawApproval,
        List<PayableItem> payables, LoanPaymentPlanDocument paymentPlan, InvoiceModel invoice, CompanyModel merchant, CompanyModel company, Draft draft,
        CreditApplicationDocument creditApplication)
    {
        var utcNow = DateTime.UtcNow;
        createDrawApproval.Payables = payables;
        createDrawApproval.CreditApplicationId = null;
        createDrawApproval.LoanOrigin = LoanOrigin.Express;
        var drawAmount = payables.Sum(x => x.Amount);
        _paymentPlanRepositoryMock.Setup(x => x.GetById(createDrawApproval.PaymentPlanId, default)).ReturnsAsync(paymentPlan);
        _invoiceExternalServiceMock.Setup(x => x.GetById(payables[0].Id, default)).ReturnsAsync(invoice);
        _companyExternalServiceMock.Setup(x => x.GetById(createDrawApproval.MerchantId!, default)).ReturnsAsync(merchant);
        _companyExternalServiceMock.Setup(x => x.GetById(createDrawApproval.CompanyId, default)).ReturnsAsync(company);
        _drawApprovalRepositoryMock.Setup(x => x.Add(It.IsAny<DrawApprovalDocument>(), CancellationToken.None))
            .ReturnsAsync((DrawApprovalDocument x, CancellationToken _) => x);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(utcNow);
        _draftServiceMock.Setup(x => x.GetAllByFilters(new DraftFilter()
        {
            CompanyId = merchant.Id
        }, default)).ReturnsAsync(new List<Draft>()
        {
            draft
        });
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default))
            .ReturnsAsync(new[]
            {
                creditApplication
            });

        await _drawApprovalService.Create(createDrawApproval, default);

        _drawApprovalRepositoryMock.Verify(x => x.Add(It.Is<DrawApprovalDocument>(
            x => x.DrawAmount == drawAmount
                 && x.CreatedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.Status == DrawApprovalStatus.New.ToString()
                 && x.Type == "express"
                 && x.LastStatusChangedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.MerchantId == createDrawApproval.MerchantId
                 && x.ApplicationDate == utcNow
                 && x.LastStatusChangedAt == utcNow
                 && x.CreditApplicationId == creditApplication.Id
                 && x.CompanyName == company.LegalName
                 && x.MerchantName == merchant.LegalName), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Create_ValidRequestLoCNotCreatedYet_CreatesDrawApproval(CreateDrawApproval createDrawApproval,
        List<PayableItem> payables, LoanPaymentPlanDocument paymentPlan, InvoiceModel invoice, CompanyModel merchant, CompanyModel company, Draft draft)
    {
        var utcNow = DateTime.UtcNow;
        createDrawApproval.Payables = payables;
        createDrawApproval.CreditApplicationId = null;
        createDrawApproval.LoanOrigin = LoanOrigin.Express;
        var drawAmount = payables.Sum(x => x.Amount);
        _paymentPlanRepositoryMock.Setup(x => x.GetById(createDrawApproval.PaymentPlanId, default)).ReturnsAsync(paymentPlan);
        _invoiceExternalServiceMock.Setup(x => x.GetById(payables[0].Id, default)).ReturnsAsync(invoice);
        _companyExternalServiceMock.Setup(x => x.GetById(createDrawApproval.MerchantId!, default)).ReturnsAsync(merchant);
        _companyExternalServiceMock.Setup(x => x.GetById(createDrawApproval.CompanyId, default)).ReturnsAsync(company);
        _drawApprovalRepositoryMock.Setup(x => x.Add(It.IsAny<DrawApprovalDocument>(), CancellationToken.None))
            .ReturnsAsync((DrawApprovalDocument x, CancellationToken _) => x);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(utcNow);
        _draftServiceMock.Setup(x => x.GetAllByFilters(new DraftFilter
        {
            CompanyId = merchant.Id
        }, default)).ReturnsAsync(new List<Draft>()
        {
            draft
        });
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default))
            .ReturnsAsync(Enumerable.Empty<CreditApplicationDocument>());

        await _drawApprovalService.Create(createDrawApproval, default);

        _drawApprovalRepositoryMock.Verify(x => x.Add(It.Is<DrawApprovalDocument>(
            x => x.DrawAmount == drawAmount
                 && x.CreatedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.Status == DrawApprovalStatus.New.ToString()
                 && x.Type == "express"
                 && x.LastStatusChangedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.MerchantId == createDrawApproval.MerchantId
                 && x.ApplicationDate == utcNow
                 && x.LastStatusChangedAt == utcNow
                 && x.CreditApplicationId == null
                 && x.CompanyName == company.LegalName
                 && x.MerchantName == merchant.LegalName), default), Times.Once);
    }

    [Fact]
    public async Task RunDrawApprovalDecisionEngineExecution_ValidRequest_RunsDecisionEngine()
    {
        var request = new DrawApprovalDecisionEngineExecutionRequest()
        {
            DrawAmount = 10,
            CompanyId = "id",
            CreditHoldAmount = 40,
            InHouseCreditId = "creditId",
            PaymentPlanId = "paymentPlanId",
            NoSupplierDetails = new NoSupplierDetailsModel()
            {
                BusinessName = "businessName",
                ContactName = "businessAddress",
                Address = new NoSupplierAddressModel { City = "city", State = "state", Address = "street", Zip = "zip" },
                BankDetails = new NoSupplierBankDetailsModel
                {
                    AccountNumber = new NoSupplierBankAccountNumber { Cipher = "cipher" },
                    RoutingNumber = "routingNumber"
                }
            }
        };

        _decisionEngineExecutionServiceMock.Setup(x =>
            x.StartDrawApprovalInitializationStep(It.IsAny<DrawApprovalInitializationStepStartRequest>(), CancellationToken.None));

        await _drawApprovalService.RunDrawApprovalDecisionEngineExecution(request, CancellationToken.None);

        _decisionEngineExecutionServiceMock.Verify(x =>
            x.StartDrawApprovalInitializationStep(It.Is<DrawApprovalInitializationStepStartRequest>(
                y => y.DrawAmount == request.DrawAmount &&
                     y.CompanyId == request.CompanyId &&
                     y.CreditHoldAmount == request.CreditHoldAmount &&
                     y.InHouseCreditId == request.InHouseCreditId &&
                     y.PaymentPlanId == request.PaymentPlanId &&
                     y.NoSupplierDetails!.BusinessName == request.NoSupplierDetails.BusinessName &&
                     y.NoSupplierDetails.ContactName == request.NoSupplierDetails.ContactName &&
                     y.NoSupplierDetails!.BankDetails!.AccountNumber!.Cipher == request.NoSupplierDetails.BankDetails.AccountNumber.Cipher
                     ), CancellationToken.None), Times.Once);

    }

    [Theory, CustomAutoData]
    public async Task RunRegularDrawApprovalDecisionEngineExecution_ValidRequest_RunsDecisionEngine(List<CreditDto> credits)
    {
        var request = new DrawApprovalDecisionEngineExecutionRequest()
        {
            DrawAmount = 10,
            CompanyId = "id",
            CreditHoldAmount = 40,
            PaymentPlanId = "paymentPlanId",
            LoanOrigin = LoanOrigin.Normal,
            CreditId = "********"
        };

        _loanExternalServiceMock.Setup(x => x.GetCreditsByFilters(It.Is<CreditFilterDto>(x =>
            x.CreditApplicationId.Contains(request.CreditId) &&
            x.Product == ProductType.LineOfCredit &&
            x.Status == CreditStatus.Active), default)).ReturnsAsync(credits!);

        _decisionEngineExecutionServiceMock.Setup(x =>
            x.StartDrawApprovalInitializationStep(It.IsAny<DrawApprovalInitializationStepStartRequest>(), default));

        await _drawApprovalService.RunDrawApprovalDecisionEngineExecution(request, default);

        _decisionEngineExecutionServiceMock.Verify(x =>
            x.StartDrawApprovalInitializationStep(It.Is<DrawApprovalInitializationStepStartRequest>(
                y => y.DrawAmount == request.DrawAmount &&
                     y.CompanyId == request.CompanyId &&
                     y.CreditId == credits.FirstOrDefault()!.Id.ToString() &&
                     y.InHouseCreditId == request.InHouseCreditId &&
                     y.PaymentPlanId == request.PaymentPlanId), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PatchDrawApprovalDetails_ValidRequest_UpdatesDrawApprovalStatus(string id, PatchInternalDrawApproval patchDrawApproval, DrawApprovalDocument drawApproval)
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(drawApproval);

        await _drawApprovalService.PatchDrawApprovalDetails(id, patchDrawApproval, default);

        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(
            x => x.CreatedBy == drawApproval.CreatedBy
                 && x.UpdatedBy == DrawAuthorizationConstants.OnBoardingService
                 && x.Status == patchDrawApproval.NewStatus.ToString()), default), Times.Once);
        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PatchDrawApprovalDetails_ValidRequest_UpdatesNotNullDrawDetailsFields(string id, PatchInternalDrawApproval patchDrawApproval, DrawApprovalDocument drawApproval)
    {
        patchDrawApproval.DrawDetails.ProjectEndDate = null;
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(drawApproval);

        await _drawApprovalService.PatchDrawApprovalDetails(id, patchDrawApproval, default);

        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(
            x => x.DrawDetails.ProjectEndDate == drawApproval.DrawDetails.ProjectEndDate
                 && x.DrawDetails.AccountStatus == patchDrawApproval.DrawDetails.AccountStatus.ToString()), default), Times.Once);
        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public Task Patch_DrawApprovalNotFound_ThrowVariableNullException(string id, PatchDrawApproval patchDrawApproval)
    {
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(DateTime.UtcNow);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(id, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.Patch(patchDrawApproval, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public async Task Patch_ValidRequest_UpdatesNotNullDrawDetailsFields(PatchDrawApproval patchDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval)
    {
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(patchDrawApproval.Id, default))!.ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(drawApprovalDocument);

        var result = await _drawApprovalService.Patch(patchDrawApproval, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(patchDrawApproval.Id, default), Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(x =>
            x.LastStatusChangedAt == currentDateTime
            && x.LastStatusChangedBy == drawApprovalDocument.UpdatedBy
            && x.Status == patchDrawApproval.Status.ToString()
            && x.Id == patchDrawApproval.Id
            && x.Type == patchDrawApproval.Type.ToString()
            && x.ProjectId == patchDrawApproval.ProjectId
            && x.PaymentPlanId == patchDrawApproval.PaymentPlanId
            && x.VirtualCardId == patchDrawApproval.VirtualCardId
            && x.DrawAmount == patchDrawApproval.DrawAmount
            && x.CompanyId == patchDrawApproval.CompanyId
            && x.MerchantId == patchDrawApproval.MerchantId
            && x.MerchantName == patchDrawApproval.MerchantName
        ), default), Times.Once);

        result.ShouldNotBe(drawApproval);
    }

    [Theory, CustomAutoData]
    public async Task Patch_ValidRequestUpdateCreditApplicationId_UpdatesNotNullDrawDetailsFields(PatchDrawApproval patchDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval,
        CreditApplicationDocument credit)
    {
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);

        patchDrawApproval.CreditApplicationId = null;
        drawApprovalDocument.CreditApplicationId = null;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(patchDrawApproval.Id, default))!.ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(drawApprovalDocument);
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default))
            .ReturnsAsync(new[]
            {
                credit
            });

        var result = await _drawApprovalService.Patch(patchDrawApproval, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(patchDrawApproval.Id, default), Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(x =>
            x.LastStatusChangedAt == currentDateTime
            && x.LastStatusChangedBy == drawApprovalDocument.UpdatedBy
            && x.Status == patchDrawApproval.Status.ToString()
            && x.Id == patchDrawApproval.Id
            && x.Type == patchDrawApproval.Type.ToString()
            && x.ProjectId == patchDrawApproval.ProjectId
            && x.PaymentPlanId == patchDrawApproval.PaymentPlanId
            && x.VirtualCardId == patchDrawApproval.VirtualCardId
            && x.DrawAmount == patchDrawApproval.DrawAmount
            && x.CompanyId == patchDrawApproval.CompanyId
            && x.MerchantId == patchDrawApproval.MerchantId
            && x.MerchantName == patchDrawApproval.MerchantName
            && x.CreditApplicationId == credit.Id
        ), default), Times.Once);

        result.ShouldNotBe(drawApproval);
    }

    [Theory, CustomAutoData]
    public async Task Patch_ValidRequestNoLoC_UpdatesNotNullDrawDetailsFields(PatchDrawApproval patchDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval)
    {
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);

        patchDrawApproval.CreditApplicationId = null;
        drawApprovalDocument.CreditApplicationId = null;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(patchDrawApproval.Id, default))!.ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(drawApprovalDocument);
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default))
            .ReturnsAsync(Enumerable.Empty<CreditApplicationDocument>());


        var result = await _drawApprovalService.Patch(patchDrawApproval, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(patchDrawApproval.Id, default), Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(x =>
            x.LastStatusChangedAt == currentDateTime
            && x.LastStatusChangedBy == drawApprovalDocument.UpdatedBy
            && x.Status == patchDrawApproval.Status.ToString()
            && x.Id == patchDrawApproval.Id
            && x.Type == patchDrawApproval.Type.ToString()
            && x.ProjectId == patchDrawApproval.ProjectId
            && x.PaymentPlanId == patchDrawApproval.PaymentPlanId
            && x.VirtualCardId == patchDrawApproval.VirtualCardId
            && x.DrawAmount == patchDrawApproval.DrawAmount
            && x.CompanyId == patchDrawApproval.CompanyId
            && x.MerchantId == patchDrawApproval.MerchantId
            && x.MerchantName == patchDrawApproval.MerchantName
            && x.CreditApplicationId == null
        ), default), Times.Once);

        result.ShouldNotBe(drawApproval);
    }

    [Theory, CustomAutoData]
    public async Task ReviewDrawApproval_ApprovedDrawApprovalModelWithQuote_ReturnDrawApproval(
        DrawApprovalDocument drawApprovalDocument,
        ReviewDrawApprovalModel model)
    {
        model.NewStatus = AdminDrawApprovalStatusUpdate.Approved;
        drawApprovalDocument.Status = DrawApprovalStatus.Processed.ToString();
        drawApprovalDocument.ApprovedStatusSynchronizedAt = null;
        drawApprovalDocument.Type = DrawApprovalType.Factoring.ToString();
        drawApprovalDocument.LoanOrigin = LoanOrigin.Factoring.ToString();

        drawApprovalDocument.Payables =
        [
            new() { Type = PayableType.Quote.ToString() }
        ];

        var invoices = new List<InvoiceModel>();

        var invoiceStatuses = new List<InvoiceStatusResponse>();

        drawApprovalDocument.DrawAmount = invoices.Sum(x => x.TotalAmount);

        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(model.Id, default))
            .ReturnsAsync(drawApprovalDocument);

        _invoiceExternalServiceMock
            .Setup(x => x.GetByIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(invoices);

        _nodeExternalService
            .Setup(x => x.GetInvoiceStatuses(It.IsAny<InvoiceStatusesRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(invoiceStatuses);

        await _drawApprovalService.ReviewDrawApproval(model, default);

        _integrationServiceMock.Verify(x => x.StartIssueLoanProcess(drawApprovalDocument, default), Times.Never);
        _integrationServiceMock.Verify(x => x.SendIhcDrawApprovalRejectedOrApprovedNotification(drawApprovalDocument, default), Times.Never);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.IsAny<DrawApprovalDocument>(), default), Times.AtLeast(2));
    }

    [Fact]
    public Task ReviewDrawApproval_InvalidRejectDrawApprovalModel_ThrowsValidationException()
    {
        var model = new ReviewDrawApprovalModel()
        {
            NewStatus = AdminDrawApprovalStatusUpdate.Rejected,
        };

        var act = async () => await _drawApprovalService.ReviewDrawApproval(model, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task ReviewDrawApproval_DrawApprovalIsNotProcessed_ThrowsValidationException(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processing.ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = nameof(ReviewDrawApprovalModel.Id),
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Approved,
        };
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        var act = async () => await _drawApprovalService.ReviewDrawApproval(model, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public async Task ReviewDrawApproval_ValidRejectDrawApprovalModel_UpdatesDrawApprovalCorrectly(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();
        var currentDateTime = DateTime.UtcNow;
        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Rejected,
            Code = nameof(ReviewDrawApprovalModel.Code),
            Note = nameof(ReviewDrawApprovalModel.Note)
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.UpdatedBy == model.UpdatedBy &&
                 d.StatusCode == model.Code && d.StatusNote == model.Note &&
                 d.Status == DrawApprovalStatus.Rejected.ToString() && d.LastStatusChangedAt == currentDateTime
                 && d.LastStatusChangedBy == model.UpdatedBy && d.RejectedAt == currentDateTime && d.RejectedBy == model.UpdatedBy
        ), default));

        await _drawApprovalService.ReviewDrawApproval(model, default);

        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.Status == DrawApprovalStatus.Rejected.ToString()
        ), default), Times.Once);
        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
        _integrationServiceMock.Verify(x => x.SendIhcDrawApprovalRejectedOrApprovedNotification(drawApproval, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ReviewDrawApproval_ValidApproveDrawApprovalModel_UpdatesDrawApprovalCorrectly(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();
        var currentDateTime = DateTime.UtcNow;
        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Approved,
            Code = nameof(ReviewDrawApprovalModel.Code),
            Note = nameof(ReviewDrawApprovalModel.Note)
        };

        var invoices = new List<InvoiceModel>()
        {
            new()
            {
                TotalAmount = drawApproval.DrawAmount
            }
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.UpdatedBy == model.UpdatedBy &&
                 d.StatusCode == drawApproval.StatusNote && d.StatusNote == drawApproval.StatusNote &&
                 d.Status == DrawApprovalStatus.Approved.ToString() && d.LastStatusChangedAt == currentDateTime
                 && d.LastStatusChangedBy == model.UpdatedBy && d.ApprovedAt == currentDateTime && d.ApprovedBy == model.UpdatedBy
        ), default));
        _loanAppRepo.Setup(x => x.GetByInvoicesIds(It.IsAny<IEnumerable<string>>(), default)).ReturnsAsync(new LoanApplicationDocument());
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(It.IsAny<string[]>(), default)).ReturnsAsync(invoices);

        await _drawApprovalService.ReviewDrawApproval(model, default);

        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.Status == DrawApprovalStatus.Approved.ToString()), default), Times.Once);
        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
        _invoiceExternalServiceMock.Verify(x => x.GetByIds(It.IsAny<string[]>(), default));
    }

    [Theory, CustomAutoData]
    public Task ReviewDrawApproval_InvoiceHasCancelledStatus_ShouldThrowValidationException(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();
        var invoiceIds = drawApproval.Payables.Select(x => x.Id).ToArray();
        var invoiceStatusesResponse = new List<InvoiceStatusResponse>()
        {
            new()
            {
                Id = invoiceIds[0],
                Status = InvoiceStatusConstants.Cancelled
            }
        };
        var currentDateTime = DateTime.UtcNow;
        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Approved,
            Code = nameof(ReviewDrawApprovalModel.Code),
            Note = nameof(ReviewDrawApprovalModel.Note)
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        _nodeExternalService.Setup(x => x.GetInvoiceStatuses(It.IsAny<InvoiceStatusesRequest>(), default)).ReturnsAsync(invoiceStatusesResponse);

        var act = async () => await _drawApprovalService.ReviewDrawApproval(model, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task ReviewDrawApproval_InvoiceHasPaidStatus_ShouldThrowValidationException(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();
        var invoiceIds = drawApproval.Payables.Select(x => x.Id).ToArray();
        var invoiceStatusesResponse = new List<InvoiceStatusResponse>()
        {
            new()
            {
                Id = invoiceIds[0],
                Status = InvoiceStatusConstants.Paid
            }
        };
        var currentDateTime = DateTime.UtcNow;
        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Approved,
            Code = nameof(ReviewDrawApprovalModel.Code),
            Note = nameof(ReviewDrawApprovalModel.Note)
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        _nodeExternalService.Setup(x => x.GetInvoiceStatuses(It.IsAny<InvoiceStatusesRequest>(), default)).ReturnsAsync(invoiceStatusesResponse);

        var act = async () => await _drawApprovalService.ReviewDrawApproval(model, default);
        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public async Task ReviewDrawApproval_ValidCancelDrawApprovalModel_UpdatesDrawApprovalCorrectly(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();
        var currentDateTime = DateTime.UtcNow;
        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Canceled,
            Code = nameof(ReviewDrawApprovalModel.Code),
            Note = nameof(ReviewDrawApprovalModel.Note)
        };

        drawApproval.Payables = new List<PayableItemDocument>()
        {
            new()
            {
                Id = "1"
            },
            new()
            {
                Id = "2"
            }
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.UpdatedBy == model.UpdatedBy &&
                 d.StatusCode == drawApproval.StatusNote && d.StatusNote == drawApproval.StatusNote &&
                 d.Status == DrawApprovalStatus.Canceled.ToString() && d.LastStatusChangedAt == currentDateTime
                 && d.LastStatusChangedBy == model.UpdatedBy && d.CanceledAt == currentDateTime && d.CanceledBy == model.UpdatedBy
        ), default));

        await _drawApprovalService.ReviewDrawApproval(model, default);

        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.Status == DrawApprovalStatus.Canceled.ToString()), default), Times.Once);
        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ReviewDrawApproval_ValidSentbackDrawApprovalModel_UpdatesDrawApprovalCorrectly(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();
        var currentDateTime = DateTime.UtcNow;
        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Sentback,
            Code = nameof(ReviewDrawApprovalModel.Code),
            Note = nameof(ReviewDrawApprovalModel.Note)
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(model.Id, default)).ReturnsAsync(drawApproval);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.UpdatedBy == model.UpdatedBy &&
                 d.StatusCode == drawApproval.StatusNote && d.StatusNote == drawApproval.StatusNote &&
                 d.Status == DrawApprovalStatus.Canceled.ToString() && d.LastStatusChangedAt == currentDateTime
                 && d.LastStatusChangedBy == model.UpdatedBy && d.CanceledAt == currentDateTime && d.CanceledBy == model.UpdatedBy
        ), default));

        await _drawApprovalService.ReviewDrawApproval(model, default);

        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(
            d => d.Status == DrawApprovalStatus.Sentback.ToString()), default), Times.Once);
        _laSyncMessageSender.Verify(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<SyncLoanApplicationMessagePayload>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ReviewDrawApproval_ShouldFailWhenRejectionNoteIsMissing(DrawApprovalDocument drawApproval)
    {
        drawApproval.Status = DrawApprovalStatus.Processed.ToString();

        var id = Guid.NewGuid().ToString();
        var model = new ReviewDrawApprovalModel()
        {
            Id = id,
            UpdatedBy = nameof(ReviewDrawApprovalModel.UpdatedBy),
            NewStatus = AdminDrawApprovalStatusUpdate.Rejected,
            Code = "P10",
            Note = null
        };

        ValidationException ex = await _drawApprovalService.ReviewDrawApproval(model, default).ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("The rejection note has to be specified");
    }

    [Theory, CustomAutoData]
    public async Task GetByInvoicesIds_ValidInvoicesIds_ReturnsNull(string[] invoicesIds)
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetByInvoicesIds(invoicesIds, default)).ReturnsAsync((DrawApprovalDocument?)null);
        var result = await _drawApprovalService.GetByInvoicesIds(invoicesIds, default);

        result.ShouldBeNull();
        _drawApprovalRepositoryMock.Verify(x => x.GetByInvoicesIds(It.IsAny<string[]>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Update_ValidRequest_ShouldUpdate(string id, UpdateDrawApproval updateDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval, List<InvoiceModel> invoices)
    {
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        drawApproval.Id = id;
        drawApprovalDocument.Id = id;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(id, default))!.ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(drawApprovalDocument);
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(updateDrawApproval.InvoiceIds.ToArray(), default)).ReturnsAsync(invoices);

        var result = await _drawApprovalService.UpdateDrawApproval(id, updateDrawApproval, default);

        result.ShouldNotBe(drawApproval);
        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(id, default), Times.Once);

        _drawApprovalRepositoryMock.Verify(f => f.Update(It.Is<DrawApprovalDocument>(x =>
            x.LastStatusChangedAt == currentDateTime
            && x.LastStatusChangedBy == drawApprovalDocument.UpdatedBy
            && x.Status == updateDrawApproval.Status.ToString()
            && x.Id == id
            && x.Type == updateDrawApproval.Type.ToString()
            && x.ProjectId == updateDrawApproval.ProjectId
            && x.PaymentPlanId == updateDrawApproval.PaymentPlanId
            && x.DrawAmount == updateDrawApproval.DrawAmount
            && x.CompanyId == updateDrawApproval.CompanyId
            && x.EinHash == updateDrawApproval.EinHash
            && x.ApprovedAt == updateDrawApproval.ApprovedAt
            && x.ApprovedBy == updateDrawApproval.ApprovedBy
            && x.DrawDetails.ProjectEndDate == updateDrawApproval.DrawDetails!.ProjectEndDate!.Value.ToDateTime(TimeOnly.MinValue)
            && x.DrawDetails.LoansLastDefaultedDate == updateDrawApproval.DrawDetails.LoansLastDefaultedDate!.Value.ToDateTime(TimeOnly.MinValue)
            && x.DrawDetails.MaxPastDueDays == updateDrawApproval.DrawDetails.MaxPastDueDays
            && x.DrawDetails.CurrentCreditLimitPercentage == updateDrawApproval.DrawDetails.CurrentCreditLimitPercentage
            && x.DrawDetails.CreditLimitPercentageWithCurrentDraw == updateDrawApproval.DrawDetails.CreditLimitPercentageWithCurrentDraw
            && x.DrawDetails.CreditPurchaseType == updateDrawApproval.DrawDetails.CreditPurchaseType!.Value.ToString()
            && x.DrawDetails.CreditAvailableBalance == updateDrawApproval.DrawDetails.CreditAvailableBalance
            && x.DrawDetails.ProjectAvailableBalance == updateDrawApproval.DrawDetails.ProjectAvailableBalance
            && x.DrawDetails.ProjectContractValue == updateDrawApproval.DrawDetails.ProjectContractValue
            && x.DrawDetails.OutstandingBalance == updateDrawApproval.DrawDetails.OutstandingBalance
            && x.DrawDetails.AccountStatus == updateDrawApproval.DrawDetails.AccountStatus.ToString()
            && x.DrawDetails.ProjectApprovalStatus == updateDrawApproval.DrawDetails.ProjectApprovalStatus.ToString()
            && x.Payables.Select(x => x.Id).SequenceEqual(invoices.Select(c => c.Id))
            && x.Payables.Select(x => x.Amount).SequenceEqual(invoices.Select(c => c.TotalAmount))
        ), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Update_ValidRequestWithLoC_ShouldUpdate(string id, UpdateDrawApproval updateDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval, List<InvoiceModel> invoices,
        CreditApplicationDocument credit)
    {
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        drawApproval.Id = id;
        drawApprovalDocument.CreditApplicationId = null;
        drawApprovalDocument.Id = id;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(id, default))!.ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(drawApprovalDocument);
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(updateDrawApproval.InvoiceIds.ToArray(), default)).ReturnsAsync(invoices);
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default))
            .ReturnsAsync(new[]
            {
                credit
            });

        var result = await _drawApprovalService.UpdateDrawApproval(id, updateDrawApproval, default);

        result.ShouldNotBe(drawApproval);
        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(id, default), Times.Once);

        _drawApprovalRepositoryMock.Verify(f => f.Update(It.Is<DrawApprovalDocument>(x =>
            x.LastStatusChangedAt == currentDateTime
            && x.LastStatusChangedBy == drawApprovalDocument.UpdatedBy
            && x.Status == updateDrawApproval.Status.ToString()
            && x.Id == id
            && x.CreditApplicationId == credit.Id
            && x.Type == updateDrawApproval.Type.ToString()
            && x.ProjectId == updateDrawApproval.ProjectId
            && x.PaymentPlanId == updateDrawApproval.PaymentPlanId
            && x.DrawAmount == updateDrawApproval.DrawAmount
            && x.CompanyId == updateDrawApproval.CompanyId
            && x.EinHash == updateDrawApproval.EinHash
            && x.ApprovedAt == updateDrawApproval.ApprovedAt
            && x.ApprovedBy == updateDrawApproval.ApprovedBy
            && x.DrawDetails.ProjectEndDate == updateDrawApproval.DrawDetails!.ProjectEndDate!.Value.ToDateTime(TimeOnly.MinValue)
            && x.DrawDetails.LoansLastDefaultedDate == updateDrawApproval.DrawDetails.LoansLastDefaultedDate!.Value.ToDateTime(TimeOnly.MinValue)
            && x.DrawDetails.MaxPastDueDays == updateDrawApproval.DrawDetails.MaxPastDueDays
            && x.DrawDetails.CurrentCreditLimitPercentage == updateDrawApproval.DrawDetails.CurrentCreditLimitPercentage
            && x.DrawDetails.CreditLimitPercentageWithCurrentDraw == updateDrawApproval.DrawDetails.CreditLimitPercentageWithCurrentDraw
            && x.DrawDetails.CreditPurchaseType == updateDrawApproval.DrawDetails.CreditPurchaseType!.Value.ToString()
            && x.DrawDetails.CreditAvailableBalance == updateDrawApproval.DrawDetails.CreditAvailableBalance
            && x.DrawDetails.ProjectAvailableBalance == updateDrawApproval.DrawDetails.ProjectAvailableBalance
            && x.DrawDetails.ProjectContractValue == updateDrawApproval.DrawDetails.ProjectContractValue
            && x.DrawDetails.OutstandingBalance == updateDrawApproval.DrawDetails.OutstandingBalance
            && x.DrawDetails.AccountStatus == updateDrawApproval.DrawDetails.AccountStatus.ToString()
            && x.DrawDetails.ProjectApprovalStatus == updateDrawApproval.DrawDetails.ProjectApprovalStatus.ToString()
            && x.Payables.Select(x => x.Id).SequenceEqual(invoices.Select(c => c.Id))
            && x.Payables.Select(x => x.Amount).SequenceEqual(invoices.Select(c => c.TotalAmount))
        ), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Update_ValidRequestWithoutLoC_ShouldUpdate(string id, UpdateDrawApproval updateDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval,
        List<InvoiceModel> invoices)
    {
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        drawApproval.Id = id;
        drawApprovalDocument.CreditApplicationId = null;
        drawApprovalDocument.Id = id;
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(id, default))!.ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(drawApprovalDocument);
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(updateDrawApproval.InvoiceIds.ToArray(), default)).ReturnsAsync(invoices);
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default))
            .ReturnsAsync(Enumerable.Empty<CreditApplicationDocument>());

        var result = await _drawApprovalService.UpdateDrawApproval(id, updateDrawApproval, default);

        result.ShouldNotBe(drawApproval);
        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(id, default), Times.Once);

        _drawApprovalRepositoryMock.Verify(f => f.Update(It.Is<DrawApprovalDocument>(x =>
            x.LastStatusChangedAt == currentDateTime
            && x.LastStatusChangedBy == drawApprovalDocument.UpdatedBy
            && x.Status == updateDrawApproval.Status.ToString()
            && x.Id == id
            && x.CreditApplicationId == null
            && x.Type == updateDrawApproval.Type.ToString()
            && x.ProjectId == updateDrawApproval.ProjectId
            && x.PaymentPlanId == updateDrawApproval.PaymentPlanId
            && x.DrawAmount == updateDrawApproval.DrawAmount
            && x.CompanyId == updateDrawApproval.CompanyId
            && x.EinHash == updateDrawApproval.EinHash
            && x.ApprovedAt == updateDrawApproval.ApprovedAt
            && x.ApprovedBy == updateDrawApproval.ApprovedBy
            && x.DrawDetails.ProjectEndDate == updateDrawApproval.DrawDetails!.ProjectEndDate!.Value.ToDateTime(TimeOnly.MinValue)
            && x.DrawDetails.LoansLastDefaultedDate == updateDrawApproval.DrawDetails.LoansLastDefaultedDate!.Value.ToDateTime(TimeOnly.MinValue)
            && x.DrawDetails.MaxPastDueDays == updateDrawApproval.DrawDetails.MaxPastDueDays
            && x.DrawDetails.CurrentCreditLimitPercentage == updateDrawApproval.DrawDetails.CurrentCreditLimitPercentage
            && x.DrawDetails.CreditLimitPercentageWithCurrentDraw == updateDrawApproval.DrawDetails.CreditLimitPercentageWithCurrentDraw
            && x.DrawDetails.CreditPurchaseType == updateDrawApproval.DrawDetails.CreditPurchaseType!.Value.ToString()
            && x.DrawDetails.CreditAvailableBalance == updateDrawApproval.DrawDetails.CreditAvailableBalance
            && x.DrawDetails.ProjectAvailableBalance == updateDrawApproval.DrawDetails.ProjectAvailableBalance
            && x.DrawDetails.ProjectContractValue == updateDrawApproval.DrawDetails.ProjectContractValue
            && x.DrawDetails.OutstandingBalance == updateDrawApproval.DrawDetails.OutstandingBalance
            && x.DrawDetails.AccountStatus == updateDrawApproval.DrawDetails.AccountStatus.ToString()
            && x.DrawDetails.ProjectApprovalStatus == updateDrawApproval.DrawDetails.ProjectApprovalStatus.ToString()
            && x.Payables.Select(x => x.Id).SequenceEqual(invoices.Select(c => c.Id))
            && x.Payables.Select(x => x.Amount).SequenceEqual(invoices.Select(c => c.TotalAmount))
        ), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public Task Update_DrawApprovalWasNotFound_ShouldThrowVariableNullException(string id, UpdateDrawApproval updateDrawApproval, DrawApprovalDocument drawApprovalDocument, DrawApproval drawApproval)
    {
        drawApproval.Id = id;
        drawApprovalDocument.Id = id;
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(id, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.UpdateDrawApproval(id, updateDrawApproval, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public Task PatchDrawApprovalPaymentPlanAdmin_DrawApprovalWasNotFound_ShouldThrowsWException(PatchDrawPaymentPlanAdminModel model)
    {
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(model.Id, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.PatchDrawApprovalPaymentPlan(model, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public async Task PatchDrawApprovalPaymentPlanAdmin_ValidRequest_ShouldPatchDrawPaymentPlanId(PatchDrawPaymentPlanAdminModel paymentPlanAdminModel, DrawApprovalDocument drawApprovalDocument,
        DrawApprovalDocument updatedDrawApprovalDocument, DrawApproval drawApproval)
    {
        drawApprovalDocument.DebtInvestor = "arcadia";
        updatedDrawApprovalDocument.DebtInvestor = drawApprovalDocument.DebtInvestor;
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        drawApproval.Id = paymentPlanAdminModel.Id;
        drawApprovalDocument.Id = paymentPlanAdminModel.Id;

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock
            .Setup(x => x.GetById(paymentPlanAdminModel.Id, default)).ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(updatedDrawApprovalDocument);

        var result = await _drawApprovalService.PatchDrawApprovalPaymentPlan(paymentPlanAdminModel, default);
        result.ShouldNotBe(drawApproval);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock
            .Verify(x => x.GetById(paymentPlanAdminModel.Id, default), Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(f =>
            f.UpdatedAt == currentDateTime &&
            f.UpdatedBy == paymentPlanAdminModel.UserId &&
            f.PaymentPlanId == paymentPlanAdminModel.NewPaymentPlanId), default));
    }

    [Theory, CustomAutoData]
    public async Task GetDrawApprovlas_ValidRequest_ShouldReturnDrawApprovals(List<DrawApprovalDocument> drawApprovalDocuments)
    {
        foreach (var drawApproval in drawApprovalDocuments)
            drawApproval.DebtInvestor = "arcadia";

        _drawApprovalRepositoryMock.Setup(x => x.GetAll(default)).ReturnsAsync(drawApprovalDocuments);

        var result = (await _drawApprovalService.Get(default)).ToArray();
        result.Length.ShouldBe(drawApprovalDocuments.Count);
        result[0].Id.ShouldBeEquivalentTo(drawApprovalDocuments[0].Id);
        result[0].Type.ToString().ShouldBeEquivalentTo(drawApprovalDocuments[0].Type);
        result[0].Status.ToString().ShouldBeEquivalentTo(drawApprovalDocuments[0].Status);
        result[0].CompanyId.ShouldBeEquivalentTo(drawApprovalDocuments[0].CompanyId);
        result[0].CompanyName.ShouldBeEquivalentTo(drawApprovalDocuments[0].CompanyName);
        _drawApprovalRepositoryMock.Verify(x => x.GetAll(default), Times.Once);
    }

    [Fact]
    public async Task GetDrawApprovals_ValidRequest_ShouldReturnEmptyList()
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetAll(default)).ReturnsAsync(Enumerable.Empty<DrawApprovalDocument>());
        var result = (await _drawApprovalService.Get(default)).ToArray();
        result.ShouldBeEquivalentTo(Array.Empty<DrawApproval>());
        _drawApprovalRepositoryMock.Verify(x => x.GetAll(default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetDrawApproval_ValidRequest_ShouldReturnValue(DrawApprovalDocument drawApprovalDocuments, string id)
    {
        drawApprovalDocuments.DebtInvestor = "arcadia";

        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(drawApprovalDocuments);

        var result = await _drawApprovalService.GetById(id, default);
        result!.Id.ShouldBeEquivalentTo(drawApprovalDocuments.Id);
        result.Type.ToString().ShouldBeEquivalentTo(drawApprovalDocuments.Type);
        result.Status.ToString().ShouldBeEquivalentTo(drawApprovalDocuments.Status);
        result.CompanyId.ShouldBeEquivalentTo(drawApprovalDocuments.CompanyId);
        result.CompanyName.ShouldBeEquivalentTo(drawApprovalDocuments.CompanyName);
        _drawApprovalRepositoryMock.Verify(x => x.GetById(id, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByFilterDrawApprovlas_ValidRequest_ShouldReturnDrawApprovals(GetQueryWithPaginationResult<DrawApprovalDocument> drawApprovalResult, GetDrawApprovalsQueryWithPagination query)
    {
        foreach (var drawApproval in drawApprovalResult.Result)
            drawApproval.DebtInvestor = "arcadia";

        var types = new string[]
        {
            "regular"
        };
        query.Type = types;

        _drawApprovalRepositoryMock.Setup(x => x.GetAllByFiltersWithPagination(query, default)).ReturnsAsync(drawApprovalResult);

        var result = (await _drawApprovalService.GetByFilter(query, default));
        result.Result.Count().ShouldBe(drawApprovalResult.Result.Count());
        result.Result.First().Id.ShouldBeEquivalentTo(drawApprovalResult.Result.First().Id);
        result.Result.First().Type.ToString().ShouldBeEquivalentTo(drawApprovalResult.Result.First().Type);
        result.Result.First().Status.ToString().ShouldBeEquivalentTo(drawApprovalResult.Result.First().Status);
        result.Result.First().CompanyId.ShouldBeEquivalentTo(drawApprovalResult.Result.First().CompanyId);
        result.Result.First().CompanyName.ShouldBeEquivalentTo(drawApprovalResult.Result.First().CompanyName);
        result.PageNumber.ShouldBeEquivalentTo(drawApprovalResult.PageNumber);
        result.PagesCount.ShouldBeEquivalentTo(drawApprovalResult.PagesCount);
        result.TotalCount.ShouldBeEquivalentTo(drawApprovalResult.TotalCount);
        _drawApprovalRepositoryMock.Verify(x => x.GetAllByFiltersWithPagination(It.Is<GetDrawApprovalsQueryWithPagination>(x => x.Type!.Contains(DrawApprovalType.Custom.ToString())), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PatchExpirationDate_ValidRequest_ShouldPatch(PatchExpirationDateAdminModel patchModel, DrawApprovalDocument drawApprovalDocument, DrawApprovalDocument updatedDrawApprovalDocument)
    {
        drawApprovalDocument.DebtInvestor = "arcadia";
        updatedDrawApprovalDocument.DebtInvestor = drawApprovalDocument.DebtInvestor;

        drawApprovalDocument.Id = patchModel.Id;
        drawApprovalDocument.Payables.FirstOrDefault()!.Type = PayableType.Quote.ToString();
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(patchModel.Id, default)).ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(updatedDrawApprovalDocument);

        await _drawApprovalService.PatchExpirationDate(patchModel, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.GetById(patchModel.Id, default), Times.Once());
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(document =>
                document.UpdatedBy == patchModel.UserId
                && document.ExpirationDate == patchModel.NewExpirationDate
                && document.UpdatedAt == currentDateTime
                && document.Id == patchModel.Id), default),
            Times.Once());
    }

    [Theory, CustomAutoData]
    public async Task PatchExpirationDate_ThereAreNoQuotePayables_ShouldPatch(PatchExpirationDateAdminModel patchModel, DrawApprovalDocument drawApprovalDocument)
    {
        drawApprovalDocument.DebtInvestor = "arcadia";
        drawApprovalDocument.Id = patchModel.Id;
        drawApprovalDocument.Payables.ToList().ForEach(x => x.Type = PayableType.Invoice.ToString());
        _drawApprovalRepositoryMock.Setup(x => x.GetById(patchModel.Id, default)).ReturnsAsync(drawApprovalDocument);

        await _drawApprovalService.PatchExpirationDate(patchModel, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Never);
        _drawApprovalRepositoryMock.Verify(x => x.GetById(patchModel.Id, default), Times.Once());
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.IsAny<DrawApprovalDocument>(), default), Times.Never);
    }

    [Theory, CustomAutoData]
    public Task PatchExpirationDate_InvalidRequest_ThrowsException(PatchExpirationDateAdminModel patchModel)
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetById(patchModel.Id, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.PatchExpirationDate(patchModel, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public async Task AddInvoices_ValidRequest_ShouldAddInvoices(string id, List<PayableItem> payables, DrawApprovalDocument drawApprovalDocument, DrawApprovalDocument updatedDrawApprovalDocument)
    {
        drawApprovalDocument.DebtInvestor = "arcadia";
        updatedDrawApprovalDocument.DebtInvestor = drawApprovalDocument.DebtInvestor;
        drawApprovalDocument.Id = id;
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        var expectedDrawAmount = drawApprovalDocument.Payables.Where(x => x.Type == PayableType.Invoice.ToString()).Sum(x => x.Amount) + payables.Where(x => x.Type == PayableType.Invoice).Sum(x => x.Amount);
        var expectedPayablesCount = payables.Count + drawApprovalDocument.Payables.Count();
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(updatedDrawApprovalDocument);

        await _drawApprovalService.AddInvoices(id, payables, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.GetById(id, default), Times.Once());
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(document =>
                document.InvoicedAt == currentDateTime
                && document.Id == id
                && document.DrawAmount == expectedDrawAmount
                && document.Payables.Count() == expectedPayablesCount), default),
            Times.Once());
    }

    [Theory, CustomAutoData]
    public Task AddInvoices_DrawApprovalNotFound_ThrowsException(string id, List<PayableItem> payables)
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.AddInvoices(id, payables, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public async Task PostTransaction_ValidRequest_ShouldRunDrawApproval(string id, string userId, DrawApprovalDocument drawApprovalDocument, DrawApprovalDocument updatedDrawApprovalDocument,
        NoteModel noteModel)
    {
        drawApprovalDocument.DebtInvestor = "arcadia";
        updatedDrawApprovalDocument.DebtInvestor = drawApprovalDocument.DebtInvestor;
        drawApprovalDocument.Id = id;
        drawApprovalDocument.Payables.FirstOrDefault()!.Type = PayableType.Quote.ToString();
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(drawApprovalDocument);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.IsAny<DrawApprovalDocument>(), default)).ReturnsAsync(updatedDrawApprovalDocument);
        _noteService.Setup(x => x.Add(It.Is<CreateDrawApprovalNote>(x => x.CreatedBy == userId && x.CreatedAt == currentDateTime && x.Note == noteModel.Note && x.DrawApprovalId == drawApprovalDocument.Id),
            default));

        await _drawApprovalService.PostTransaction(id, userId, noteModel, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.GetById(id, default), Times.Once());
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(document =>
                document.UpdatedAt == currentDateTime
                && document.InvoicedAt == currentDateTime
                && document.Id == id
                && document.UpdatedBy == userId
                && document.InvoicedAt == currentDateTime
                && document.InvoicedBy == userId), default),
            Times.Once());
        _noteService.Verify(x => x.Add(It.Is<CreateDrawApprovalNote>(x => x.CreatedBy == userId && x.CreatedAt == currentDateTime && x.Note == noteModel.Note && x.DrawApprovalId == drawApprovalDocument.Id),
            default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PostTransaction_ThereAreNoQuote_ShouldRunDrawApproval(string id, string userId, DrawApprovalDocument drawApprovalDocument, DrawApprovalDocument updatedDrawApprovalDocument,
        NoteModel noteModel)
    {
        drawApprovalDocument.DebtInvestor = "arcadia";
        updatedDrawApprovalDocument.DebtInvestor = drawApprovalDocument.DebtInvestor;
        var currentDateTime = new DateTime(2024, 01, 01, 0, 0, 0, DateTimeKind.Utc);
        drawApprovalDocument.Id = id;
        drawApprovalDocument.Payables.ToList().ForEach(x => x.Type = PayableType.Invoice.ToString());
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(drawApprovalDocument);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _noteService.Setup(x => x.Add(It.Is<CreateDrawApprovalNote>(x => x.CreatedBy == userId && x.CreatedAt == currentDateTime && x.Note == noteModel.Note && x.DrawApprovalId == drawApprovalDocument.Id),
            default));

        await _drawApprovalService.PostTransaction(id, userId, noteModel, default);

        _dateProviderMock.Verify(x => x.CurrentDateTime, Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.GetById(id, default), Times.Once());
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.IsAny<DrawApprovalDocument>(), default), Times.Never);
        _noteService.Verify(x => x.Add(It.Is<CreateDrawApprovalNote>(x => x.CreatedBy == userId && x.CreatedAt == currentDateTime && x.Note == noteModel.Note && x.DrawApprovalId == drawApprovalDocument.Id),
            default), Times.Once);
    }

    [Theory, CustomAutoData]
    public Task PostTransaction_DrawApprovalNotFound_ThrowsException(string id, string userId, NoteModel note)
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetById(id, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.PostTransaction(id, userId, note, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public async Task PatchAuthorizationPeriod_ValidRequest_ShouldPatch(string drawApprovalId, string authPeriodId, DrawApprovalDocument document)
    {
        document.DebtInvestor = "arcadia";
        _drawApprovalRepositoryMock.Setup(x => x.GetById(drawApprovalId, default)).ReturnsAsync(document);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.Is<DrawApprovalDocument>(x => x.AuthorizationPeriodId == authPeriodId), default)).ReturnsAsync(document);

        var result = await _drawApprovalService.PatchAuthorizationPeriod(drawApprovalId, authPeriodId, default);
        result.ShouldNotBeNull();
        result.AuthorizationPeriodId = authPeriodId;

        _drawApprovalRepositoryMock.Verify(x => x.GetById(drawApprovalId, default), Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(x => x.AuthorizationPeriodId == authPeriodId), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public Task PatchAuthorizationPeriod_DrawApprovalNotFound_ShouldPatch(string drawApprovalId, string authPeriodId)
    {
        _drawApprovalRepositoryMock.Setup(x => x.GetById(drawApprovalId, default))!.ReturnsAsync((DrawApprovalDocument?)null);

        var act = async () => await _drawApprovalService.PatchAuthorizationPeriod(drawApprovalId, authPeriodId, default);
        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Fact]
    public async Task GetProjectIdByDrawId_WithValidDrawId_ReturnsProjectId()
    {
        // Arrange
        var drawId = "drawId";
        var loanApplication = new LoanApplicationDocument()
        {
            InvoiceDetails = new InvoiceDetails
            {
                InvoiceIds = new()
                {
                    "invoiceId"
                }
            }
        };
        var invoices = new List<InvoiceModel>
        {
            new()
            {
                ProjectId = "projectId"
            }
        };

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApplication);
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(invoices);

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        Assert.Equal("projectId", result);
    }

    [Fact]
    public async Task GetProjectIdByDrawId_InvoicesAreNull_ReturnsNull()
    {
        // Arrange
        var drawId = "drawId";
        var loanApplication = new LoanApplicationDocument()
        {
            InvoiceDetails = new InvoiceDetails
            {
                InvoiceIds = new()
                {
                    "invoiceId"
                }
            }
        };

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApplication);
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<InvoiceModel>());

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public async Task GetProjectIdByDrawId_InvoiceIdsAreNull_ReturnsNull()
    {
        // Arrange
        var drawId = "drawId";
        var loanApplication = new LoanApplicationDocument()
        {
            InvoiceDetails = new InvoiceDetails
            {
                InvoiceIds = null
            }
        };

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApplication);

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public async Task GetProjectIdByDrawId_WithInvalidDrawId_ReturnsNull()
    {
        // Arrange
        var drawId = "drawId";

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((LoanApplicationDocument?)null);

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetProjectIdByDrawId_WithNullInvoiceIds_ReturnsNull()
    {
        // Arrange
        var drawId = "drawId";
        var loanApplication = new LoanApplicationDocument
        {
            InvoiceDetails = new InvoiceDetails
            {
                InvoiceIds = null
            }
        };

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApplication);

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetProjectIdByDrawId_WithEmptyInvoiceIds_ReturnsNull()
    {
        // Arrange
        var drawId = "drawId";
        var loanApplication = new LoanApplicationDocument
        {
            InvoiceDetails = new InvoiceDetails
            {
                InvoiceIds = new()
            }
        };

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApplication);

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetProjectIdByDrawId_WithMultipleProjectIds_ReturnsNull()
    {
        // Arrange
        var drawId = "drawId";
        var loanApplication = new LoanApplicationDocument
        {
            InvoiceDetails = new InvoiceDetails
            {
                InvoiceIds = new()
                {
                    "invoiceId1",
                    "invoiceId2"
                }
            }
        };
        var invoices = new List<InvoiceModel>
        {
            new()
            {
                ProjectId = "projectId1"
            },
            new()
            {
                ProjectId = "projectId2"
            }
        };

        _loanApplicationRepositoryMock.Setup(x => x.GetByDrawId(drawId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApplication);
        _invoiceExternalServiceMock.Setup(x => x.GetByIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(invoices);

        // Act
        var result = await _drawApprovalService.GetProjectIdByDrawId(drawId, CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Theory, CustomAutoData]
    public async Task GetManyByInvoicesIds_ValidInput_MapsCorrectly(string[] invoiceIds, List<DrawApprovalDocument> documents)
    {
        //Arrange
        foreach (var document in documents)
            document.DebtInvestor = DebtInvestorType.Arcadia.ToString();

        _drawApprovalRepositoryMock
            .Setup(x => x.GetManyByInvoicesIds(invoiceIds, default))
            .ReturnsAsync(documents);

        //Act
        var result = (await _drawApprovalService.GetManyByInvoicesIds(invoiceIds, default)).ToList();

        //Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(documents.Count);
    }
}
