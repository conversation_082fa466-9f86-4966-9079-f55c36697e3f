using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.API.ViewModels.Error;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.SNS.SlackNotification.Models;
using System.Net;
using System.Text.Json;

namespace BlueTape.Services.OnBoardingService.API.Middlewares;

public class ExceptionMiddleware(
    RequestDelegate next,
    ILogger<ExceptionMiddleware> logger,
    ISlackNotificationService notificationService)
{
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await next.Invoke(context);
        }
        catch (OperationCanceledException ex)
        {
            if (ex.CancellationToken.IsCancellationRequested) return;
            await HandleExceptionAsync(context, ex, StatusCodes.Status404NotFound, ExceptionConstants.ExceptionMessage);
        }
        catch (VariableNullException ex)
        {
            await HandleExceptionAsync(context, ex, StatusCodes.Status404NotFound, ExceptionConstants.ExceptionMessage);
        }
        catch (ValidationException ex)
        {
            await HandleExceptionAsync(context, ex, StatusCodes.Status400BadRequest,
                ExceptionConstants.ExceptionMessage);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex, StatusCodes.Status500InternalServerError,
                ExceptionConstants.ExceptionMessage);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception ex, int statusCode, string? message = null)
    {
        logger.LogError("{message} {newLine} {innerExceptionMessage}", ex.Message, Environment.NewLine,
            ex.InnerException?.Message);
        logger.LogError("Error query: {query}", context.Request.Path);
        logger.LogError("{stackTrace}", ex.StackTrace);
        var traceId = context.TraceIdentifier;

        var evenMessage = ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(),
            ConfigConstants.ProjectValue, EventLevel.Error);
        await notificationService.Notify(evenMessage, traceId, CancellationToken.None);

        if (context.Response.StatusCode == (int)HttpStatusCode.InternalServerError)
        {
            await notificationService.Notify(
                $"Internal Error was happened during API request: ${context.Request.Path}. Message: {ex.Message}",
                "API Internal Error",
                EventLevel.Error,
                traceId,
                CancellationToken.None);
        }

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = ContentConstants.ApplicationJsonFormat;
        var error = new ErrorViewModel { ErrorDescription = ex.Message, Message = message, StatusCode = statusCode };
        string jsonString = JsonSerializer.Serialize(error);

        await context.Response.WriteAsync(jsonString);
    }
}