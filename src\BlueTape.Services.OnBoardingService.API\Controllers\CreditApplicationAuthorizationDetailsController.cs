using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.CreditApplicationAuthorizationDetails)]
[ApiController]
public class CreditApplicationAuthorizationDetailsController : ControllerBase
{
    private readonly ICreditApplicationAuthorizationDetailsService _authorizationDetailsService;
    private readonly IMapper _mapper;

    public CreditApplicationAuthorizationDetailsController(ICreditApplicationAuthorizationDetailsService authorizationDetailsService, IMapper mapper)
    {
        _authorizationDetailsService = authorizationDetailsService;
        _mapper = mapper;
    }

    /// <summary>
    /// Authorization details for credit application with the given ID. 
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /CreditApplicationAuthorizationDetails/6577001b0952df74adb0e0e4
    ///     
    /// </remarks>
    /// <returns>Authorization details for credit application with the given ID.</returns>
    [HttpGet(EndpointConstants.Id)]
    public async Task<CreditApplicationAuthorizationDetailsDto?> GetById([FromRoute] string id, CancellationToken ct)
    {
        var result = await _authorizationDetailsService.GetByCreditApplicationId(id, ct);
        return _mapper.Map<CreditApplicationAuthorizationDetailsDto>(result);
    }

    [HttpPost]
    public Task CreateOrUpdateSnapshot([FromBody] UpsertAccountAuthDetailsSnapshotDto dto,
        CancellationToken ct)
    {
        var model = _mapper.Map<UpsertAccountAuthDetailsSnapshotModel>(dto);
        return _authorizationDetailsService.CreateOrUpdateAccountAuthDetailsSnapshot(model, ct);
    }
}
