using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.DrawApproval.Requests;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Web;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.Admin)]
[ApiController]
public class AdminController(IMapper mapper,
        IDecisionEngineStepsService decisionEngineStepsService,
        ICreditApplicationService creditApplicationService,
        ICreditApplicationExecutionService creditApplicationExecutionService,
        IDrawApprovalService drawApprovalService,
        IManualAuthorizationDetailsRefreshDetectorService detectorService)
    : ControllerBase
{



    [HttpPatch(EndpointConstants.UpdateFinalDrawApprovalStatus)]
    public async Task<DrawApprovalDto> UpdateFinalDrawApprovalStatus(
        [FromRoute] string id,
        [FromBody] PatchDrawStatusAdminRequest drawStatusAdminRequest,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId, CancellationToken ct)
    {
        var updateModel = mapper.Map<ReviewDrawApprovalModel>(drawStatusAdminRequest);
        updateModel.Id = id;
        updateModel.UpdatedBy = userId;

        var drawApproval = await drawApprovalService.ReviewDrawApproval(updateModel, ct);
        return mapper.Map<DrawApprovalDto>(drawApproval);
    }

    [HttpPatch($"{EndpointConstants.DrawApproval}/{EndpointConstants.Id}/{EndpointConstants.PaymentPlan}")]
    public async Task<DrawApprovalDto> PatchDrawApprovalPaymentPlan(
        [FromRoute] string id,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        [FromBody] PatchDrawPaymentPlanAdminRequest paymentPlanAdminRequest,
        CancellationToken ct)
    {
        var patchDrawPaymentPlanModel = new PatchDrawPaymentPlanAdminModel()
        {
            Id = id,
            UserId = userId,
            NewPaymentPlanId = paymentPlanAdminRequest.NewPaymentPlanId
        };

        return mapper.Map<DrawApprovalDto>(await drawApprovalService.PatchDrawApprovalPaymentPlan(patchDrawPaymentPlanModel, ct));
    }

    [HttpPatch($"{EndpointConstants.DrawApprovals}/{EndpointConstants.Id}/{EndpointConstants.QuoteExpiration}")]
    public async Task<DrawApprovalDto> PatchExpirationDate(
        [FromRoute] string id,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        [FromBody] PatchExpirationDateRequest patchRequest,
        CancellationToken ct)
    {
        var patchExpirationDateModel = new PatchExpirationDateAdminModel
        {
            Id = id,
            UserId = userId,
            NewExpirationDate = patchRequest.NewExpirationDate
        };

        return mapper.Map<DrawApprovalDto>(await drawApprovalService.PatchExpirationDate(patchExpirationDateModel, ct));
    }

    [HttpPost($"{EndpointConstants.DrawApprovals}/{EndpointConstants.Id}/{EndpointConstants.PostTransaction}")]
    public Task PostTransaction(
        [FromRoute] string id,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        [FromBody] NoteDto dto,
        CancellationToken ct)
        => drawApprovalService.PostTransaction(id, userId, mapper.Map<NoteModel>(dto), ct);

    [HttpPatch($"{EndpointConstants.CreditApplication}/{EndpointConstants.Id}")]
    public async Task<CreditApplicationDto> PatchCreditApplicationAdminRequest([FromRoute] string id,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        [FromBody] PatchCreditApplicationAdminDto patchRequest,
        CancellationToken ct)
    {
        var model = mapper.Map<PatchCreditApplicationAdminModel>(patchRequest);
        model.Id = id;
        model.UpdatedBy = userId;

        return mapper.Map<CreditApplicationDto>(await creditApplicationService.PatchAdmin(model, ct));
    }

    [HttpPatch(EndpointConstants.ReviewCreditApplication)]
    public async Task<CreditApplicationDto> ReviewCreditApplication(
        ReviewCreditApplicationDto reviewCreditApplicationDto,
        [FromRoute] string creditApplicationId,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId, CancellationToken ct)
    {
        var application = await creditApplicationService.Review(creditApplicationId, userId, reviewCreditApplicationDto, ct);
        return mapper.Map<CreditApplicationDto>(application);
    }

    [HttpPost($"{EndpointConstants.CreditApplication}/{EndpointConstants.Id}/aradvance")]
    public async Task<StepFunctionsExecutionResponse> RunAsArAdvance([FromRoute] string id,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var arAdvanceCreditApplication = await creditApplicationExecutionService.RunDecisionEngineAsArAdvance(id, ct);
        return arAdvanceCreditApplication;
    }

    [HttpPost($"{EndpointConstants.Companies}/{EndpointConstants.Id}/deadrs/execute")]
    public Task RunRefreshServiceManually([FromRoute] string id,
        [FromBody] ManualRefreshRunRequest request,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        return detectorService.ManualRun(id, request, userId, ScheduleMode.CreateNew, ct);
    }

    [HttpPost($"{EndpointConstants.Companies}/{EndpointConstants.Id}/deadrs/recalculate")]
    public Task RerunCalculationsAfterOverride([FromRoute] string id,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        return detectorService.ManualRun(id, null, userId, ScheduleMode.RecalculateLatest, ct);
    }

    [HttpPatch($"{EndpointConstants.Companies}/{EndpointConstants.Id}/deadrs/lastExecutions/rule/reinstate")]
    public Task ReinstateDecisionEngineRule([FromRoute] string id,
        [FromQuery] string? identifier,
        [FromBody] ReinstateDecisionEngineRuleModel reinstateDecisionEngineRule,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        return decisionEngineStepsService.ReinstateDecisionEngineRule(id, identifier, userId, reinstateDecisionEngineRule, ct);
    }

    [HttpPatch($"{EndpointConstants.Companies}/{EndpointConstants.Id}/deadrs/lastExecutions/rule")]
    public Task IgnoreDecisionEngineRule([FromRoute] string id,
    [FromQuery] string? identifier,
    [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
    [FromBody] IgnoreDecisionEngineRuleModel ignoreDecisionEngineRule,
    CancellationToken ct)
    {
        return decisionEngineStepsService.IgnoreDecisionEngineRule(id, identifier, userId, ignoreDecisionEngineRule, ct);
    }
}