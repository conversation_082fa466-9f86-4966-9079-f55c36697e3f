﻿using BlueTape.OBS.Enums;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;

[BsonIgnoreExtraElements]
public class MerchantSettingsDocument
{
    [BsonElement("cardPricingPackageId")]
    public string? CardPricingPackageId { get; set; }

    [BsonElement("loanPricingPackageId")]
    public string? LoanPricingPackageId { get; set; }

    [BsonElement("isACHDelay")]
    public bool IsAchDelay { get; set; }

    /// <summary>
    /// Trade Credit or ARA debt investor for setting.
    /// Not written when rejected / canceled / etc. or when trade credit opt out
    /// </summary>
    [BsonElement("debtInvestor")]
    [BsonIgnoreIfNull]
    public DebtInvestorType? DebtInvestor { get; set; }

    // Not stored in the database, will be evaluated during query execution
    [BsonElement("cardPricingPackageName")]
    [BsonIgnoreIfNull]
    public string? CardPricingPackageName { get; set; }

    [BsonElement("loanPricingPackageName")]
    [BsonIgnoreIfNull]
    public string? LoanPricingPackageName { get; set; }
}
