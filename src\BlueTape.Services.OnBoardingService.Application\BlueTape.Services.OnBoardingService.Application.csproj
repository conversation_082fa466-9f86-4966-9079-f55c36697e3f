﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="BlueTape.CompanyService.Common" Version="1.1.21" />
    <PackageReference Include="BlueTape.MongoDB" Version="1.1.32" />
    <PackageReference Include="BlueTape.InvoiceService" Version="1.0.40" />
    <PackageReference Include="BlueTape.InvoiceService.Common" Version="1.1.3" />
    <PackageReference Include="BlueTape.OBS" Version="1.6.71" />
    <PackageReference Include="ClosedXML" Version="0.102.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.1" />
	<PackageReference Include="BlueTape.AWSStepFunction" Version="1.0.4" />
	<PackageReference Include="BlueTape.ServiceBusMessaging" Version="1.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.DataAccess.CompanyService\BlueTape.Services.OnBoardingService.DataAccess.CompanyService.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.DataAccess.LMS\BlueTape.Services.OnBoardingService.DataAccess.LMS.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.DataAccess\BlueTape.Services.OnBoardingService.DataAccess.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.Domain\BlueTape.Services.OnBoardingService.Domain.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.Infrastructure\BlueTape.Services.OnBoardingService.Infrastructure.csproj" />
  </ItemGroup>

</Project>
