﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class NoSupplierBankDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("routingNumber")]
    public string? RoutingNumber { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("accountNumber")]
    public NoSupplierBankAccountNumberDocument? AccountNumber { get; set; } = new();
}
