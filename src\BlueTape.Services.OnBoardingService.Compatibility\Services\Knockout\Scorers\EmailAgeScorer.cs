﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Extensions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class EmailAgeScorer(IConfiguration config) : IScoring
{

    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        int passThreshold = config.GetValue<int?>("ScoringThresholds:EmailAgeThresholdPass") ?? 600;
        var result = new List<OwnerScore>();

        if (fraud?.Fraud != null && fraud.Fraud.Any())
        {
            foreach (var item in fraud.Fraud)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                int.TryParse(item.EmailAge?.EmailAgeEaScoreRiskLevel, out var emailAgeRiskLevel);

                ownerScore.Scores?.Add(KnockoutCalculationExtension.Calculate("eaScore", emailAgeRiskLevel, passThreshold));

                result.Add(ownerScore);
            }

            return result;
        }

        int.TryParse(fraud?.EmailAge?.EmailAgeEaScoreRiskLevel, out var emailAgeRiskLevel1);
        var score = KnockoutCalculationExtension.Calculate("eaScore", emailAgeRiskLevel1, passThreshold);
        return [new OwnerScore { Scores = [score] }];
    }
}
