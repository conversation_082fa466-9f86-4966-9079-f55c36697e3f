{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "AllowedHosts": "*", "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_beta"}, "StepsReportOptions": {"BucketName": "beta.uw1.linqpal-temp-assets"}, "SlackNotification": {"ErrorSnsTopicName": "obs-notifications-beta"}, "ConnectorQueueOptions": {"TopicName": "netsuite-connector-beta.fifo"}, "InitializationStepOptions": {"CreditApplicationsStateMachineName": "dotnet-decision-engine-beta", "DrawApprovalsStateMachineName": "dotnet-draw-approval-beta"}}