using MongoDB.Bson;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Services.OnBoardingService.DataAccess.Pipelines;

[ExcludeFromCodeCoverage]
public static class LoanPricingPackagesPipelines
{
    public static readonly BsonDocument LookupLoanPricingPackages = new("$lookup",
        new BsonDocument
        {
            {
                "from", "loanpaymentplans"
            },
            {
                "let", new BsonDocument("loanPaymentPlanId",
                    new BsonDocument("$toObjectId", "$paymentPlanId"))
            },
            {
                "pipeline", new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$eq",
                                new BsonArray
                                {
                                    "$_id",
                                    "$$loanPaymentPlanId"
                                })))
                }
            },
            {
                "as", "loanPaymentPlan"
            }
        });

    public static readonly BsonDocument AddLoanPricingPackages = new("$addFields",
        new BsonDocument
        {
            { "term", "$loanPaymentPlan.term" },
            { "name", "$loanPaymentPlan.name" }
        });

    public static readonly BsonDocument UnwindLoanPricingPackages = new("$unwind",
        new BsonDocument
        {
            {
                "path", "$loanPaymentPlan"
            },
            {
                "preserveNullAndEmptyArrays", true
            }
        });
}