﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Extensions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DecisionEngineSteps;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class DecisionEngineStepsRepository(IObsMongoDBContext context, ILogger<DecisionEngineStepsRepository> logger) : GenericRepository<DecisionEngineStepsDocument>(context, logger),
    IDecisionEngineStepsRepository
{
    private readonly IMongoCollection<DecisionEngineStepsDocument> _collection = context.GetCollection<DecisionEngineStepsDocument>();

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByCreditApplicationId(string creditApplicationId, CancellationToken ct)
    {
        Logger.LogInformation("Get decision engine steps by credit application id: {id}", creditApplicationId);

        var documents = _collection.Find(x => x.CreditApplicationId == creditApplicationId);
        return await documents.ToListAsync(ct);
    }

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByAccountAuthorizationId(string accountAuthorizationId, CancellationToken ct)
    {
        Logger.LogInformation("Get decision engine steps by account authorization id: {accountAuthorizationId}", accountAuthorizationId);

        var documents = _collection.Find(x => x.AccountAuthorizationDetailsId == accountAuthorizationId);
        return await documents.ToListAsync(ct);
    }

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByExecutionId(string executionId, CancellationToken ct)
    {
        Logger.LogInformation("Get decision engine steps by execution id: {executionId}", executionId);

        var documents = _collection.Find(x => x.ExecutionId == executionId);
        return await documents.ToListAsync(ct);
    }

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByCreditApplicationIds(string[] creditApplicationIds, CancellationToken ct)
    {
        Logger.LogInformation("Get decision engine steps by credit application ids");

        var documents = _collection.Find(x => creditApplicationIds.Contains(x.CreditApplicationId));
        return await documents.ToListAsync(ct);
    }

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByDrawApprovalIds(string[] drawApprovalIds, CancellationToken ct)
    {
        Logger.LogInformation("Get decision engine steps by draw approval ids");

        var documents = _collection.Find(x => drawApprovalIds.Contains(x.DrawApprovalId));
        return await documents.ToListAsync(ct);
    }

    public async Task<DecisionEngineStepsDocument?> GetByStepName(string creditApplicationId, StepName stepName, CancellationToken ct)
    {
        var results = await Collection.Aggregate()
            .Match(results => results.CreditApplicationId == creditApplicationId && results.Step == stepName.ToString()).ToListAsync(ct);

        if (!results.Any()) return null;

        return results.Where(x => x != null).MaxBy(x => x!.CreatedAt);
    }

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByDrawApprovalId(string drawApprovalId, CancellationToken ct)
    {
        Logger.LogInformation("Get decision engine steps by draw approval id: {id}", drawApprovalId);

        var documents = _collection.Find(x => x.DrawApprovalId == drawApprovalId);

        return await documents.ToListAsync(ct);
    }

    public async Task MigrateExecutionType(CancellationToken ct)
    {
        var filter = Builders<DecisionEngineStepsDocument>.Filter.Where(x => string.IsNullOrEmpty(x.ExecutionType));
        var docs = await _collection.Find(filter).ToListAsync(ct);
        foreach (var update in docs.Select(doc => Builders<DecisionEngineStepsDocument>.Update.Set(x => x.ExecutionType, GetExecutionTypeByStep(doc))))
        {
            var updateResult = await _collection.UpdateOneAsync(filter, update, cancellationToken: ct);
            if (updateResult.MatchedCount == 0)
                throw new InvalidOperationException("No document was updated, check the filter criteria.");
        }
    }

    public async Task<IEnumerable<DecisionEngineStepsDocument>> GetByFilters(DecisionEngineStepsQuery query, CancellationToken ct)
    {
        var expression = Builders<DecisionEngineStepsDocument>.Filter;
        var filter = expression.Empty;

        if (query.CreditApplicationIds?.Length > 0) filter &= expression.Or(query.CreditApplicationIds?.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.CreditApplicationId));
        if (query.ExecutionTypes?.Length > 0) filter &= expression.Or(query.ExecutionTypes.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.ExecutionType));
        if (query.StepNames?.Length > 0) filter &= expression.Or(query.StepNames?.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.Step));
        if (query.Statuses?.Length > 0) filter &= expression.Or(query.Statuses?.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.Status));
        if (query.AccountAuthorizationDetailIdsForCompanyFilter?.Length > 0 ||
            query.CreditApplicationIdsForCompanyFilter?.Length > 0)
        {
            var companyFilters = new List<FilterDefinition<DecisionEngineStepsDocument>>();
            if (query.AccountAuthorizationDetailIdsForCompanyFilter?.Length > 0)
            {
                var accountFilters =
                        query.AccountAuthorizationDetailIdsForCompanyFilter
                            .ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x =>
                                x.AccountAuthorizationDetailsId);
                companyFilters.AddRange(accountFilters);
            }
            if (query.CreditApplicationIdsForCompanyFilter?.Length > 0)
            {
                var creditAppFilters = query.CreditApplicationIdsForCompanyFilter
                    .ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.CreditApplicationId);
                companyFilters.AddRange(creditAppFilters);
            }
            filter &= expression.Or(companyFilters);
        }
        if (query.DrawApprovalIds?.Length > 0) filter &= expression.Or(query.DrawApprovalIds.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.DrawApprovalId));
        if (query.AccountAuthorizationDetailIds?.Length > 0) filter &= expression.Or(query.AccountAuthorizationDetailIds?.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.AccountAuthorizationDetailsId));
        if (query.ExecutionIds?.Length > 0) filter &= expression.Or(query.ExecutionIds?.ConvertToCaseInsensitiveRegexFiltersList<DecisionEngineStepsDocument>(x => x.ExecutionId));

        var serializerRegistry = BsonSerializer.SerializerRegistry;
        var documentSerializer = serializerRegistry.GetSerializer<DecisionEngineStepsDocument>();
        var i = filter.Render(documentSerializer, serializerRegistry).ToJson();

        return await _collection.Find(filter).ToListAsync(ct);
    }

    private static string GetExecutionTypeByStep(DecisionEngineStepsDocument step)
    {
        return step switch
        {
            { AccountAuthorizationDetailsId: not null and not "" } => DecisionStepExecutionType.Scheduled.ToString(),
            { CreditApplicationId: not null and not "" } => DecisionStepExecutionType.CreditApplication.ToString(),
            { DrawApprovalId: not null and not "" } => DecisionStepExecutionType.DrawApproval.ToString(),
            _ => throw new ArgumentOutOfRangeException(nameof(step), step, $"Cannot get ExecutionType of step with Id {step.Id}")
        };
    }
}
