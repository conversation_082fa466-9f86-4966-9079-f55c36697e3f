﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class PatchAccountAuthorization
{
    public string Id { get; set; } = string.Empty;

    public BusinessDetailsPatch? BusinessDetails { get; set; }

    public IEnumerable<OwnersDetailsPatch>? OwnersDetails { get; set; }

    public IEnumerable<BankAccountDetailsPatch>? BankAccountDetails { get; set; }

    public IEnumerable<OwnersEntitiesDetailsPatch>? OwnersEntitiesDetails { get; set; }

    public AccountAuthorizationCreditDetails? CreditDetails { get; set; }
    public AccountAuthorizationFactoringOverallDetails? FactoringOverallDetails { get; set; }

    public string CreditApplicationId { get; set; } = string.Empty;

    public string UpdatedBy { get; set; } = string.Empty;

    public bool? IsSentBack { get; set; }
}