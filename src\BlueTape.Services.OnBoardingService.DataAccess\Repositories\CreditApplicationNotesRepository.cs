using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationNotes;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class CreditApplicationNotesRepository : GenericRepository<CreditApplicationNoteDocument>, ICreditApplicationNotesRepository
{
    public CreditApplicationNotesRepository(
        IObsMongoDBContext context,
        ILogger<CreditApplicationNotesRepository> logger) : base(context, logger)
    {
    }

    public Task<IEnumerable<CreditApplicationNoteDocument>> GetByApplicationId(string id, CancellationToken ct)
    {
        return GetAll(doc => doc.CreditApplicationId == id && doc.DeletedAt == null, ct);
    }

    public async Task SoftDelete(string id, string userId, CancellationToken ct)
    {
        var document = await GetById(id, ct);
        document.DeletedAt = DateTime.UtcNow;
        document.DeletedBy = userId;
        await Update(document, ct);
    }
}
