﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.PricingPackages;

[BsonIgnoreExtraElements]
[MongoCollection("cardpricingpackages")]
public class CardPricingPackageDocument : Document
{
    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("title")]
    public string? Title { get; set; }

    [BsonElement("description")]
    public string? Description { get; set; }
}
