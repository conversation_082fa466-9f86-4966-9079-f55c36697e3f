﻿using BlueTape.AWSS3.Abstractions;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Reports;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Infrastructure.Options;
using ClosedXML.Excel;
using Microsoft.Extensions.Options;
using Serilog;

namespace BlueTape.Services.OnBoardingService.Application.Services.Reports;

public class ExcelService : IExcelService
{
    private const int CellSizeLimitation = 1000;

    private readonly IS3Client _is3Client;
    private readonly StepsReportOptions _reportOptions;
    private readonly IReportsService _reportsService;

    public ExcelService(IS3Client is3Client, IOptions<StepsReportOptions> reportOptions, IReportsService reportsService)
    {
        _is3Client = is3Client;
        _reportsService = reportsService;
        _reportOptions = reportOptions.Value;
    }

    public Task<string> UploadDecisionEngineStepsReport(string creditApplicationId, CancellationToken ct)
    {
        var bucketName = _reportOptions.BucketName;
        return UploadReport(creditApplicationId, bucketName, ct);
    }

    private async Task<string> UploadReport(string creditApplicationId, string? s3BucketName, CancellationToken ct)
    {
        Log.Information("Started generating report for credit application {id}", creditApplicationId);

        if (s3BucketName is null)
        {
            throw new ArgumentNullException(nameof(s3BucketName));
        }

        using var workbook = new XLWorkbook();
        var createReportData = await _reportsService.RetrieveDataForReportsCreation(creditApplicationId, ct);

        foreach (var stepData in createReportData.ReportStepsData)
        {
            var columnNumber = 1;
            var worksheet = workbook.Worksheets.Add(stepData.StepName.ToString());

            if (stepData.StepName is StepName.Initialization)
            {
                AddBasicFields(stepData, worksheet, ref columnNumber);
                AdjustContent(worksheet);
                continue;
            }

            AddCreditApplicationField(stepData, worksheet, ref columnNumber);
            AddBasicFields(stepData, worksheet, ref columnNumber);
            AddBviResultsFields(stepData, worksheet, ref columnNumber);

            AdjustContent(worksheet);
        }

        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        var bytes = stream.ToArray();
        var time = DateTime.Now.ToString("yyyy_MM_dd_HH_mm");
        var folderName = $@"decision_engine\{creditApplicationId}\";
        var fileName = $@"Decision_Engine_Steps_Report_{time}.csv";
        Log.Information("Finished generating report for credit application {id}." +
                        " Started saving binary", creditApplicationId);

        await _is3Client.SaveBinaryAsync(bytes, folderName + fileName, s3BucketName, ct);

        Log.Information("Finished saving binary for credit application {id}.", creditApplicationId);

        return fileName;
    }

    private static void AddBasicFields(ReportStepDataModel stepData, IXLWorksheet worksheet, ref int columnNumber)
    {
        worksheet.Cell(1, columnNumber).Value = ReportsConstants.CompanyId;
        worksheet.Cell(1, columnNumber + 1).Value = ReportsConstants.Input;
        worksheet.Cell(1, columnNumber + 2).Value = ReportsConstants.Output;

        worksheet.Cell(2, columnNumber).Value = stepData.CompanyId;
        worksheet.Cell(2, columnNumber + 1).Value = stepData.StepInput;
        worksheet.Cell(2, columnNumber + 2).Value = stepData.StepOutput;

        columnNumber += 3;
    }

    private static void AddCreditApplicationField(ReportStepDataModel stepData, IXLWorksheet worksheet, ref int columnNumber)
    {
        worksheet.Cell(1, columnNumber).Value = ReportsConstants.CreditApplicationId;

        worksheet.Cell(2, columnNumber).Value = stepData.CreditApplicationId;

        columnNumber++;
    }

    private static void AddBviResultsFields(ReportStepDataModel stepData, IXLWorksheet worksheet, ref int columnNumber)
    {
        foreach (var bviResult in stepData.BviResults)
        {
            worksheet.Cell(1, columnNumber).Value = bviResult.IntegrationSource;

            var bviResultPartsSplittedByCells = SplitBviResultByCellSize(bviResult.ResponseJsonString).ToList();

            for (var i = 0; i < bviResultPartsSplittedByCells.Count; i++)
            {
                worksheet.Cell(2 + i, columnNumber).Value = bviResultPartsSplittedByCells[i];
            }

            columnNumber++;
        }
    }

    private static void AdjustContent(IXLWorksheet worksheet)
    {
        worksheet.Columns().AdjustToContents();
        worksheet.Columns().Style.Alignment.SetVertical(XLAlignmentVerticalValues.Top);
        worksheet.Columns().Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);
    }

    private static IEnumerable<string> SplitBviResultByCellSize(string bviResult)
    {
        var result = new List<string>();

        while (bviResult.Length > CellSizeLimitation)
        {
            for (var i = CellSizeLimitation; i >= 0; i--)
            {
                if (bviResult[i] == ',')
                {
                    var commaPosition = i;
                    result.Add(bviResult[..(commaPosition + 1)]);
                    bviResult = bviResult[(commaPosition + 1)..];

                    break;
                }
            }
        }

        result.Add(bviResult);
        return result;
    }
}
