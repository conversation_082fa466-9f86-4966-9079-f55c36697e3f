﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;

[BsonIgnoreExtraElements]
public class Knockout
{
    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("score")]
    public double? Score { get; set; }

    [BsonElement("knockout")]
    public KnockoutScores KnockoutScores { get; set; } = new();
}
