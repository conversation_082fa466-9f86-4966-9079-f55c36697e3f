﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using Newtonsoft.Json.Linq;
using TinyHelpers.Extensions;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout;

public class KnockoutService(IDictionary<string, IScoring> scoring) : IKnockoutService
{
    public Models.Knockout.Knockout CalculateScore(LoanApplicationDocument application)
    {
        if (application.Outputs == null)
            return null;

        var kyc = application.Outputs.Find(x => x.Step == StepName.KYC.ToString());
        var kyb = application.Outputs.Find(x => x.Step == StepName.KYB.ToString());
        var bt = application.Outputs.Find(x => x.Step == StepName.BlueTape.ToString());
        var creditStatus = application.Outputs.Find(x => x.Step == "creditStatus");
        var loanDecision = application.Outputs.Find(x => x.Step == "LoanDecision");

        var result = new KnockoutScores();
        KycData? kycData = null;
        KybData? kybData = null;
        ExperianData? experianData = null;
        BluetapeData? btData = null;
        LoanDecisionData? decisionData = null;

        if (kyc != null && !kyc.Data.ToJson().IsNullOrEmpty())
            kycData = JObject.Parse(kyc.Data.ToJson()).ToObject<KycData>();

        if (kyb != null && !kyb.Data.ToJson().IsNullOrEmpty())
            kybData = JObject.Parse(kyb.Data.ToJson()).ToObject<KybData>();

        var creditStatusJson = creditStatus?.Data.ToJson();
        if (creditStatus != null && !creditStatusJson.IsNullOrEmpty())
            experianData = JObject.Parse(creditStatus.Data.ToJson()).ToObject<ExperianData>();

        var btJson = bt?.Data.ToJson();
        if (btJson != null && btJson.IsNullOrEmpty())
            btData = JObject.Parse(btJson).ToObject<BluetapeData>();

        if (loanDecision?.Data != null)
        {
            decisionData = BsonSerializer.Deserialize<LoanDecisionData>(loanDecision.Data.ToBsonDocument());
        }

        foreach (var scorer in scoring)
        {
            var ownerScores = scorer.Value.Decide(kycData, kybData, experianData, decisionData);

            foreach (var item in ownerScores)
            {
                var owner = result.Owners.FirstOrDefault(x => x.Owner?.Id == item?.Owner?.Id);

                if (owner == null)
                {
                    result.Owners.Add(item);
                }
                else
                {
                    owner.Scores?.AddRange(item.Scores);
                }
            }
        }

        var score = result.Scores
            .Select(s => (double)s.Pass)
            .Concat(result.Owners.SelectMany(o => o.Scores?.Select(s => (double)s.Pass)!))
            .Average();

        var status = (btData?.RejectedApplicationsMatchingSsnEin ?? 0) > 0
            ? "fail"
            : Math.Abs(score - 1) < 0.1
                ? "passed"
                : "manual";

        return new Models.Knockout.Knockout
        {
            Status = status,
            Score = score,
            KnockoutScores = result
        };
    }
}
