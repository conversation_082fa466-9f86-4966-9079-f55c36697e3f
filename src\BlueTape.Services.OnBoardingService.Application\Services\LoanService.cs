﻿using AutoMapper;
using BlueTape.LS.DTOs.Credit;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Credit;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class LoanService : ILoanService
{
    private readonly ILoanExternalService _loanExternalService;
    private readonly IMapper _mapper;

    public LoanService(ILoanExternalService loanExternalService, IMapper mapper)
    {
        _loanExternalService = loanExternalService;
        _mapper = mapper;
    }

    public async Task<CreditModel> CreateCredit(CreateCreditModel requestModel, CancellationToken ct)
    {
        return _mapper.Map<CreditModel>(await _loanExternalService.CreateCredit(_mapper.Map<CreateCreditDto>(requestModel), ct));
    }
}
