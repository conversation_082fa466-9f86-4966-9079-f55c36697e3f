﻿using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IDrawApprovalService
{
    Task<IEnumerable<DrawApproval>> Get(CancellationToken ct);

    Task<GetQueryWithPaginationResultModel<DrawApproval>> GetByFilter(GetDrawApprovalsQueryWithPagination query, CancellationToken ct);

    Task<DrawApproval?> GetById(string id, CancellationToken ct);
    Task<DrawApproval?> GetByInvoicesIds(string[] invoicesIds, CancellationToken ct);
    Task<IEnumerable<DrawApproval?>> GetManyByInvoicesIds(string[] invoicesIds, CancellationToken ct);
    Task<DrawApproval> Patch(PatchDrawApproval patchDrawApproval, CancellationToken ct);

    Task<DrawApproval> ReviewDrawApproval(ReviewDrawApprovalModel review, CancellationToken ct);

    Task<DrawApproval> Create(CreateDrawApproval createDrawApproval, CancellationToken ctx);
    Task<DrawApproval> AddInvoices(string id, List<PayableItem> payables, CancellationToken ct);
    Task<DrawApproval> PostTransaction(string id, string userId, NoteModel model, CancellationToken ct);

    Task<DrawApproval> PatchDrawApprovalDetails(string id, PatchInternalDrawApproval patchDrawApproval, CancellationToken ctx);

    Task<DrawApproval> PatchDrawApprovalPaymentPlan(PatchDrawPaymentPlanAdminModel model, CancellationToken ct);

    Task<DrawApproval> PatchExpirationDate(PatchExpirationDateAdminModel model, CancellationToken ct);

    Task<DrawApproval> UpdateDrawApproval(string id, UpdateDrawApproval updateDrawApproval, CancellationToken ct);

    Task<string?> GetProjectIdByDrawId(string id, CancellationToken ctx);
    Task<DrawApproval> PatchAuthorizationPeriod(string drawApprovalId, string authPeriodId, CancellationToken ct);
    Task<StepFunctionsExecutionResponse> RunDrawApprovalDecisionEngineExecution(DrawApprovalDecisionEngineExecutionRequest request, CancellationToken ctx);
    Task<StepFunctionsExecutionResponse> RunDecisionEngineInitializationStepForDrawApproval(DrawApprovalInitializationStepStartRequest request, CancellationToken ctx);
    Task MigrateDrawApprovalCreditAndAccountStatuses(CancellationToken ct);
    Task SyncDrawApprovalChanges(string drawApprovalId, CancellationToken ct);
}
