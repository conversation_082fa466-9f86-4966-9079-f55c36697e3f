﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;

public class UpdateCreditApplication
{
    public string? Id { get; set; }

    public string? StatusNote { get; set; }

    public string? NewStatus { get; set; }

    public decimal? CreditLimit { get; set; }

    public string? BusinessName { get; set; }

    public decimal? ApprovedCreditLimit { get; set; }

    public string? BusinessDba { get; set; }

    public string? BusinessCategory { get; set; }

    public string? ApplicantName { get; set; }

    public decimal? RequestedAmount { get; set; }

    public string? AutomatedDecisionResult { get; set; }

    public string? ArAdvanceApplicationId { get; set; }

    public string? UpdatedBy { get; set; }

    public string? ExecutionId { get; set; }
    public bool? ShouldIgnoreCaching { get; set; }
    public BankAccountType? BankAccountType { get; set; }
}