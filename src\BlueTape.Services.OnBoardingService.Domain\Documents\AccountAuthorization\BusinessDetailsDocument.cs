﻿using BlueTape.MongoDB.DTO;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;

[BsonIgnoreExtraElements]
public class BusinessDetailsDocument
{
    [BsonElement("LastEINRejectionDate")]
    public DateTime? LastEINRejectionDate { get; set; }

    [BsonElement("LoansLastDefaultedDate")]
    public DateTime? LoansLastDefaultedDate { get; set; }

    [BsonElement("BusinessStartDate")]
    public DateTime? BusinessStartDate { get; set; }

    [BsonElement("BRICodes")]
    public IEnumerable<string?> BRICodes { get; set; } = Enumerable.Empty<string?>();

    [BsonElement("BVI")]
    public string? BVI { get; set; }

    [BsonElement("ReliabilityCode")]
    public string? ReliabilityCode { get; set; }

    [BsonElement("FirstReportedTradeLineDate")]
    public DateTime? FirstReportedTradeLineDate { get; set; }

    [BsonElement("BankruptcyIndicator")]
    public bool? BankruptcyIndicator { get; set; }

    [BsonElement("LastBankruptcyDate")]
    public DateTime? LastBankruptcyDate { get; set; }

    [BsonElement("JudgmentIndicator")]
    public bool? JudgmentIndicator { get; set; }

    [BsonElement("LastJudgmentDate")]
    public DateTime? LastJudgmentDate { get; set; }

    [BsonElement("JudgmentBalance")]
    public decimal? JudgmentBalance { get; set; }

    [BsonElement("LienIndicator")]
    public bool? LienIndicator { get; set; }

    [BsonElement("LastLienDate")]
    public DateTime? LastLienDate { get; set; }

    [BsonElement("LienBalance")]
    public decimal? LienBalance { get; set; }

    [BsonElement("DBT60PlusPercentage")]
    public decimal? DBT60PlusPercentage { get; set; }

    [BsonElement("LoanRevenue")]
    public decimal? LoanRevenue { get; set; }

    [BsonElement("LienRevenue")]
    public decimal? LienRevenue { get; set; }

    [BsonElement("JudgmentRevenue")]
    public decimal? JudgmentRevenue { get; set; }

    [BsonElement("PastDueAmount")]
    public decimal? PastDueAmount { get; set; }

    [BsonElement("CompanyIncome")]
    public decimal? CompanyIncome { get; set; }

    [BsonElement("AnnualRevenue")]
    public decimal? AnnualRevenue { get; set; }

    [BsonElement("InquiriesDuringLast6Months")]
    [BsonIgnoreIfNull]
    public decimal? InquiriesDuringLast6Months { get; set; }

    [BsonElement("RevenueVariancePercentage")]
    public decimal? RevenueVariancePercentage { get; set; }

    [BsonElement("DTI2Value")]
    public decimal? DTI2Value { get; set; }

    [BsonElement("TradeLinesPercentage")]
    public decimal? TradeLinesPercentage { get; set; }

    [BsonElement("TotalTradeLines")]
    public decimal? TotalTradeLines { get; set; }

    [BsonElement("businessOutstandingBalance")]
    public decimal? BusinessOutstandingBalance { get; set; }

    [BsonElement("loanDebt")]
    public decimal? LoanDebt { get; set; }

    [BsonElement("currentDebt")]
    public decimal? CurrentDebt { get; set; }

    [BsonElement("availableCreditLimit")]
    public decimal? AvailableCreditLimit { get; set; }

    [BsonElement("debtAdjustor")]
    public decimal? DebtAdjustor { get; set; }

    [BsonElement("acceptablePercentRevenue")]
    public decimal? AcceptablePercentRevenue { get; set; }

    [BsonElement("revenueByCustomer")]
    public decimal? RevenueByCustomer { get; set; }

    [BsonElement("debtByCustomer")]
    public decimal? DebtByCustomer { get; set; }

    [BsonElement("totalAcceptableDebtAmount")]
    public decimal? TotalAcceptableDebtAmount { get; set; }

    [BsonElement("DBT60PlusAmount")]
    public decimal? DBT60PlusAmount { get; set; }

    [BsonElement("creditUtilizationRatio")]
    public decimal? CreditUtilizationRatio { get; set; }

    [BsonElement("DBT60PlusAndRevenueRatio")]
    public decimal? DBT60PlusAndRevenueRatio { get; set; }

    [BsonElement("judgmentAndRevenueRatio")]
    public decimal? JudgmentAndRevenueRatio { get; set; }

    [BsonElement("lienAndRevenueRatio")]
    public decimal? LienAndRevenueRatio { get; set; }

    [BsonElement("isEntityCoOwnersExist")]
    public bool? IsEntityCoOwnersExist { get; set; }

    [BsonElement("bin")]
    public ParsedBodyLoggingDocument? Bin { get; set; }
}