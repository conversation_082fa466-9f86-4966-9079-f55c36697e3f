using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;

namespace BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Services;

public class CompanyExternalService : ICompanyExternalService
{
    private readonly ICompanyHttpClient _companyHttpClient;
    public CompanyExternalService(ICompanyHttpClient companyHttpClient)
    {
        _companyHttpClient = companyHttpClient;
    }

    public Task<CompanyModel?> GetById(string id, CancellationToken ctx)
    {
        return _companyHttpClient.GetCompanyByIdAsync(id, ctx);
    }

    public Task<IEnumerable<CashFlowItemResponse>?> GetPlaidAssetReport(string companyId, AssetReportQuery query, CancellationToken ct)
    {
        return _companyHttpClient.GetPlaidAssetReport(companyId, query, ct);
    }

    public Task<PaginatedCompanyResponse?> GetCompaniesByQueryAsync(CompanyQueryPaginated query, CancellationToken ct)
    {
        return _companyHttpClient.GetCompaniesByQueryAsync(query, ct);
    }

    public Task<CustomerModel?> SetCustomerIhcSettings(string customerId, UpdateCustomerModel updateCustomerModel, CancellationToken ct)
    {
        return _companyHttpClient.SetCustomerIhcSettings(customerId, updateCustomerModel, ct);
    }

    public async Task UpdateCompany(string id, string userId, UpdateCompanyModel updateCompanyModel, CancellationToken ct)
    {
        await _companyHttpClient.UpdateCompanyAsync(id, userId, updateCompanyModel, ct);
    }
}
