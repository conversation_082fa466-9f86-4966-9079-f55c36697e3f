using BlueTape.Functions.AuthorizationDetailsRefreshDetector;
using BlueTape.Functions.Hosting.Extensions;
using Microsoft.Extensions.Hosting;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureBlueTapeFunctionAppHostConfiguration()
    .ConfigureBlueTapeSerilog(nameof(AuthorizationDetailsRefreshDetector))
    .ConfigureBlueTapeServices()
    .Build();

await host.RunAsync();
