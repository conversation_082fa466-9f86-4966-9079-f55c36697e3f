﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;

public static class StringExtensions
{
    public static string ToFormattedJsonString(this string? jsonString)
    {
        if (!IsValidJson(jsonString)) return jsonString ?? string.Empty;
        return JToken.Parse(jsonString!).ToString(Formatting.Indented);
    }

    private static bool IsValidJson(string? strInput)
    {
        if (string.IsNullOrWhiteSpace(strInput)) return false;

        strInput = strInput.Trim();
        if ((!strInput.StartsWith('{') || !strInput.EndsWith('}')) &&
            (!strInput.StartsWith('[') || !strInput.EndsWith(']'))) return false;
        try
        {
            JToken.Parse(strInput);
            return true;
        }
        catch (JsonReaderException jex)
        {
            Console.WriteLine(jex.Message);
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());
            return false;
        }
    }
}
