module "capp" {
  source = "../../modules/mod-capp"

  environment                       = var.environment
  application_name                  = var.application_name
  container_registry_name           = var.container_registry_name
  image_version                     = var.image_version
  acr_password                      = var.acr_password
  client_id                         = var.client_id
  tenant_id                         = var.tenant_id
  client_secret                     = var.client_secret
  aws_secret_access_key             = var.aws_secret_access_key
  aws_access_key_id                 = var.aws_access_key_id
  aws_default_region                = var.aws_default_region
  lp_aws_account                    = var.lp_aws_account
  key_vault_uri                     = data.terraform_remote_state.core.outputs.key_vault_uri
  container_app_environment_id      = data.terraform_remote_state.core.outputs.container_app_environment_id
  resource_group_id                 = data.terraform_remote_state.core.outputs.resource_group_id
  resource_group_name               = data.terraform_remote_state.core.outputs.resource_group_name
  container_registry_login_server   = data.terraform_remote_state.core.outputs.container_registry_login_server
  container_registry_admin_username = data.terraform_remote_state.core.outputs.container_registry_admin_username
}

module "fapp" {
  source = "../../modules/mod-fapp"

  application_name                               = var.application_name
  environment                                    = var.environment
  location                                       = var.location
  key_vault_uri                                  = data.terraform_remote_state.core.outputs.key_vault_uri
  client_id                                      = var.client_id
  tenant_id                                      = var.tenant_id
  client_secret                                  = var.client_secret
  aws_secret_access_key                          = var.aws_secret_access_key
  aws_access_key_id                              = var.aws_access_key_id
  aws_default_region                             = var.aws_default_region
  lp_aws_account                                 = var.lp_aws_account
  resource_group_name                            = data.terraform_remote_state.core.outputs.resource_group_name
  service_plan_core_id                           = data.terraform_remote_state.core.outputs.service_plan_core_id
  storage_account_name                           = data.terraform_remote_state.core.outputs.storage_account_functions_name
  storage_account_access_key                     = data.terraform_remote_state.core.outputs.storage_account_functions_access_key
  app_insights_connection_string                 = data.terraform_remote_state.core.outputs.app_insights_connection_string
  app_insights_instrumentation_key               = data.terraform_remote_state.core.outputs.app_insights_instrumentation_key
  user_assigned_identity_core_id                 = data.terraform_remote_state.core.outputs.user_assigned_identity_core_id
  functions_worker_runtime                       = var.functions_worker_runtime
  dotnet_version                                 = var.dotnet_version
  function_extension_version                     = var.function_extension_version
  storage_account_primary_connection_string      = data.terraform_remote_state.core.outputs.storage_account_functions_primary_connection_string
  loan_application_sync_queue_name               = module.sbus.azurerm_servicebus_queue_sblas_name
  loan_application_sync_queue_connection         = module.sbus.azurerm_servicebus_queue_sblas_connection
  authorization_details_refresh_queue_name       = module.sbus.azurerm_servicebus_queue_sbdeadrs_name
  authorization_details_refresh_queue_connection = module.sbus.azurerm_servicebus_queue_sbdeadrs_connection
  virtual_network_subnet_id                      = data.terraform_remote_state.core.outputs.subnet_ids.fapp
}

module "sbus" {
  source = "../../modules/mod-sbus"

  environment                       = var.environment
  application_name                  = var.application_name
  key_vault_id                      = data.terraform_remote_state.core.outputs.key_vault_id
  resource_group_name               = data.terraform_remote_state.core.outputs.resource_group_name
  resource_group_location           = data.terraform_remote_state.core.outputs.resource_group_location
}