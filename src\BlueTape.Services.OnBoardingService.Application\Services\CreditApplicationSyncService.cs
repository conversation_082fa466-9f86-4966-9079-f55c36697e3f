﻿using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Models.Compatibility;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Extensions;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class CreditApplicationSyncService : ICreditApplicationSyncService
{
    private readonly ILoanApplicationSyncMessageSender _messageSender;

    public CreditApplicationSyncService(ILoanApplicationSyncMessageSender messageSender)
    {
        _messageSender = messageSender;
    }

    public Task SyncApplicableCreditApplication(CreditApplicationDocument creditApplication, CancellationToken ct)
    {
        if (IsApplicableApplicationType(creditApplication))
        {
            return _messageSender.SendMessage(new ServiceBusMessageBt<SyncLoanApplicationMessagePayload>(new()
            {
                CreditApplicationId = creditApplication.Id,
                SyncType = SyncType.SyncCreditApplication
            }), ct);
        }

        return Task.CompletedTask;
    }

    private static bool IsApplicableApplicationType(CreditApplicationDocument creditApplication)
    {
        return CreditApplicationType.LineOfCredit.IsEnum(creditApplication.Type) ||
               CreditApplicationType.GetPaid.IsEnum(creditApplication.Type);
    }
}
