﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;

public interface IManualAuthorizationDetailsRefreshDetectorService
{
    Task ManualRun(string companyId, ManualRefreshRunRequest? manualRefreshRunRequest, string userId,
        ScheduleMode scheduleMode, CancellationToken ctx);
}
