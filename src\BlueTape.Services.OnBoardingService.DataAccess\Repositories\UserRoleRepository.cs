﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.User;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class UserRoleRepository(IObsMongoDBContext context, ILogger<GenericRepository<UserRoleDocument>> logger)
    : GenericRepository<UserRoleDocument>(context, logger), IUserRoleRepository
{
    private readonly IMongoCollection<UserRoleDocument> _collection = context.GetCollection<UserRoleDocument>();

    public async Task<UserRoleDocument?> GetByCompanyId(string companyId, CancellationToken ct)
    {
        var result = _collection.Find(x => x.CompanyId == companyId && x.Role == "Owner");
        var userRoleDocument = await result.FirstOrDefaultAsync(ct);

        return userRoleDocument;
    }
}
