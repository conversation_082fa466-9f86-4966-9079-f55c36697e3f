﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class LoanDecisionData
{
    [BsonElement("loanApproved")]
    public bool? LoanApproved { get; set; }

    [BsonElement("decision")]
    public Decision? Decision { get; set; }
}

[BsonIgnoreExtraElements]
public class Decision
{
    [BsonElement("business_outstanding_balance")]
    public double? BusinessOutstandingBalance { get; set; }

    [BsonElement("approved_amount")]
    public double? ApprovedAmount { get; set; }

    [BsonElement("min_amount")]
    public double? MinAmount { get; set; }

    [BsonElement("loan_revenue")]
    public double? LoanRevenue { get; set; }

    [BsonElement("annual_revenue")]
    public double? AnnualRevenue { get; set; }

    [BsonElement("loan_debt")]
    public double? LoanDebt { get; set; }

    [BsonElement("debt_amount")]
    public double? DebtAmount { get; set; }

    [BsonElement("total_acceptable_debt_amount")]
    public double? TotalAcceptableDebtAmount { get; set; }

    [BsonElement("available_credit_limit")]
    public double? AvailableCreditLimit { get; set; }

    [BsonElement("debt_adjustor")]
    public double? DebtAdjustor { get; set; }

    [BsonElement("acceptable_debt_of_revenue")]
    public double? AcceptableDebtOfRevenue { get; set; }

    [BsonElement("revenue_est_provided_in_app")]
    public double? RevenueEstProvidedInApp { get; set; }

    [BsonElement("debt_estimate_provided_in_app")]
    public double? DebtEstimateProvidedInApp { get; set; }

    [BsonElement("debt_experian")]
    public double? DebtExperian { get; set; }

    [BsonElement("loanApproved")]
    public bool? LoanApproved { get; set; }

    [BsonElement("businessAge")]
    public string? BusinessAge { get; set; }
}
