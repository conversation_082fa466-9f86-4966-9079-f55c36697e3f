using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class AutomatedApprovalDetailsModel
{
    public decimal? DrawLimit { get; set; }
    public decimal? DailyAmountLimit { get; set; }
    public decimal? WeeklyAmountLimit { get; set; }
    public decimal? CreditLimitPercentage { get; set; }

    public decimal? AutoApprovedDrawsAmountThisDay { get; set; }
    public decimal? AutoApprovedDrawsAmountLastSevenDays { get; set; }
    
    public VelocityCheckResult? VelocityCheckResult { get; set; }
    public AutomatedApprovalResultReason? Reason { get; set; }
}