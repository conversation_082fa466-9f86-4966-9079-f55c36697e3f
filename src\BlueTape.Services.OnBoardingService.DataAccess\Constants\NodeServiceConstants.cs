﻿namespace BlueTape.Services.OnBoardingService.DataAccess.Constants;

public static class NodeServiceConstants
{
    public const string NodeServiceUrl = "BLUETAPE-API-URL";
    public const string NodeServiceApiKey = "BLUETAPE-API-KEY";

    public const string HumanApprovalPath = "v1/internal/human-approval";
    public const string IssueFactoringLoanPath = "v1/internal/issue-factoring-loan";
    public const string IssueLoanPrequalifiedPath = "v1/internal/run-issue-loan-prequalified";
    public const string OpsTeamNotificationPath = "v1/internal/run-notify-ops-team";
    public const string ApprovalUserNotificationPath = "v1/internal/run-approve-user-notification";
    public const string LoanCanceledNotificationPath = "v1/internal/run-loan-canceled-notify-customer";
    public const string SendBackNotificationPath = "v1/internal/run-send-back-notify-user";
    public const string RejectLoan = "v1/internal/run-reject-loan";
    public const string PostTransactionPath = "v1/internal/run-quote-service-post-transaction";
    public const string UpdateAuthorizationPath = "v1/internal/run-quote-service-update-authorization";
    public const string CancelLoanApplication = "v1/internal/run-cancel-loan-application";
    public const string IhcDrawApprovalNotificationPath = "v1/internal/send-factoring-invoice-decision-supplier-notification";
    public const string InvoiceStatus = "v1/internal/invoice-status";
}
