﻿namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
public class DecisionEngineStepsBviResultsModel
{
    public string? Id { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string DecisionEngineStepId { get; set; } = string.Empty;
    public string IntegrationLogId { get; set; } = string.Empty;
    public string IntegrationSource { get; set; } = string.Empty;
    public string? CreditApplicationId { get; set; }
    public string? ExecutionId { get; set; }
}
