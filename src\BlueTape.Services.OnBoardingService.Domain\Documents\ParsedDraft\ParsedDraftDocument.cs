﻿using BlueTape.MongoDB.Attributes;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.ParsedDraft;

[BsonIgnoreExtraElements]
[MongoCollection("parsedDraftDocument")]
public class ParsedDraftDocument : Document
{
    [BsonElement("businessAddress")]
    public AddressDocument? BusinessAddress { get; set; }

    [BsonElement("businessName")]
    public BusinessNameDocument? BusinessName { get; set; }

    [BsonElement("businessPhone")]
    public string? BusinessPhone { get; set; }

    [BsonElement("businessStartDate")]
    public string? BusinessStartDate { get; set; }

    [BsonElement("ein")]
    public CipherDocument? Ein { get; set; }

    [BsonElement("coOwnersInfo")]
    public List<CoOwnerDocument> CoOwnersInfo { get; set; } = new();

    [BsonElement("ownerFirstName")]
    public string? OwnerFirstName { get; set; }

    [BsonElement("ownerLastName")]
    public string? OwnerLastName { get; set; }

    [BsonElement("ownerAddress")]
    public AddressDocument? OwnerAddress { get; set; }

    [BsonElement("ownerPhone")]
    public string? OwnerPhone { get; set; }

    [BsonElement("ownerEmail")]
    public string? OwnerEmail { get; set; }

    [BsonElement("ownerBirthDate")]
    public string? OwnerBirthDate { get; set; }

    [BsonElement("ownerSsn")]
    public CipherDocument? OwnerSsn { get; set; }

    [BsonElement("bankDetails")]
    public BankDetailsDocument? BankDetails { get; set; }

    [BsonElement("revenue")]
    public decimal? Revenue { get; set; }

    [BsonElement("debt")]
    public decimal? Debt { get; set; }

    [BsonElement("requestedAmount")]
    public decimal? RequestedAmount { get; set; }

    [BsonElement("companyId")]
    public string? CompanyId { get; set; }

    [BsonElement("draftId")]
    public string? DraftId { get; set; }

    [BsonElement("ownerId")]
    public string? OwnerId { get; set; }

    [BsonElement("isPrincipal")]
    public bool? IsPrincipal { get; set; }

    [BsonElement("ownerPercentOwned")]
    public int OwnerPercentOwned { get; set; }

    [BsonElement("arAdvanceRequested")]
    public bool? ArAdvanceRequested { get; set; }
}
