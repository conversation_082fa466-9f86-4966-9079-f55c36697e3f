﻿using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Serilog.Context;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService;

public class ManualAuthorizationDetailsRefreshDetectorService : IManualAuthorizationDetailsRefreshDetectorService
{
    private readonly IAuthorizationDetailsRefreshConfigurationService _configurationService;
    private readonly ICreditApplicationNotesService _creditApplicationNotesService;
    private readonly ICreditApplicationRepository _creditApplicationRepository;
    private readonly ICompanyManualScheduledUpdateEventsGenerator _eventsGenerator;
    private readonly IDecisionEngineStepsRepository _decisionEngineStepsRepository;
    private readonly IDateProvider _dateProvider;
    private readonly IRefreshServiceMessageSender _refreshServiceMessageSender;
    private readonly ICompanyService _companyService;
    private readonly ILogger<ManualAuthorizationDetailsRefreshDetectorService> _logger;

    public ManualAuthorizationDetailsRefreshDetectorService(
        IAuthorizationDetailsRefreshConfigurationService configurationService,
        ICreditApplicationRepository creditApplicationRepository,
        ICompanyManualScheduledUpdateEventsGenerator eventsGenerator,
        ICreditApplicationNotesService creditApplicationNotesService,
        IDateProvider dateProvider,
        IDecisionEngineStepsRepository decisionEngineStepsRepository,
        IRefreshServiceMessageSender refreshServiceMessageSender,
        ICompanyService companyService,
        ILogger<ManualAuthorizationDetailsRefreshDetectorService> logger)
    {
        _configurationService = configurationService;
        _creditApplicationRepository = creditApplicationRepository;
        _creditApplicationNotesService = creditApplicationNotesService;
        _dateProvider = dateProvider;
        _eventsGenerator = eventsGenerator;
        _decisionEngineStepsRepository = decisionEngineStepsRepository;
        _refreshServiceMessageSender = refreshServiceMessageSender;
        _companyService = companyService;
        _logger = logger;
    }

    public async Task ManualRun(string companyId, ManualRefreshRunRequest? manualRefreshRunRequest, string userId, ScheduleMode scheduleMode, CancellationToken ctx)
    {
        _logger.LogInformation("Started manual details refresh detector execution");
        var company = await _companyService.GetCompanyById(companyId, ctx) ?? throw new VariableNullException($"Company {companyId} does not exist");
        var approvedCreditApplications = await GetApprovedCreditApplications(new List<CompanyModel>() { company }, ctx);
        var scheduledChecks = await _configurationService.GetRefreshChecks(ctx);
        var refreshServiceMessages = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>();
        using (LogContext.PushProperty("CompanyId", company.Id))
        {
            foreach (var scheduledCheck in scheduledChecks)
            {
                // for manual run we disable filtering to expiration or the schedule type
                var companyEvents = _eventsGenerator.GenerateScheduledUpdateEvents(company, scheduledCheck,
                    approvedCreditApplications, scheduleMode, userId);
                refreshServiceMessages.AddRange(companyEvents);
            }

            if (scheduleMode == ScheduleMode.CreateNew)
            {
                await GenerateCreditApplicationNotes(userId, manualRefreshRunRequest, companyId, approvedCreditApplications, scheduledChecks, ctx);
            }
        }
        await SendManualExecutionEventsToRefreshService(refreshServiceMessages, ctx);

        _logger.LogInformation("Finished manual authorization details refresh detector execution");
    }

    private async Task<List<LightCreditApplicationDocument>> GetApprovedCreditApplications(List<CompanyModel> activeCompanies, CancellationToken ctx)
    {
        var companyIds = activeCompanies.Select(company => company.Id).ToArray();
        var approvedCreditApplications = (await _creditApplicationRepository.GetLightCreditApplications(companyIds,
            CreditApplicationStatus.Approved.ToString(),
            ctx)).ToList();

        return approvedCreditApplications;
    }

    private Task SendManualExecutionEventsToRefreshService(IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> events, CancellationToken ct)
    {
        if (events.Any()) return _refreshServiceMessageSender.SendMessages(events, ct);

        _logger.LogInformation("SendManualExecutionEventsToRefreshService: No events to send to refresh service");
        return Task.CompletedTask;
    }

    private Task GenerateCreditApplicationNotes(string userId,
        ManualRefreshRunRequest? manualRefreshRunRequest,
        string companyId,
        List<LightCreditApplicationDocument> approvedCreditApplications,
       IReadOnlyList<RefreshCheckConfiguration> scheduledChecks,
       CancellationToken ctx)
    {
        var createCreditApplicationNotes = new List<CreateCreditApplicationNote>();

        var creditApplicationIds = new List<string>();

        foreach (var scheduledCheck in scheduledChecks)
        {
            creditApplicationIds.AddRange(approvedCreditApplications
               .Where(ca =>
                   ca.CompanyId == companyId &&
                   ca.Type.ParseToEnum<CreditApplicationType>() is CreditApplicationType type &&
                   scheduledCheck.CreditApplicationTypes.Contains(type))
               .Select(ca => ca.Id)
               .Distinct());
        }

        creditApplicationIds = creditApplicationIds.Distinct().ToList();

        if (manualRefreshRunRequest != null &&
            manualRefreshRunRequest.ExecutionFlow == RefreshServiceManualOverrideExecutionFlow.ClearOverrides)
        {
            createCreditApplicationNotes.AddRange(
                CreateCreditApplicationNoteList(userId, manualRefreshRunRequest.Note,
                AuthorizationDetailsRefreshDetectorConstants.OverridesClearedCaption, creditApplicationIds));
        }

        if (manualRefreshRunRequest != null &&
             manualRefreshRunRequest.ExecutionFlow == RefreshServiceManualOverrideExecutionFlow.Rerun)
        {
            createCreditApplicationNotes.AddRange(
                CreateCreditApplicationNoteList(userId, null,
                AuthorizationDetailsRefreshDetectorConstants.RerunCalculationsCaption, creditApplicationIds));
        }

        return _creditApplicationNotesService.AddRange(createCreditApplicationNotes, ctx);
    }

    private IEnumerable<CreateCreditApplicationNote> CreateCreditApplicationNoteList(string userId, string? note, string caption, IEnumerable<string> approvedCreditApplicationsIds)
    {
        var createCreditApplicationNotes = new List<CreateCreditApplicationNote>();

        foreach (var creditApplicationId in approvedCreditApplicationsIds)
        {
            createCreditApplicationNotes.Add(new CreateCreditApplicationNote()
            {
                Caption = caption,
                CreditApplicationId = creditApplicationId,
                CreatedAt = _dateProvider.CurrentDateTime,
                CreatedBy = userId,
                Note = note ?? "",
            });
        }

        return createCreditApplicationNotes;
    }
}