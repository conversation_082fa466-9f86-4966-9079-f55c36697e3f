﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class PersonalBankruptcyScorer : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        var result = new List<OwnerScore>();

        if (experian?.OwnersData != null && experian.OwnersData.Any())
        {
            foreach (var item in experian.OwnersData)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                ownerScore.Scores?.Add(Calculate("personalBankruptcy", item.LastBankruptcyDate));

                result.Add(ownerScore);
            }
        }

        return EvaluateScores(result);
    }

    private Score Calculate(string name, DateTime? lastBankruptcyDate)
    {
        ScoringResult pass;

        if (lastBankruptcyDate.HasValue)
        {
            DateTime twoYearsAfterBankruptcy = lastBankruptcyDate.Value.AddYears(2);
            pass = lastBankruptcyDate > twoYearsAfterBankruptcy ? ScoringResult.Pass : ScoringResult.Reject;
        }
        else
        {
            pass = ScoringResult.Pass;
        }

        return new Score()
        {
            Name = name,
            Value = lastBankruptcyDate.HasValue ? lastBankruptcyDate.ToString() : null,
            Pass = pass,
        };
    }

    public static List<OwnerScore> EvaluateScores(List<OwnerScore> scores)
    {
        if (scores.Count > 0)
        {
            if (scores.Any(item => item.Owner == null))
            {
                return [new OwnerScore
                {
                    Scores = new List<Score>()
                    {
                        new()
                        {
                            Name = "personalBankruptcy",
                            Value = scores[0].Scores?[0].Value,
                            Pass = scores[0].Scores![0].Pass
                        }

                    }

                }];
            }
            else
            {
                return scores;
            }
        }
        else
        {
            return [new OwnerScore
            {
                Scores = new List<Score>()
                {
                    new()
                    {
                        Name = "personalBankruptcy",
                        Value = null,
                        Pass = ScoringResult.Review
                    }
                }
            }];
        }
    }
}
