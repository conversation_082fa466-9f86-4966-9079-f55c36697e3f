using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;

[BsonIgnoreExtraElements]
public class AccountAuthorizationFactoringOverallDetailsDocument
{
    [BsonElement("lastEinRejectionDate")]
    public DateTime? LastEinRejectionDate { get; set; }
    
    [BsonElement("lastDefaultedDate")]
    public DateTime? LastDefaultedDate { get; set; }
}