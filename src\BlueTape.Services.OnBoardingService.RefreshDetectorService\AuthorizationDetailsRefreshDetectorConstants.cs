﻿using System.Collections.Immutable;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService;

public static class AuthorizationDetailsRefreshDetectorConstants
{
    public const string CreatedBy = "BlueTape.DEADRS.Detector";
    public const string ScheduledUpdateEventType = "ScheduledUpdate";
    public const string RefreshDetectorCompaniesList = "OBS-REFRESH-DETECTOR-COMPANIES";

    public const string OverridesClearedAutomaticallyCaption = "Overrides cleared automatically";
    public const string OverridesClearedCaption = "Overrides cleared";
    public const string RerunCalculationsCaption = "Rerun calculations";

    public static readonly ImmutableDictionary<string, string> Checks = new Dictionary<string, string>()
    {
        {"businessChecks","business check"},
        {"annualRevenueChecks","annual revenue check"},
        {"creditRatingChecks","credit rating check"},
        {"inactivityChecks","inactivity check"}
    }.ToImmutableDictionary();
}
