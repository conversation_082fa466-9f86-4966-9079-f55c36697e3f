﻿using BlueTape.MongoDB.Attributes;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;

[BsonIgnoreExtraElements]
[MongoCollection("creditApplicationAuthorizationDetails")]
public class CreditApplicationAuthorizationDetailsDocument : Document
{
    [BsonElement("creditApplicationId")]
    public string CreditApplicationId { get; set; } = string.Empty;

    [BsonElement("accountAuthorizationDetailsSnapshot")]
    public AccountAuthorizationDocument? AccountAuthorizationDetailsSnapshot { get; set; }
}