﻿using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Extensions;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.DataAccess.LMS.HttpClients;

public class LoanServiceHttpClient : ILoanServiceHttpClient
{

    public HttpClient Client { get; private set; }

    public LoanServiceHttpClient(HttpClient client, IConfiguration configuration)
    {
        Client = client;
        if (EnvironmentExtensions.IsDevelopment()) return;
        var apiKey = configuration.GetSection(LoanServiceConstants.LoanServiceApiKey).Value;
        Client.DefaultRequestHeaders.Add(HttpConstants.ApiKeyHeader, apiKey);
    }
}
