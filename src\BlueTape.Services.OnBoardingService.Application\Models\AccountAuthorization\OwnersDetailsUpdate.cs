﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class OwnersDetailsUpdate
{
    public string? Id { get; set; }

    public string Identifier { get; set; } = string.Empty;

    public decimal PercentOwned { get; set; }

    public string? FraudpointScore { get; set; }

    public string? Phone { get; set; }

    public string? Email { get; set; }

    public string? EmailRiskScore { get; set; }

    public DateOnly Birthday { get; set; }

    public string SsnHash { get; set; } = string.Empty;

    public string FirstName { get; set; } = string.Empty;

    public string LastName { get; set; } = string.Empty;

    public AddressUpdate Address { get; set; } = new();

    public bool IsPrincipal { get; set; }

    public string? B2ELinkIndex { get; set; }

    public DateOnly? LastSSNRejectionDate { get; set; }

    public IEnumerable<string?> CRICodes { get; set; } = Enumerable.Empty<string?>();

    public string? CVI { get; set; }

    public string? FICOScore { get; set; }

    public string? InquiriesDuringLast6Months { get; set; }

    public DateOnly? LastPersonalBankruptcyDate { get; set; }

    public string? IPRiskLevel { get; set; }

    public string? DomainRiskLevel { get; set; }

    public DateOnly? LastEINRejectionDate { get; set; }
}