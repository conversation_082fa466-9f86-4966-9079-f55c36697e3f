﻿using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.LS.DTOs.Credit;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.CustomerAccount;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;
using CompanyUpdateModelFromOnboarding = BlueTape.Services.OnBoardingService.Application.Models.Company.UpdateCompanyModel;


namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CreditApplicationServiceTests
{
    private readonly CreditApplicationService _creditApplicationService;
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();
    private readonly Mock<ICreditApplicationAuthorizationDetailsService> _creditApplicationAuthorizationDetailsServiceMock = new();
    private readonly Mock<IAccountAuthorizationsService> _accountAuthorizationsServiceMock = new();
    private readonly Mock<IAccountStatusService> _accountStatusService = new();
    private readonly Mock<ICompanyService> _companyServiceMock = new();
    private readonly Mock<ICreditApplicationNotesService> _creditNotesService = new();
    private readonly Mock<ICreditApplicationSyncService> _creditApplicationSyncServiceMock = new();
    private readonly Mock<ChangeCreditApplicationStatusStrategy> _changeCreditApplicationStatusStrategyMock = new();
    private readonly Mock<ILogger<CreditApplicationService>> _loggerMock = new();
    private readonly Mock<ICompanyExternalService> _companyExternalServiceMock = new();
    private readonly Mock<ILoanExternalService> _loanExternalServiceMock = new();

    public CreditApplicationServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        IMapper mapper = new Mapper(mapperConfig);

        _creditApplicationService = new CreditApplicationService(
            _creditApplicationRepositoryMock.Object,
            _creditApplicationAuthorizationDetailsServiceMock.Object,
            _accountStatusService.Object,
            _dateProviderMock.Object,
            mapper,
            _companyServiceMock.Object,
            _creditNotesService.Object,
            _creditApplicationSyncServiceMock.Object,
           new[] { _changeCreditApplicationStatusStrategyMock.Object },
           _companyExternalServiceMock.Object,
           _loanExternalServiceMock.Object,
            _loggerMock.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetById_CreditApplicationIsExist_ReturnsDraft(string id, CreditApplicationDocument creditApplication)
    {
        _creditApplicationRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(creditApplication);

        var result = await _creditApplicationService.GetById(id, default);

        result.CreditLimit.ShouldBeEquivalentTo(creditApplication.CreditLimit);
    }

    [Theory, CustomAutoData]
    public async Task GetById_ArAdvanceCreditApplication_ReturnsGetPaidInfo(string id, CreditApplicationDocument arAdvanceCreditApplication, CreditApplicationDocument getPaidCreditApplication)
    {
        getPaidCreditApplication.ApprovedBy = "test";
        getPaidCreditApplication.CreatedAt = DateTime.Now;
        arAdvanceCreditApplication.Type = CreditApplicationType.ARAdvance.ToString();
        getPaidCreditApplication.ArAdvanceApplicationId = arAdvanceCreditApplication.Id;

        _creditApplicationRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(arAdvanceCreditApplication);
        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(query =>
            query.ArAdvanceApplicationIds.Contains(arAdvanceCreditApplication.Id)), default)).ReturnsAsync(new[] { getPaidCreditApplication });

        var result = await _creditApplicationService.GetById(id, default);

        result.GetPaidApplicationId.ShouldBe(getPaidCreditApplication.Id);
        result.GetPaidCreatedAt.ShouldBe(getPaidCreditApplication.CreatedAt);
        result.GetPaidApprovalDate.ShouldBe(getPaidCreditApplication.ApprovedAt);
        result.GetPaidApprovedBy.ShouldBe("test");
    }

    [Theory, CustomAutoData]
    public async Task GetById_CreditApplicationIsNull_ReturnsNull(string id)
    {
        _creditApplicationRepositoryMock.Setup(x => x.GetById(id, default));

        var result = await _creditApplicationService.GetById(id, default);

        result.ShouldBeNull();
    }

    [Theory, CustomAutoData]
    public async Task GetAll_Valid_ReturnsCreditApplications(List<CreditApplicationDocument> creditApplications)
    {
        _creditApplicationRepositoryMock.Setup(x => x.GetAll(default)).ReturnsAsync(creditApplications);

        var result = await _creditApplicationService.GetAll(default);

        var applications = result.ToList();
        applications.Count.ShouldBe(creditApplications.Count);
        applications[0].CreditLimit.ShouldBeEquivalentTo(creditApplications[0].CreditLimit);
    }

    [Fact]
    public async Task GetAll_CreditApplicationsIsNotExist_ReturnsEmptyList()
    {
        _creditApplicationRepositoryMock.Setup(x => x.GetAll(default));

        var result = await _creditApplicationService.GetAll(default);

        result.Count().ShouldBe(0);
    }

    [Theory, CustomAutoData]
    public async Task Create_Valid_ReturnsCreditApplications(CreditApplicationDocument creditApplication, CreateCreditApplication createCreditApplication)
    {
        createCreditApplication.IsNotNotifyUser = null;
        _creditApplicationRepositoryMock.Setup(x => x.Add(
                It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);

        var result = await _creditApplicationService.Create(createCreditApplication, default);

        result.CreditLimit.ShouldBeEquivalentTo(creditApplication.CreditLimit);
        _creditApplicationSyncServiceMock.Verify(x =>
            x.SyncApplicableCreditApplication(It.IsAny<CreditApplicationDocument>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Update_Valid_ReturnsCreditApplications(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication)
    {
        var now = DateTime.UtcNow;
        creditApplication.Type = CreditApplicationType.LineOfCredit.ToString();
        creditApplication.Status = CreditApplicationStatus.Processing.ToString();
        updateCreditApplication.NewStatus = CreditApplicationStatus.Processed.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);

        var result = await _creditApplicationService.Update(updateCreditApplication, default);

        result.CreditLimit.ShouldBeEquivalentTo(creditApplication.CreditLimit);
        result.LastStatusChangedAt.ShouldBe(now);
        result.StatusNote.ShouldBe(updateCreditApplication.StatusNote);

        _creditApplicationSyncServiceMock.Verify(x => x.SyncApplicableCreditApplication(creditApplication, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Update_StatusNotChanged_DoesNotUpdateStatusFields(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication)
    {
        var now = DateTime.UtcNow;
        var lastStatusChangedAt = creditApplication.LastStatusChangedAt;
        var lastStatusChangedBy = creditApplication.LastStatusChangedBy;
        var statusNote = creditApplication.StatusNote;

        creditApplication.Type = CreditApplicationType.LineOfCredit.ToString();
        creditApplication.Status = CreditApplicationStatus.Processing.ToString();
        updateCreditApplication.NewStatus = CreditApplicationStatus.Processing.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);

        var result = await _creditApplicationService.Update(updateCreditApplication, default);

        result.LastStatusChangedAt.ShouldBe(lastStatusChangedAt);
        result.LastStatusChangedBy.ShouldBe(lastStatusChangedBy);
        result.StatusNote.ShouldBe(statusNote);
    }

    [Theory, CustomAutoData]
    public async Task Update_SnapshotDoesNotExist_CreatesNewSnapshot(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication, List<AccountAuthorization> accountAuthorizations)
    {
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);
        _accountAuthorizationsServiceMock.Setup(x => x.GetAllByFilters(null, creditApplication.CompanyId,
            creditApplication.EinHash, null, default)).ReturnsAsync(accountAuthorizations);
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.GetByCreditApplicationId(It.IsAny<string>(), default)).ReturnsAsync((CreditApplicationAuthorizationDetails?)null);
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.Create(It.Is<CreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default));
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.UpdateByCreditApplicationId(It.Is<UpdateCreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default));

        await _creditApplicationService.Update(updateCreditApplication, default);

        _creditApplicationAuthorizationDetailsServiceMock.Verify(x => x.CreateOrUpdateAccountAuthDetailsSnapshot(It.Is<UpsertAccountAuthDetailsSnapshotModel>(x => x.CreditApplicationId == creditApplication.Id
            && x.EinHash == creditApplication.EinHash && x.CompanyId == creditApplication.CompanyId), default), Times.Once);
        _creditApplicationAuthorizationDetailsServiceMock.Verify(x => x.UpdateByCreditApplicationId(It.Is<UpdateCreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default), Times.Never);
    }

    [Theory, CustomAutoData]
    public async Task Update_SnapshotExists_UpdatesNewSnapshot(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication,
        List<AccountAuthorization> accountAuthorizations, CreditApplicationAuthorizationDetails creditApplicationAuthorizationDetails)
    {
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);
        _accountAuthorizationsServiceMock.Setup(x => x.GetAllByFilters(null, creditApplication.CompanyId,
            creditApplication.EinHash, null, default)).ReturnsAsync(accountAuthorizations);
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.GetByCreditApplicationId(It.IsAny<string>(), default)).ReturnsAsync(creditApplicationAuthorizationDetails);
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.Create(It.Is<CreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default));
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.UpdateByCreditApplicationId(It.Is<UpdateCreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default));

        await _creditApplicationService.Update(updateCreditApplication, default);

        _creditApplicationAuthorizationDetailsServiceMock.Verify(x => x.Create(It.Is<CreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default), Times.Never);
        _creditApplicationAuthorizationDetailsServiceMock.Verify(x => x.CreateOrUpdateAccountAuthDetailsSnapshot(It.Is<UpsertAccountAuthDetailsSnapshotModel>(x => x.CreditApplicationId == creditApplication.Id
            && x.EinHash == creditApplication.EinHash && x.CompanyId == creditApplication.CompanyId), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Update_AutomatedDecisionResultNull_DoesNotUpdateOrCreateSnapshot(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication,
        List<AccountAuthorization> accountAuthorizations, CreditApplicationAuthorizationDetails creditApplicationAuthorizationDetails)
    {
        var now = DateTime.UtcNow;
        creditApplication.AutomatedDecisionResult = null;
        updateCreditApplication.AutomatedDecisionResult = null;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);
        _accountAuthorizationsServiceMock.Setup(x => x.GetAllByFilters(null, creditApplication.CompanyId,
            creditApplication.EinHash, null, default)).ReturnsAsync(accountAuthorizations);
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.GetByCreditApplicationId(It.IsAny<string>(), default)).ReturnsAsync(creditApplicationAuthorizationDetails);
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.Create(It.Is<CreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default));
        _creditApplicationAuthorizationDetailsServiceMock.Setup(x => x.UpdateByCreditApplicationId(It.Is<UpdateCreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default));

        await _creditApplicationService.Update(updateCreditApplication, default);

        _creditApplicationAuthorizationDetailsServiceMock.Verify(x => x.Create(It.Is<CreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default), Times.Never);
        _creditApplicationAuthorizationDetailsServiceMock.Verify(x => x.UpdateByCreditApplicationId(It.Is<UpdateCreditApplicationAuthorizationDetails>(x => x.CreditApplicationId == creditApplication.Id
            && x.AccountAuthorizationDetailsSnapshot!.Id == accountAuthorizations.FirstOrDefault()!.Id), default), Times.Never);
    }

    [Theory, CustomAutoData]
    public async Task Update_StatusProcessed_ReturnsWithValidInfo(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication)
    {
        var status = CreditApplicationStatus.Processed.ToString().ToLower();
        updateCreditApplication.NewStatus = status;
        creditApplication.Type = CreditApplicationType.LineOfCredit.ToString();
        creditApplication.Status = CreditApplicationStatus.Processing.ToString().ToLower();
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.Is<CreditApplicationDocument>(
                y => y.LastStatusChangedAt == now &&
                   y.LastStatusChangedBy == updateCreditApplication.UpdatedBy &&
                   y.ApprovedCreditLimit == updateCreditApplication.ApprovedCreditLimit &&
                   y.Status == status
                ), default))
            .ReturnsAsync(creditApplication);

        var result = await _creditApplicationService.Update(updateCreditApplication, default);

        result.ShouldNotBeNull();
        _creditApplicationSyncServiceMock.Verify(x => x.SyncApplicableCreditApplication(creditApplication, default), Times.Once);
    }

    [Theory]
    [InlineData(CreditApplicationStatus.Approved)]
    [InlineData(CreditApplicationStatus.SentBack)]
    [InlineData(CreditApplicationStatus.Rejected)]
    [InlineData(CreditApplicationStatus.Canceled)]
    public Task Update_AdminStatus_ThrowsValidationException(CreditApplicationStatus status)
    {
        var updateCreditApplication = new UpdateCreditApplication()
        {
            NewStatus = status.ToString()
        };
        var act = async () => await _creditApplicationService.Update(updateCreditApplication, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task Update_AlreadyHasApprovedApplicationsAndNewStatusApproved_ThrowsValidationException(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication,
        List<CreditApplicationDocument> approvedApplications)
    {
        updateCreditApplication.NewStatus = "approved";
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);
        _creditApplicationRepositoryMock
            .Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default)).ReturnsAsync(approvedApplications);

        var act = () => _creditApplicationService.Update(updateCreditApplication, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task Update_EmptyEin_ThrowsValidationException(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication)
    {
        updateCreditApplication.NewStatus = "approved";
        var now = DateTime.UtcNow;
        creditApplication.EinHash = string.Empty;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);

        var act = () => _creditApplicationService.Update(updateCreditApplication, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task Update_AlreadyHasApprovedApplicationsAndNewStatusNotApproved_DoesNotThrowException(CreditApplicationDocument creditApplication, UpdateCreditApplication updateCreditApplication,
        List<CreditApplicationDocument> approvedApplications)
    {
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(creditApplication);
        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFilters(new GetCreditApplicationQuery()
        {
            EinHash = creditApplication.EinHash,
            Status = new[] { updateCreditApplication.NewStatus! },
            CompanyId = null,
            Id = null
        }, default)).ReturnsAsync(approvedApplications);

        var act = () => _creditApplicationService.Update(updateCreditApplication, default);

        return act.ShouldNotThrowAsync();
    }

    [Fact]
    public async Task Review_ShouldChangeStatus_WhenAllDataExist()
    {
        var userId = "userId";
        var creditApplicationDocument = new CreditApplicationDocument()
        {
            Status = "Processed",
            CompanyId = "companyId",
            Id = "creditAppId",
            Type = "LineOfCredit",
        };
        var dto = new ReviewCreditApplicationDto()
        {
            IsSecured = true,
            DepositAmount = 200,
            NewStatus = "Approved",
        };
        var creditApplication = new CreditApplication();
        var updateCompanyModel = new UpdateCompanyModel()
        {
            Settings = new CompanySettingsModel()
            {
                DepositDetails = new DepositDetailsModel()
                {
                    DepositAmount = dto.DepositAmount,
                    IsSecured = dto.IsSecured.Value,
                }
            }
        };

        _changeCreditApplicationStatusStrategyMock
            .Setup(x => x.ChangeStatus(creditApplicationDocument, dto, userId, default))
            .ReturnsAsync(creditApplication);
        _companyExternalServiceMock.Setup(x => x.UpdateCompany(creditApplicationDocument.CompanyId, userId, updateCompanyModel, default))
            .Returns(Task.CompletedTask);
        _creditApplicationRepositoryMock
            .Setup(x => x.GetById(creditApplicationDocument.Id, default))
            .ReturnsAsync(creditApplicationDocument);
        _changeCreditApplicationStatusStrategyMock.Setup(x => x.IsApplicable(dto.NewStatus))
            .Returns(true);

        var result = await _creditApplicationService.Review(creditApplicationDocument.Id, userId, dto, default);

        result.ShouldNotBeNull();
        _changeCreditApplicationStatusStrategyMock.Verify(x => x.ChangeStatus(creditApplicationDocument, dto, userId, default), Times.Once);
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(creditApplicationDocument.CompanyId, userId, It.IsAny<UpdateCompanyModel>(), default), Times.Once);
        _creditApplicationRepositoryMock.Verify(x => x.GetById(creditApplicationDocument.Id, default), Times.Once);
        _changeCreditApplicationStatusStrategyMock.Verify(x => x.IsApplicable(dto.NewStatus), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Review_ShouldThrowValidationError_OnMissingNewStatus(CreditApplicationDocument creditApplication)
    {
        var dto = new ReviewCreditApplicationDto();
        var act = () => _creditApplicationService.Review(creditApplication.Id, "userId", dto, default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("new credit application status");
    }

    [Fact]
    public async Task Review_ShouldThrowValidationError_WhenItWasNotFound()
    {
        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString() };
        var act = () => _creditApplicationService.Review("cr", "userId", dto, default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("was not found");
    }

    [Fact]
    public async Task Review_ShouldThrowValidationError_WhenCompanyIdIsNull()
    {
        var ca = new CreditApplicationDocument { Id = "cr" };
        _creditApplicationRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(ca);

        var dto = new ReviewCreditApplicationDto { NewStatus = CreditApplicationStatus.Approved.ToString() };
        var act = () => _creditApplicationService.Review("cr", "userId", dto, default);
        ValidationException ex = await act.ShouldThrowAsync<ValidationException>();
        ex.Message.ShouldContain("doesn't belong to a company");
    }

    [Fact]
    public async Task GetAllByFilters_ReturnsCreditApplications()
    {
        var query = new GetCreditApplicationQuery();
        var creditApplications = Enumerable.Range(0, 2)
            .Select(_ => new CreditApplicationDocument { Id = Guid.NewGuid().ToString() })
            .ToList();

        _creditApplicationRepositoryMock
            .Setup(x => x.GetAllByFilters(query, default))
            .ReturnsAsync(creditApplications);

        var result = (await _creditApplicationService.GetAllByFilters(query, default)).ToList();

        _creditApplicationRepositoryMock.Verify(s => s.GetAllByFilters(query, default), Times.Once);

        result.ShouldBeUnique();
        result.ShouldAllBe(res => creditApplications.Any(app => app.Id == res.Id));
    }

    [Fact]
    public async Task GetAllByFilters_NoData_ReturnsEmptyList()
    {
        var query = new GetCreditApplicationQuery();

        _creditApplicationRepositoryMock
            .Setup(x => x.GetAllByFilters(query, default))
            .ReturnsAsync([]);

        var result = await _creditApplicationService.GetAllByFilters(query, default);

        result.Count().ShouldBe(0);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFiltersWithPagination_CompanyIdAndStatusAreNull_ReturnsCreditApplications(GetCreditApplicationQueryWithPagination query,
        GetQueryWithPaginationResult<CreditApplicationDocument> result)
    {
        var credits = new List<CreditDto>();

        foreach (var targetResult in result.Result)
        {
            var targetCredit = new CreditDto()
            {
                Id = new Guid(),
                CreditApplicationId = targetResult.Id,
            };
            credits.Add(targetCredit);
        }

        var creditFilterDto = new CreditFilterDto()
        {
            CreditApplicationId = result.Result.Select(x => x.Id).ToArray(),
            Detailed = true,
        };

        _loanExternalServiceMock.Setup(x => x.GetCreditsByFilters(It.IsAny<CreditFilterDto>(), default))
            .ReturnsAsync(credits);

        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFiltersWithPagination(It.IsAny<GetCreditApplicationQueryWithPagination>(), default))
            .ReturnsAsync(result);

        var value = await _creditApplicationService.GetAllByFiltersWithPagination(query, default);

        value.Result.Count().ShouldBe(result.Result.Count());
    }

    [Theory, CustomAutoData]
    public async Task PatchAdmin_ValidRequest_ShouldPatch(PatchCreditApplicationAdminModel patchModel, CreditApplicationDocument document)
    {
        document.Type = CreditApplicationType.LineOfCredit.ToString();
        patchModel.ApprovedCreditLimit = document.ApprovedCreditLimit;
        _creditApplicationRepositoryMock.Setup(x => x.GetById(patchModel.Id!, default)).ReturnsAsync(document);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.Is<CreditApplicationDocument>(doc =>
                doc.RevenueFallPercentage == patchModel.RevenueFallPercentage
                && doc.ApprovedCreditLimit == patchModel.ApprovedCreditLimit
                && doc.UpdatedBy == patchModel.UpdatedBy
                && string.Equals(doc.PurchaseTypeOption, patchModel.PurchaseTypeOption, StringComparison.InvariantCultureIgnoreCase)), default))
            .ReturnsAsync(document);
        _companyServiceMock.Setup(x => x.PatchCompanyCredit(document.CompanyId!, It.Is<CompanyUpdateModelFromOnboarding>(x =>
            x.PurchaseType == patchModel.PurchaseTypeOption!.ToLower()
                && x.Limit == Convert.ToInt32(patchModel.ApprovedCreditLimit)), default));

        var result = await _creditApplicationService.PatchAdmin(patchModel, default);

        result.ShouldNotBeNull();
        _creditApplicationRepositoryMock.Verify(x => x.GetById(patchModel.Id!, default), Times.Once());
        _creditApplicationRepositoryMock.Verify(x => x.Update(It.IsAny<CreditApplicationDocument>(), default), Times.Once);
        _companyServiceMock.Verify(x => x.PatchCompanyCredit(document.CompanyId!, It.IsAny<CompanyUpdateModelFromOnboarding>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PatchAdmin_CreditLimitChanged_GeneratesNote(PatchCreditApplicationAdminModel patchModel, CreditApplicationDocument document)
    {
        document.Type = CreditApplicationType.InHouseCredit.ToString();
        patchModel.ApprovedCreditLimit = 500;
        document.ApprovedCreditLimit = 100;
        document.CustomerAccount = new CustomerAccountDocument { Id = "customerId" };

        _creditApplicationRepositoryMock.Setup(x => x.GetById(patchModel.Id!, default)).ReturnsAsync(document);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(document);

        var result = await _creditApplicationService.PatchAdmin(patchModel, default);

        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.SetCustomerIhcSettings(document.CustomerAccount.Id,
            It.Is<UpdateCustomerModel>(m => m.Limit == (double)patchModel.ApprovedCreditLimit!.Value), default),
            Times.Once);
        _creditNotesService.Verify(x => x.Add(It.Is<CreateCreditApplicationNote>(note =>
            note.CreditApplicationId == document.Id &&
            note.CreatedBy == patchModel.UpdatedBy &&
            note.Note.Contains("100") &&
            note.Note.Contains("500")), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PatchAdmin_ARAdvanceType_UpdatesCompanySettings(PatchCreditApplicationAdminModel patchModel, CreditApplicationDocument document)
    {
        document.Type = CreditApplicationType.ARAdvance.ToString();
        patchModel.ApprovedCreditLimit = 500;
        patchModel.DebtInvestor = DebtInvestorType.Arcadia;
        document.ApprovedCreditLimit = 100;

        _creditApplicationRepositoryMock.Setup(x => x.GetById(patchModel.Id!, default)).ReturnsAsync(document);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(document);

        var result = await _creditApplicationService.PatchAdmin(patchModel, default);

        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(document.CompanyId!, patchModel.UpdatedBy,
            It.Is<BlueTape.CompanyService.Companies.UpdateCompanyModel>(m =>
                m.Settings!.ARAdvance!.MerchantLimit == Convert.ToInt32(patchModel.ApprovedCreditLimit!.Value)),
            default),
            Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetLightCreditApplications(string[] companyIds, List<LightCreditApplicationDocument> documents)
    {
        _creditApplicationRepositoryMock.Setup(x => x.GetLightCreditApplications(companyIds, CreditApplicationStatus.Approved.ToString(), default)).ReturnsAsync(documents);

        var result = await _creditApplicationService.GetLightCreditApps(companyIds, CreditApplicationStatus.Approved.ToString(), default);
        _creditApplicationRepositoryMock.Verify(x => x.GetLightCreditApplications(companyIds, CreditApplicationStatus.Approved.ToString(), default), Times.Once());
        result.ShouldNotBeNull();
    }

    [Theory, CustomAutoData]
    public async Task PatchAdmin_GetPaidType_UpdatesCompanyDebtInvestorSettings(PatchCreditApplicationAdminModel patchModel, CreditApplicationDocument document)
    {
        // Arrange
        document.Type = CreditApplicationType.GetPaid.ToString();
        document.CompanyId = "testCompanyId";
        patchModel.DebtInvestor = (int)DebtInvestorType.Arcadia;
        patchModel.ApprovedCreditLimit = null;

        _creditApplicationRepositoryMock.Setup(x => x.GetById(patchModel.Id!, default)).ReturnsAsync(document);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(document);

        // Act
        var result = await _creditApplicationService.PatchAdmin(patchModel, default);

        // Assert
        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(
            document.CompanyId,
            patchModel.UpdatedBy,
            It.Is<UpdateCompanyModel>(m =>
                m.Settings!.DefaultDebtInvestorTradeCredit == CompanyService.Common.Enums.DebtInvestorType.Arcadia),
            default),
            Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task PatchAdmin_ARAdvanceTypeWithDebtInvestor_UpdatesCompanyDebtInvestorSettings(PatchCreditApplicationAdminModel patchModel, CreditApplicationDocument document)
    {
        // Arrange
        document.Type = CreditApplicationType.ARAdvance.ToString();
        document.CompanyId = "testCompanyId";
        patchModel.DebtInvestor = (int)DebtInvestorType.Arcadia;
        patchModel.ApprovedCreditLimit = null;

        _creditApplicationRepositoryMock.Setup(x => x.GetById(patchModel.Id!, default)).ReturnsAsync(document);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.IsAny<CreditApplicationDocument>(), default))
            .ReturnsAsync(document);

        // Act
        var result = await _creditApplicationService.PatchAdmin(patchModel, default);

        // Assert
        result.ShouldNotBeNull();
        _companyExternalServiceMock.Verify(x => x.UpdateCompany(
            document.CompanyId,
            patchModel.UpdatedBy,
            It.Is<UpdateCompanyModel>(m =>
                m.Settings!.ARAdvance!.DefaultDebtInvestor == CompanyService.Common.Enums.DebtInvestorType.Arcadia),
            default),
            Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyIds_ValidQuery_ReturnsCreditApplications(
        GetCreditApplicationsByCompanyIdsQuery query,
        List<CreditApplicationDocument> creditApplications)
    {
        // Arrange
        _creditApplicationRepositoryMock
            .Setup(x => x.GetByCompanyIds(query, default))
            .ReturnsAsync(creditApplications);

        // Act
        var result = await _creditApplicationService.GetByCompanyIds(query, default);

        // Assert
        var applications = result.ToList();
        applications.Count.ShouldBe(creditApplications.Count);
        applications[0].CreditLimit.ShouldBeEquivalentTo(creditApplications[0].CreditLimit);
        _creditApplicationRepositoryMock.Verify(x => x.GetByCompanyIds(query, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyIds_EmptyCompanyIds_ReturnsEmptyList(GetCreditApplicationsByCompanyIdsQuery query)
    {
        // Arrange
        query.CompanyIds = Array.Empty<string>();
        _creditApplicationRepositoryMock
            .Setup(x => x.GetByCompanyIds(query, default))
            .ReturnsAsync(Array.Empty<CreditApplicationDocument>());

        // Act
        var result = await _creditApplicationService.GetByCompanyIds(query, default);

        // Assert
        result.ShouldBeEmpty();
        _creditApplicationRepositoryMock.Verify(x => x.GetByCompanyIds(query, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyIds_WithFilters_ReturnsCreditApplications(
        GetCreditApplicationsByCompanyIdsQuery query,
        List<CreditApplicationDocument> creditApplications)
    {
        // Arrange
        query.Status = new[] { "Approved" };
        query.Type = new[] { "LineOfCredit" };
        query.Category = new[] { "Contractor" };

        _creditApplicationRepositoryMock
            .Setup(x => x.GetByCompanyIds(query, default))
            .ReturnsAsync(creditApplications);

        // Act
        var result = await _creditApplicationService.GetByCompanyIds(query, default);

        // Assert
        var applications = result.ToList();
        applications.Count.ShouldBe(creditApplications.Count);
        _creditApplicationRepositoryMock.Verify(x => x.GetByCompanyIds(
            It.Is<GetCreditApplicationsByCompanyIdsQuery>(q =>
                q.Status!.Contains("Approved") &&
                q.Type!.Contains("LineOfCredit") &&
                q.Category!.Contains("Contractor")),
            default),
            Times.Once);
    }
}
