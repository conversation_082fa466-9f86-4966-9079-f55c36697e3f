﻿using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorizationDetailsChanges;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Utilities.Providers;
using Serilog;
using System.Collections;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class AccountAuthorizationDetailsChangesService(
    IAccountAuthorizationsChangesRepository accountAuthorizationsChangesRepository,
    IDateProvider dateProvider)
    : IAccountAuthorizationDetailsChangesService
{
    private readonly List<AccountAuthorizationDetailsChangesDocument> _updatedFields = [];

    public async Task CompareAccountAuthorizationsDetailsAndLogChangesAsync(
        AccountAuthorizationDocument initialAccountAuthorizationDocument,
        AccountAuthorizationDocument updatedAccountAuthorizationDocument,
        string creditApplicationId,
        ExecutionChangeType executionChangeType,
        CancellationToken cancellationToken)
    {
        Log.Information("Started calculation of account auth details changes {Id}",
            initialAccountAuthorizationDocument.Id);

        var comparisonModel = new ComparisonModel
        {
            CompanyId = updatedAccountAuthorizationDocument.CompanyId,
            EinHash = updatedAccountAuthorizationDocument.EinHash,
            CreatedBy = updatedAccountAuthorizationDocument.UpdatedBy,
            ExecutionId = updatedAccountAuthorizationDocument.UpdatedBy.Split(":")[0],
            AccountAuthorizationDetailsId = updatedAccountAuthorizationDocument.Id,
            ChangeType = executionChangeType.ToString(),
            CreditApplicationId = creditApplicationId
        };

        CompareAndUpdateProperties(
            initialAccountAuthorizationDocument.BusinessDetails,
            updatedAccountAuthorizationDocument.BusinessDetails,
            comparisonModel);

        CompareAndUpdateCollection(
            initialAccountAuthorizationDocument.OwnersDetails,
            updatedAccountAuthorizationDocument.OwnersDetails,
            comparisonModel);

        CompareAndUpdateCollection(
            initialAccountAuthorizationDocument.OwnersEntitiesDetails,
            updatedAccountAuthorizationDocument.OwnersEntitiesDetails,
            comparisonModel);

        CompareAndUpdateCollection(
            initialAccountAuthorizationDocument.BankAccountDetails,
            updatedAccountAuthorizationDocument.BankAccountDetails,
            comparisonModel);

        Log.Information("Finished calculation of account auth details changes {Id}",
            initialAccountAuthorizationDocument.Id);

        var accountAuthDetailsChanges =
            _updatedFields.Where(x => !string.IsNullOrEmpty(x.Key)).ToList();

        if (accountAuthDetailsChanges.Any())
        {
            Log.Information("Account authorization details has been changed {Id}.Saving {Count} changes",
                initialAccountAuthorizationDocument.Id, accountAuthDetailsChanges.Count);

            await accountAuthorizationsChangesRepository.AddRange(accountAuthDetailsChanges, cancellationToken);
        }
    }

    private void CompareAndUpdateProperties<T>(
        T? initialDetails,
        T? updatedDetails,
        ComparisonModel comparisonModel,
        string? identifier = null)
    {
        var properties = typeof(T).GetProperties();
        var detailsType = updatedDetails?.GetType();
        if (initialDetails is null)
        {
            foreach (var property in properties)
            {
                var updatedValue = property.GetValue(updatedDetails);
                if (updatedValue is null) continue;

                var propertyName = property.Name;
                var integration = GetIntegration(detailsType, propertyName);
                var key = GetKey(detailsType, propertyName, identifier);
                AddNewAccountAuthorizationsDetailsChangesLogToList(key, comparisonModel, integration, null, updatedValue);
            }
        }
        else
        {
            foreach (var property in properties)
            {
                var initialValue = property.GetValue(initialDetails);
                var updatedValue = property.GetValue(updatedDetails);

                if (!AreValuesEqual(initialValue, updatedValue))
                {
                    var propertyName = property.Name;
                    var integration = GetIntegration(detailsType, propertyName);
                    var key = GetKey(detailsType, propertyName, identifier);
                    AddNewAccountAuthorizationsDetailsChangesLogToList(key, comparisonModel, integration, initialValue, updatedValue);
                }
            }
        }
    }

    private void CompareAndUpdateCollection<T>(
        IEnumerable<T> initialCollection,
        IEnumerable<T> updatedCollection,
        ComparisonModel comparisonModel)
    {
        var collection = initialCollection.ToList();
        var collectionType = initialCollection.GetType();
        var elementType = collectionType.GetGenericArguments().FirstOrDefault() ?? collectionType.GetElementType();
        var identifierFieldName = GetIdentifierFieldName(elementType);

        foreach (var updatedDetail in updatedCollection)
        {
            var updatedIdentifier = updatedDetail?.GetType().GetProperty(identifierFieldName)?.GetValue(updatedDetail)?.ToString();
            var matchingDetail = collection.Find(x =>
                {
                    var initialIdentifier = x?.GetType().GetProperty(identifierFieldName)?.GetValue(x)?.ToString();
                    return initialIdentifier == updatedIdentifier;
                }
            );

            CompareAndUpdateProperties(matchingDetail ?? default, updatedDetail, comparisonModel, updatedIdentifier);
        }
    }

    private void AddNewAccountAuthorizationsDetailsChangesLogToList(string key, ComparisonModel comparisonModel, string integration, object? oldValue, object? newValue)
    {
        _updatedFields.Add(new AccountAuthorizationDetailsChangesDocument
        {
            CreatedAt = dateProvider.CurrentDateTime,
            AccountAuthorizationDetailsId = comparisonModel.AccountAuthorizationDetailsId,
            ChangeType = comparisonModel.ChangeType,
            CompanyId = comparisonModel.CompanyId,
            CreatedBy = comparisonModel.CreatedBy,
            EinHash = comparisonModel.EinHash,
            OldValue = oldValue,
            NewValue = newValue,
            UpdatedAt = dateProvider.CurrentDateTime,
            CreditApplicationId = comparisonModel.CreditApplicationId,
            ExecutionId = comparisonModel.ExecutionId,
            Integration = integration,
            Key = key
        });
    }

    private static bool AreValuesEqual(object? oldValue, object? newValue)
    {
        if (oldValue == null && newValue == null) return true;
        if (oldValue == null || newValue == null) return false;
        if (ReferenceEquals(oldValue, newValue)) return true;

        var newValueType = newValue.GetType();

        if (newValueType is { IsClass: false }) return newValue.Equals(oldValue);

        if (oldValue is IEnumerable oldEnumerable && newValue is IEnumerable newEnumerable)
            return AreEnumerableValuesEqual(oldEnumerable, newEnumerable);

        return CheckSamenessInObjectProperties(oldValue, newValue);
    }

    private static bool AreEnumerableValuesEqual(IEnumerable oldEnumerable, IEnumerable newEnumerable)
    {
        var oldEnumerator = oldEnumerable.GetEnumerator();
        var newEnumerator = newEnumerable.GetEnumerator();

        while (oldEnumerator.MoveNext())
        {
            if (!newEnumerator.MoveNext())
                return false;

            var oldElement = oldEnumerator.Current;
            var newElement = newEnumerator.Current;

            if (oldElement == null)
            {
                if (newElement != null)
                    return false;
            }
            else if (!oldElement.Equals(newElement))
            {
                return false;
            }
        }

        return !newEnumerator.MoveNext();
    }

    private static bool CheckSamenessInObjectProperties(object? oldValue, object? newValue)
    {
        if (oldValue == null || newValue == null) return false;

        var oldType = oldValue.GetType();
        var newType = newValue.GetType();

        // Ensure both objects are of the same type
        if (oldType != newType)
            return false;

        var propertiesList = oldType.GetProperties().ToList();
        foreach (var property in propertiesList)
        {
            var objValue = property.GetValue(oldValue);
            var anotherValue = property.GetValue(newValue);
            if (objValue != null && !AreValuesEqual(objValue, anotherValue)) return false;
        }

        return true;
    }

    private static string GetIntegration(Type? type, string propertyName)
    {
        if (type == null || string.IsNullOrEmpty(propertyName)) return string.Empty;

        if (type == typeof(BusinessDetailsDocument)) return AccountAuthorizationDetailsConstants
            .BusinessDetailsIntegrations
            .FirstOrDefault(x => x.Key.Contains(propertyName)).Value ?? string.Empty;

        if (type == typeof(OwnersDetailsDocument)) return AccountAuthorizationDetailsConstants
            .OwnersDetailsIntegrations
            .FirstOrDefault(x => x.Key.Contains(propertyName)).Value ?? string.Empty;

        if (type == typeof(OwnersEntitiesDetailsDocument)) return AccountAuthorizationDetailsConstants
            .BusinessDetailsIntegrations
            .FirstOrDefault(x => x.Key.Contains(propertyName)).Value ?? string.Empty;

        if (type == typeof(BankAccountDetailsDocument)) return AccountAuthorizationDetailsConstants
            .BankAccountsDetailsIntegrations
            .FirstOrDefault(x => x.Key.Contains(propertyName)).Value ?? string.Empty;

        return string.Empty;
    }

    private static string GetKey(Type? type, string propertyName, string? identifier)
    {
        if (type == null || string.IsNullOrEmpty(propertyName)) return string.Empty;

        if (type == typeof(BusinessDetailsDocument)) return $"businessDetails.{propertyName}";
        if (type == typeof(OwnersDetailsDocument)) return $"ownersDetails[{identifier}].{propertyName}";
        if (type == typeof(OwnersEntitiesDetailsDocument)) return $"ownersEntitiesDetails[{identifier}].{propertyName}";
        return type == typeof(BankAccountDetailsDocument) ? $"bankAccountDetails[{identifier}].{propertyName}" : string.Empty;
    }

    private static string GetIdentifierFieldName(Type? type)
    {
        if (type == null) return string.Empty;

        if (type == typeof(OwnersDetailsDocument)) return AccountAuthorizationDetailsConstants.IdentifierFieldName;
        if (type == typeof(OwnersEntitiesDetailsDocument)) return AccountAuthorizationDetailsConstants.IdentifierFieldName;
        return type == typeof(BankAccountDetailsDocument) ? AccountAuthorizationDetailsConstants.BankAccountIdFieldName : string.Empty;
    }
}