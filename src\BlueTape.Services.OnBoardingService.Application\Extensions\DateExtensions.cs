using System.Globalization;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;

public static class DateExtensions
{
    private static readonly TimeSpan CstOffset = TimeSpan.FromHours(-6);
    
    public static DateOnly ToCstDate(this DateTime date)
    {
        if (date != DateTime.MinValue)
        {
            DateTime dateTime = date.ToUniversalTime() + CstOffset;
            return DateOnly.FromDateTime(dateTime);    
        }
        
        return date.ToDateOnly();
    }
    
    private static DateOnly ToDateOnly(this DateTime date)
    {
        return new DateOnly(date.Year, date.Month, date.Day);
    }
}