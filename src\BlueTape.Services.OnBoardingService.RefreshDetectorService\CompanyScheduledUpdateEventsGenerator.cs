﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService;

public class CompanyScheduledUpdateEventsGenerator : ICompanyScheduledUpdateEventsGenerator
{
    private readonly IDateProvider _dateProvider;
    private readonly ITraceIdAccessor _traceIdAccessor;
    private readonly ILogger<CompanyScheduledUpdateEventsGenerator> _logger;

    public CompanyScheduledUpdateEventsGenerator(IDateProvider dateProvider,
        ITraceIdAccessor traceIdAccessor,
        ILogger<CompanyScheduledUpdateEventsGenerator> logger)
    {
        _dateProvider = dateProvider;
        _traceIdAccessor = traceIdAccessor;
        _logger = logger;
    }

    public IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> GenerateScheduledUpdateEvents(
        CompanyModel company,
        RefreshCheckConfiguration scheduledCheck,
        IReadOnlyList<LightCreditApplicationDocument> approvedCreditApplications,
        IReadOnlyList<DecisionEngineSteps> lastCompanyExecutions)
    {
        var events = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>();

        var creditApplicationsTypesToCheck = scheduledCheck.CreditApplicationTypes.ToList();
        var companyCreditApplications = approvedCreditApplications
            .Where(creditApp => ShouldCreditApplicationBeRerun(company, creditApp, creditApplicationsTypesToCheck));
        var companyCreditApplicationIds = companyCreditApplications
            .Select(x => x.Id)
            .Distinct()
            .ToArray();

        if (companyCreditApplicationIds.Length == 0)
        {
            _logger.LogInformation("No credit applications applicable for scheduled update {scheduledUpdateType} check", scheduledCheck.ScheduledUpdate);
            return events;
        }

        if (IsLatestExecutionFresh(scheduledCheck, lastCompanyExecutions, scheduledCheck.FrequencyInDays))
        {
            _logger.LogInformation("Last execution has not expired yet for scheduled update {scheduledUpdateType} check", scheduledCheck.ScheduledUpdate);
            return events;
        }

        var correlationId = _traceIdAccessor.TraceId;

        _logger.LogInformation("Generating scheduled update events");
        foreach (var creditAppId in companyCreditApplicationIds)
        {
            var scheduledUpdateEvent = new ScheduledUpdateEvent()
            {
                CreatedBy = AuthorizationDetailsRefreshDetectorConstants.CreatedBy,
                BlueTapeCorrelationId = correlationId,
                EventType = $"{AuthorizationDetailsRefreshDetectorConstants.ScheduledUpdateEventType}.{scheduledCheck.ScheduledUpdate}",
                UserId = string.Empty,
                Details = new ScheduledUpdateDetails()
                {
                    ApprovedCreditApplicationId = creditAppId,
                    ScheduleMode = ScheduleMode.CreateNew
                }
            };

            events.Add(new ServiceBusMessageBt<ScheduledUpdateEvent>(scheduledUpdateEvent, new ServiceBusMessageAttributes()
            {
                CorrelationId = correlationId
            }));
        }

        return events;
    }

    private static bool ShouldCreditApplicationBeRerun(CompanyModel company, LightCreditApplicationDocument creditApplication,
        List<CreditApplicationType> creditApplicationsTypesToCheck)
    {
        if (creditApplication.CompanyId != company.Id) return false;
        var creditAppType = creditApplication.Type.ParseToEnum<CreditApplicationType>();
        return creditAppType != null && creditApplicationsTypesToCheck.Contains(creditAppType.Value);
    }

    private bool IsLatestExecutionFresh(
        RefreshCheckConfiguration scheduledCheck,
        IReadOnlyList<DecisionEngineSteps> lastCompanyExecutions,
        int refreshingFrequencyInDays)
    {
        var includedSteps = lastCompanyExecutions
            .Where(x => scheduledCheck.StepsIncluded.Contains(x.Step));
        var currentDate = _dateProvider.CurrentDateTime;
        var latestStepDate = includedSteps.MaxBy(x => x.UpdatedAt)?.UpdatedAt;
        var daysSinceLatestExecution = latestStepDate?.CalendarDaysBetween(currentDate);

        return daysSinceLatestExecution <= refreshingFrequencyInDays;
    }
}
