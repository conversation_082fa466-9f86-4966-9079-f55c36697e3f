﻿using BlueTape.Services.OnBoardingService.Domain.Documents.Company;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

public interface ICompanyRepository : IGenericRepository<CompanyDocument>
{
    Task UpdateManyByIds(IEnumerable<string> companyIds, UpdateCompanyDocument updateCompany, CancellationToken cancellationToken);
    Task<IEnumerable<CompanyDocument>> GetSuppliers(CancellationToken cancellationToken);
}
