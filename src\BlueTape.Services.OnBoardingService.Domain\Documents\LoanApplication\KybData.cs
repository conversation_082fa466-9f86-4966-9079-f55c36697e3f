﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class KybData
{
    [BsonElement("KYB")]
    public List<KYB>? KYB { get; set; }
    [BsonElement("KYC")]
    public List<KYC>? KYC { get; set; }
    [BsonElement("BVI")]
    public double? BVI { get; set; }
    [BsonElement("BRI")]
    public List<string>? BRI { get; set; }
    [BsonElement("CRI")]
    public List<string>? CRI { get; set; }
    [BsonElement("CVI")]
    public double? CVI { get; set; }
    [BsonElement("B2E")]
    public double? B2E { get; set; }
}

[BsonIgnoreExtraElements]
public class KYB
{
    [BsonElement("owner")]
    public Owner? Owner { get; set; }
    [BsonElement("BVI")]
    public string? BVI { get; set; }
    [BsonElement("BRI")]
    public List<string>? BRI { get; set; }
}

[BsonIgnoreExtraElements]
public class KYC
{
    [BsonElement("owner")]
    public Owner? Owner { get; set; }
    [BsonElement("CRI")]
    public List<string>? CRI { get; set; }
    [BsonElement("CVI")]
    public string? CVI { get; set; }
    [BsonElement("B2E")]
    public string? B2E { get; set; }
}
