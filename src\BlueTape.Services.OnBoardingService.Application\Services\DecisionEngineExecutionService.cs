﻿using BlueTape.AWSStepFunction.Abstractions;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class DecisionEngineExecutionService(
        IStepFunctionClient<CreditApplicationInitializationStepStartRequest> creditApplicationFunctionClient,
        IStepFunctionClient<DrawApprovalInitializationStepStartRequest> drawApprovalFunctionClient,
        ICreditApplicationRepository creditApplicationRepository, ILogger<DecisionEngineExecutionService> logger)
    : IDecisionEngineExecutionService
{
    public async Task<StepFunctionsExecutionResponse> StartCreditApplicationInitializationStep(CreditApplicationInitializationStepStartRequest stepStartRequest, CancellationToken ctx)
    {
        ArgumentNullException.ThrowIfNull(stepStartRequest);

        logger.LogInformation("Starting DE execution for credit application {id}", stepStartRequest.CreditApplicationId);
        var executionResult = await creditApplicationFunctionClient.StartAsync(stepStartRequest, stepStartRequest.JobId.ToString(), cancellationToken: ctx);
        logger.LogInformation("Finished DE execution for credit application {id}. Job id: {jobId}", stepStartRequest.CreditApplicationId, stepStartRequest.JobId);

        return new StepFunctionsExecutionResponse()
        {
            ExecutionArn = executionResult.ExecutionArn,
        };
    }

    public async Task<StepFunctionsExecutionResponse> StartDrawApprovalInitializationStep(DrawApprovalInitializationStepStartRequest stepStartRequest, CancellationToken ctx)
    {
        ArgumentNullException.ThrowIfNull(stepStartRequest);

        logger.LogInformation("Starting DE execution for draw approval {id}", stepStartRequest.DrawApprovalId);
        var executionResult = await drawApprovalFunctionClient.StartAsync(stepStartRequest, stepStartRequest.JobId.ToString(), cancellationToken: ctx);
        logger.LogInformation("Finished DE execution for draw approval {id}. Job id: {jobId}", stepStartRequest.DrawApprovalId, stepStartRequest.JobId);

        return new StepFunctionsExecutionResponse()
        {
            ExecutionArn = executionResult.ExecutionArn,
        };
    }

    public async Task PreValidateCreditApplicationDecisionEngineExecution(CreditApplicationType type, string? merchantId, string? companyId, string einHash, CancellationToken ctx)
    {
        logger.LogInformation("Started PreValidation process for company {companyId}, ein hash {einHash}, merchant {merchantId}", companyId, einHash, merchantId);

        if (string.IsNullOrEmpty(companyId))
        {
            throw new ValidationException("Draft does not contain company identifier.");
        }

        var existingCompanyCreditApplications = (await creditApplicationRepository.GetAllByFilters(new GetCreditApplicationQuery()
        {
            MerchantId = merchantId,
            EinHash = einHash,
            Type = [type.ToString()],
            Status = CreditApplicationConstants.SingleCreditApplicationApplicableStatuses
        }, ctx)).ToList();

        if (existingCompanyCreditApplications.Count > 0)
        {
            var failureReason =
                CreditApplicationExtensions.GetCreditApplicationsPreValidationFailureReason(einHash, existingCompanyCreditApplications);
            throw new ValidationException(failureReason);
        }

        logger.LogInformation("Finished PreValidation process for company {companyId}, ein hash {einHash}, merchant {merchantId}", companyId, einHash, merchantId);
    }

    public void PreValidateDrawApprovalDecisionEngineExecution(DrawApprovalDecisionEngineExecutionRequest request)
    {
        logger.LogInformation("Started draw approval PreValidation process for company id {id}", request.CompanyId);

        if (!request.Payables.Any())
        {
            throw new ValidationException("Credit application does not contain any payables");
        }

        logger.LogInformation("Finished draw approval PreValidation process for company id {id}", request.CompanyId);
    }
}
