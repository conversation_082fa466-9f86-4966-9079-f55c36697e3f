﻿using BlueTape.InvoiceService.Messages;
using BlueTape.ServiceBusMessaging;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services.Senders;

public class InvoiceSyncMessageSender(
    IConfiguration configuration,
    ILogger<InvoiceSyncMessageSender> logger) : ServiceBusMessageSender<SyncInvoiceMessagePayload>(configuration, logger,
    InfrastructureConstants.InvoiceSyncQueueName, InfrastructureConstants.InvoiceSyncQueueConnectionString), IInvoiceSyncMessageSender;
