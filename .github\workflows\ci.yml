name: Build and SonarCloud Analysis

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
 PACKAGE_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
 PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}
 NUGET_PACKAGES_DIRECTORY: ".nuget"
 PATH_TO_SLN: "./src"
 GIT_DEPTH: '0'

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4.1.1
      with:
          fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x

    - name: Set up JDK 
      uses: actions/setup-java@v3.13.0
      with:
       distribution: 'temurin'
       java-version: '19'

    - name: Cache SonarCloud packages
      uses: actions/cache@v4
      with:
        path: ~\sonar\cache
        key: ${{ runner.os }}-sonar
        restore-keys: ${{ runner.os }}-sonar
  
    - name: Install SonarCloud scanner
      if: steps.cache-sonar-scanner.outputs.cache-hit != 'true'
      run: dotnet tool install --global dotnet-sonarscanner

    - name: Restore dependencies
      run: dotnet restore $PATH_TO_SLN --packages $NUGET_PACKAGES_DIRECTORY -f

    - name: Build and analyze
      env:
        SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        PROJECT_KEY: ${{ vars.PROJECT_KEY }}
        ORGANIZATION: ${{ vars.ORGANIZATION }}
        EXCLUDE_FROM_COVERAGE: ${{ vars.EXCLUDE_FROM_COVERAGE }}
        EXCLUDE_FROM_DUPLICATION: ${{ vars.EXCLUDE_FROM_DUPLICATION }}
      run: |
          dotnet tool restore
          dotnet-sonarscanner begin /k:"$PROJECT_KEY" /o:"$ORGANIZATION" /d:sonar.login="$SONAR_TOKEN" /d:sonar.host.url="$SONAR_HOST_URL" /d:sonar.coverage.exclusions="$EXCLUDE_FROM_COVERAGE" /d:sonar.cpd.exclusions="$EXCLUDE_FROM_DUPLICATION" /d:sonar.cs.opencover.reportsPaths="**/TestResults/**/coverage.opencover.xml" -d:sonar.cs.vstest.reportsPaths="**/TestResults/*.trx"
          dotnet build --no-restore --configuration Release $PATH_TO_SLN
          dotnet test $PATH_TO_SLN --no-build --no-restore --configuration Release --verbosity normal --logger trx --collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=opencover
          dotnet-sonarscanner end /d:sonar.login="${{ secrets.SONAR_TOKEN }}"
