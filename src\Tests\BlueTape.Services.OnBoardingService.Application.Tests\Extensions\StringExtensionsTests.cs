﻿using BlueTape.Services.OnBoardingService.Application.Extensions;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Extensions;
public class StringExtensionsTests
{

    [Theory]
    [InlineData("There was a field validation error with the inquiry")]
    [InlineData("{ \"hello\": \"world\" ")]
    [InlineData("{\"\": \"world }")]
    [InlineData("{There was a field validation error with the inquiry}")]
    [InlineData("")]
    public void ToFormattedJsonString_NotValidJsonString_ReturnsOriginalString(string input)
    {
        var result = input.ToFormattedJsonString();

        result.ShouldBe(input);
    }

    [Fact]
    public void ToFormattedJsonString_EmptyString_ReturnsEmptyString()
    {
        var input = string.Empty;

        var result = input.ToFormattedJsonString();

        result.ShouldBe(input);
    }

    [Fact]
    public void ToFormattedJsonString_NullString_ReturnsEmptyString()
    {
        string? input = null;

        var result = input.ToFormattedJsonString();

        result.ShouldBe(string.Empty);
    }
}
