﻿using BlueTape.CompanyService.Common.Functions.AccountStatus;
using BlueTape.CompanyService.Common.Senders;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Utilities.Providers;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;
public class AccountStatusServiceTests
{
    private readonly AccountStatusService _accountStatusService;
    private readonly Mock<IAccountStatusChangeQueueSender> _accountStatusChangeQueueSenderMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();

    public AccountStatusServiceTests()
    {
        _accountStatusService =
            new AccountStatusService(_accountStatusChangeQueueSenderMock.Object, _dateProviderMock.Object);

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(new DateTime(2024, 01, 01));
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_EventTypeNull_AccountStatusNotChanged(CreditApplicationDocument creditApp)
    {
        creditApp.Status = null;

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Never);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusNew_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.New.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusProcessed_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.Processed.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusReview_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.Review.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusProcessing_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.Processing.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusSentBack_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.SentBack.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusApproved_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.Approved.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusCanceled_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.Canceled.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeAccountStatus_CreditStatusRejected_SendMessageToUpdateAccountStatus(CreditApplicationDocument creditApp)
    {
        creditApp.Status = CreditApplicationStatus.Rejected.ToString().ToLower();

        await _accountStatusService.ChangeAccountStatus(creditApp, default);

        _accountStatusChangeQueueSenderMock.Verify(
            x => x.SendMessage(It.IsAny<ServiceBusMessageBt<ChangeAccountStatusModel>>(), default), Times.Once);
    }
}
