﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class LiensScorer : IScoring
{
    public const int CompanyLiabilitiesThreshold = 5000;

    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        var result = new List<OwnerScore>();

        if (experian?.BusinessData != null && experian.BusinessData.Any())
        {
            foreach (var item in experian.BusinessData)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                ownerScore.Scores?.Add(Calculate("liens", item.LienBalance));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = Calculate("liens", experian?.LienBalance);
        return [new OwnerScore { Scores = [score] }];
    }

    private Score Calculate(string name, double? lienBalance)
    {
        lienBalance ??= 0;

        return new Score()
        {
            Name = name,
            Value = lienBalance != 0 ? lienBalance.ToString() : null,
            Pass = lienBalance.Value <= CompanyLiabilitiesThreshold ? ScoringResult.Pass : ScoringResult.Reject,
        };
    }
}
