﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Constants;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using TinyHelpers.Extensions;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class BriScorer : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        var companyRiskIndicatorManualReview = LexisNexisConstants.ListCompanyRiskIndicatorManualReview;

        var result = new List<OwnerScore>();

        if (kyb?.KYB != null && kyb.KYB.Any())
        {
            foreach (var item in kyb.KYB)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                ownerScore.Scores?.Add(Calculate("BRI", item.BRI, companyRiskIndicatorManualReview));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = Calculate("BRI", kyb?.BRI, companyRiskIndicatorManualReview);
        return [new OwnerScore { Scores = [score] }];
    }

    private Score Calculate(string name, List<string>? bri, IEnumerable<string> companyRiskIndicatorManualReview)
    {
        bool containsAnyRisks = companyRiskIndicatorManualReview.Any(x => bri != null && bri.Contains(x));
        var pass = bri == null || !bri.Any() || containsAnyRisks ? ScoringResult.Review : ScoringResult.Pass;

        return new Score()
        {
            Name = name,
            Value = !bri.IsNullOrEmpty() ? string.Join(", ", bri) : null,
            Pass = pass
        };
    }
}