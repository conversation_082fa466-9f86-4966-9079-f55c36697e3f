using BlueTape.LS.Domain.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class FactoringDetailsModel
{
    public decimal? ArAdvanceCreditLimit { get; set; }
    public decimal? ArAdvanceCreditOutstandingBalance { get; set; }
    public decimal? ArAdvanceCreditAvailableBalance { get; set; }
    public CreditStatus? InHouseCreditStatus { get; set; }
    public decimal? InHouseCreditLimit { get; set; }
    public decimal? InHouseCreditOutstandingBalance { get; set; }
    public decimal? InHouseCreditAvailableBalance { get; set; }
}