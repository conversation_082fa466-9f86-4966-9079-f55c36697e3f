﻿namespace BlueTape.Services.OnBoardingService.Infrastructure.Query.LoanApplication;

public class GetLoanApplicationQuery
{
    public string? Id { get; set; }
    public string? CompanyId { get; set; }
    public string? Status { get; set; }
    public bool? IsInvoiceDetailsExists { get; set; }
    public string? Name { get; set; }
    public DateTime? AppDateFrom { get; set; }
    public DateTime? AppDateTo { get; set; }
}
