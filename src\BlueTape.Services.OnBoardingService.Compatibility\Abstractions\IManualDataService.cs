﻿using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.Services.OnBoardingService.Compatibility.Models;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
public interface IManualDataService
{
    Task<IEnumerable<CashFlowItemResponse>> LoanApplicationManualDataStep(LoanApplicationDocument application,
        CancellationToken cancellationToken);
}
