﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class SixtyPlusDebtScorer : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        ScoringResult pass = ScoringResult.Review;
        double? score = null;

        var principalBusinessData = experian?.BusinessData?.FirstOrDefault(x => x.Owner != null && x.Owner.IsPrincipal.Value);

        var tradelinesDebt = principalBusinessData?.TradelinesDebt ?? experian?.TradelinesDebt ?? "0";

        if (decision?.Decision?.LoanRevenue.HasValue == true && !string.IsNullOrEmpty(tradelinesDebt))
        {
            score = (double.Parse(tradelinesDebt) / decision.Decision.LoanRevenue.Value) * 100;
            pass = score > 1 ? ScoringResult.Reject : ScoringResult.Pass;
        }

        if (principalBusinessData != null)
        {
            return [new OwnerScore
            {
                Owner = principalBusinessData.Owner,
                Scores = new List<Score>()
                {
                    new()
                    {
                        Name = "sixtyPlusDebt",
                        Value = score.ToString(),
                        Pass = pass
                    }
                }
            }];
        }
        else
        {
            return [new OwnerScore
            {
                Scores = new List<Score>()
                {
                    new()
                    {
                        Name = "sixtyPlusDebt",
                        Value = score.ToString(),
                        Pass = pass
                    }
                }
            }];
        }
    }
}
