using MongoDB.Bson;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Services.OnBoardingService.DataAccess.Pipelines;

[ExcludeFromCodeCoverage]
public static class InvoicePipelines
{
    public static readonly BsonDocument LookupInvoiceStatus = new("$lookup",
        new BsonDocument
        {
            { "from", "invoices" },
            { "let", new BsonDocument("payableIds", new BsonDocument("$map",
                        new BsonDocument { { "input", "$payables" },
                            { "as", "payable" },
                            { "in", new BsonDocument("$toObjectId", "$$payable._id") }
            }))},
            { "pipeline", new BsonArray
                {
                    new BsonDocument("$match", new BsonDocument("$expr",
                            new BsonDocument("$in", new BsonArray { "$_id", "$$payableIds" })
                        )),
                    new BsonDocument("$project",
                        new BsonDocument
                        {
                            {
                                "_id", 1
                            },
                            {
                                "status", 1
                            },
                            {
                                "invoice_due_date", 1
                            },
                            {
                                "invoice_number", 1
                            }
                        }
                    )
                }
            },
            {
                "as", "invoices"
            }
        });

    public static readonly BsonDocument AddInvoiceStatusToPayables = new("$addFields",
    new BsonDocument
    {
        {
            "payables", new BsonDocument("$map",
                new BsonDocument {
                    {
                        "input", "$payables"
                    },
                    {
                        "as", "payable"
                    },
                    {
                        "in", new BsonDocument("$mergeObjects",
                            new BsonArray
                            {
                                "$$payable",
                                new BsonDocument
                                {
                                    {
                                        "status", new BsonDocument("$let",
                                            new BsonDocument
                                            {
                                                {
                                                    "vars", new BsonDocument("matchedInvoice",
                                                        new BsonDocument("$arrayElemAt",
                                                            new BsonArray
                                                            {
                                                                new BsonDocument("$filter",
                                                                    new BsonDocument
                                                                    {
                                                                        {
                                                                            "input", "$invoices"
                                                                        },
                                                                        {
                                                                            "as", "invoice"
                                                                        },
                                                                        {
                                                                            "cond", new BsonDocument("$eq",
                                                                                new BsonArray
                                                                                {
                                                                                    "$$invoice._id",
                                                                                    new BsonDocument("$toObjectId", "$$payable._id")
                                                                                }
                                                                            )
                                                                        }
                                                                    }
                                                                ),
                                                                0
                                                            }))
                                                },
                                                {
                                                    "in", new BsonDocument("$ifNull",
                                                        new BsonArray
                                                        {
                                                            "$$matchedInvoice.status",
                                                            BsonNull.Value
                                                        })
                                                }
                                            })
                                    }
                                },
                                new BsonDocument
                                {
                                    {
                                        "invoiceNumber", new BsonDocument("$let",
                                            new BsonDocument
                                            {
                                                {
                                                    "vars", new BsonDocument("matchedInvoice",
                                                        new BsonDocument("$arrayElemAt",
                                                            new BsonArray
                                                            {
                                                                new BsonDocument("$filter",
                                                                    new BsonDocument
                                                                    {
                                                                        {
                                                                            "input", "$invoices"
                                                                        },
                                                                        {
                                                                            "as", "invoice"
                                                                        },
                                                                        {
                                                                            "cond", new BsonDocument("$eq",
                                                                                new BsonArray
                                                                                {
                                                                                    "$$invoice._id",
                                                                                    new BsonDocument("$toObjectId", "$$payable._id")
                                                                                }
                                                                            )
                                                                        }
                                                                    }
                                                                ),
                                                                0
                                                            }))
                                                },
                                                {
                                                    "in", new BsonDocument("$ifNull",
                                                        new BsonArray
                                                        {
                                                            "$$matchedInvoice.invoice_number",
                                                            BsonNull.Value
                                                        })
                                                }
                                            })
                                    }
                                },
                                new BsonDocument
                                {
                                    {
                                        "invoiceDueDate", new BsonDocument("$let",
                                            new BsonDocument
                                            {
                                                {
                                                    "vars", new BsonDocument("matchedInvoice",
                                                        new BsonDocument("$arrayElemAt",
                                                            new BsonArray
                                                            {
                                                                new BsonDocument("$filter",
                                                                    new BsonDocument
                                                                    {
                                                                        {
                                                                            "input", "$invoices"
                                                                        },
                                                                        {
                                                                            "as", "invoice"
                                                                        },
                                                                        {
                                                                            "cond", new BsonDocument("$eq",
                                                                                new BsonArray
                                                                                {
                                                                                    "$$invoice._id",
                                                                                    new BsonDocument("$toObjectId", "$$payable._id")
                                                                                }
                                                                            )
                                                                        }
                                                                    }
                                                                ),
                                                                0
                                                            }))
                                                },
                                                {
                                                    "in", new BsonDocument("$ifNull",
                                                        new BsonArray
                                                        {
                                                            "$$matchedInvoice.invoice_due_date",
                                                            BsonNull.Value
                                                        })
                                                }
                                            })
                                    }
                                }
                            })
                    }
                })
        }
    });
}