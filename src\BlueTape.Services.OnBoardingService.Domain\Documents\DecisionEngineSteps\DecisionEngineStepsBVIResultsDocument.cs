﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;

[BsonIgnoreExtraElements]
[MongoCollection("decisionEngineStepsBVIResults")]
public class DecisionEngineStepsBviResultsDocument : Document
{
    [BsonElement("decisionEngineStepId")]
    public string DecisionEngineStepId { get; set; } = string.Empty;

    [BsonElement("integrationLogId")]
    public string IntegrationLogId { get; set; } = string.Empty;

    [BsonElement("integrationSource")]
    public string IntegrationSource { get; set; } = string.Empty;

    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("executionId")]
    public string? ExecutionId { get; set; }
}
