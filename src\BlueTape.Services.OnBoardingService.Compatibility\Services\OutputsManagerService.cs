﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using MongoDB.Bson;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Dynamic;
using TinyHelpers.Extensions;
using Owner = BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication.Owner;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services;
public class OutputsManagerService(
    ILoanApplicationRepository loanApplicationRepository,
    IDecisionService decisionService,
    IKnockoutService knockoutService,
    IBluetapeService bluetapeService,
    IPlaidDataService plaidDataService,
    IDecisionEngineStepsRepository decisionEngineStepsRepository,
    IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
    IManualDataService manualDataService) : IOutputsManagerService
{
    private readonly JsonSerializerSettings _jsonSettings = new()
    {
        ContractResolver = new DefaultContractResolver
        {
            NamingStrategy = new CamelCaseNamingStrategy
            {
                ProcessDictionaryKeys = true,
                OverrideSpecifiedNames = true
            }
        },
        Formatting = Formatting.Indented
    };

    public async Task SyncProcessPlaidData(LoanApplicationDocument loanApplication, CancellationToken cancellationToken)
    {
        var cashFlow = await plaidDataService.LoanApplicationPlaidDataStep(loanApplication, cancellationToken);
        var result = new CashFlowData()
        {
            CashFlow = cashFlow,
        };

        var json = JsonConvert.SerializeObject(result, _jsonSettings);
        var data = JsonConvert.DeserializeObject<ExpandoObject>(json);

        var document = new OutputDocument()
        {
            Step = "ProcessPlaidData",
            Data = data
        };

        await loanApplicationRepository.UpsertOutputDocument(loanApplication.Id, document, cancellationToken);
    }

    public async Task SyncLoanDecision(LoanApplicationDocument loanApplication, CancellationToken cancellationToken)
    {
        var loanDecision = await decisionService.LoanDecision(loanApplication, cancellationToken);
        var document = new OutputDocument()
        {
            Step = "LoanDecision",
            Data = JsonConvert.DeserializeObject<ExpandoObject>(loanDecision.ToJson())
        };

        await loanApplicationRepository.UpsertOutputDocument(loanApplication.Id, document, cancellationToken);
    }

    public async Task SyncProcessManualData(LoanApplicationDocument loanApplication, CancellationToken cancellationToken)
    {
        var processManualData = await manualDataService.LoanApplicationManualDataStep(loanApplication, cancellationToken);
        var result = new CashFlowData()
        {
            CashFlow = processManualData,
        };

        var json = JsonConvert.SerializeObject(result, _jsonSettings);
        var data = JsonConvert.DeserializeObject<ExpandoObject>(json);

        var document = new OutputDocument()
        {
            Step = "ProcessManualData",
            Data = data
        };

        await loanApplicationRepository.UpsertOutputDocument(loanApplication.Id, document, cancellationToken);
    }

    public Task SyncValidation(string loanApplicationId, CancellationToken cancellationToken)
    {
        var jsonString = @"{ 'status': 'valid'}";
        var data = JsonConvert.DeserializeObject<ExpandoObject>(jsonString);

        var document = new OutputDocument()
        {
            Step = "validate",
            Data = data
        };

        return loanApplicationRepository.UpsertOutputDocument(loanApplicationId, document, cancellationToken);
    }

    public async Task SyncKyc(string loanApplicationId, string creditApplicationId, LoanApplicationDocument? loanApplication, LoanApplicationDocument? firstLoanApplication, AccountAuthorizationDocument? accountAuthorization, CancellationToken cancellationToken)
    {
        var result = new KycData { Fraud = [] };
        OutputDocument? document;

        if (accountAuthorization is not null)
        {
            foreach (var owner in accountAuthorization.OwnersDetails)
            {
                result.Fraud.Add(new()
                {
                    Owner = new Owner()
                    {
                        Id = owner.Id,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Type = "Individual",
                        IsPrincipal = owner.IsPrincipal,
                    },
                    EmailAge = new()
                    {
                        EmailAgeEaScoreRiskLevel = owner.EmailRiskScore,
                        EmailAgeIpRiskLevel = owner.IPRiskLevel,
                        EmailAgeDomainRiskLevel = owner.DomainRiskLevel
                    },
                    FraudPoint = new()
                    {
                        FraudPointScoreRiskLevel = owner.FraudpointScore,
                    }
                });
            }


            var json = result.ToJson();
            var data = JsonConvert.DeserializeObject<ExpandoObject>(json);

            document = new OutputDocument()
            {
                Step = StepName.KYC.ToString(),
                Data = data
            };
        }
        else
        {
            document = firstLoanApplication?.Outputs?.FirstOrDefault(x => x.Step == "KYC");
        }

        await loanApplicationRepository.UpsertOutputDocument(loanApplicationId, document, cancellationToken);
    }

    public async Task SyncKyb(string loanApplicationId, CreditApplication creditApplication,
        LoanApplicationDocument loanApplication, LoanApplicationDocument? firstLoanApplication, DraftDocument draft,
        AccountAuthorizationDocument? accountAuthorization, CancellationToken cancellationToken)
    {
        var businessContent = draft.Data?.BusinessInfo?.Items?.FirstOrDefault(x => x.Identifier == "businessName")
            ?.Content.ToBsonDocument();
        var legalName = businessContent?["legalName"].AsString;
        OutputDocument? document;

        if (accountAuthorization is not null)
        {
            KybData result = new KybData
            {
                KYB =
                [
                    new KYB
                    {
                        Owner = new Owner()
                        {
                            BusinessName = legalName,
                            IsPrincipal = true,
                            Type = "Entity"
                        },
                        BVI = accountAuthorization?.BusinessDetails?.BVI,
                        BRI = accountAuthorization?.BusinessDetails?.BRICodes.Where(c => c != null).Select(c => c!)
                            .ToList()
                    }
                ],
                KYC = []
            };

            foreach (var owner in accountAuthorization.OwnersDetails)
            {
                result.KYC.Add(new KYC
                {
                    Owner = new Owner()
                    {
                        Id = owner.Id,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Type = "Individual",
                        IsPrincipal = owner.IsPrincipal,
                    },
                    CVI = owner.CVI,
                    CRI = owner.CRICodes.Where(c => c != null).Select(c => c!).ToList(),
                    B2E = owner.B2ELinkIndex
                });
            }

            dynamic data = JsonConvert.DeserializeObject<ExpandoObject>(result.ToJson())!;

            document = new OutputDocument()
            {
                Step = StepName.KYB.ToString(),
                Data = data
            };
        }
        else
        {
            document = firstLoanApplication?.Outputs?.FirstOrDefault(x => x.Step == "KYB");
        }

        await loanApplicationRepository.UpsertOutputDocument(loanApplicationId, document, cancellationToken);
    }

    public async Task SyncExperian(string loanApplicationId, string creditApplicationId,
        LoanApplicationDocument loanApplication, LoanApplicationDocument? firstLoanApplication, DraftDocument draft,
        AccountAuthorizationDocument? accountAuthorization, CancellationToken cancellationToken)
    {
        var businessContent = draft.Data?.BusinessInfo?.Items?.FirstOrDefault(x => x.Identifier == "businessName")
            ?.Content.ToBsonDocument();
        var legalName = businessContent?["legalName"].AsString;
        OutputDocument? document;

        if (accountAuthorization is not null)
        {
            double.TryParse(accountAuthorization?.BusinessDetails?.ReliabilityCode, out var reliabilityCode);

            var result = new ExperianData
            {
                BusinessData =
                [
                    new BusinessData
                    {
                        Owner = new Owner
                        {
                            BusinessName = legalName,
                            IsPrincipal = true,
                            Type = "Entity"
                        },
                        ReliabilityCode = reliabilityCode,
                        BankruptcyIndicator = accountAuthorization?.BusinessDetails?.BankruptcyIndicator,
                        JudgmentBalance = (double?)accountAuthorization?.BusinessDetails?.JudgmentBalance,
                        LienBalance = (double?)accountAuthorization?.BusinessDetails?.LienBalance,
                        AccountBalanceDebt = (double?)accountAuthorization?.BusinessDetails?.CurrentDebt,
                        TradelinesBalance = null,
                        TradelinesPercentage = null,
                        TradelinesDebt = null,
                        YearsOnFile = null,
                    }
                ],
                OwnersData = []
            };

            foreach (var owner in accountAuthorization.OwnersDetails)
            {
                result.OwnersData.Add(new OwnerData()
                {
                    Owner = new Owner()
                    {
                        Id = owner.Id,
                        FirstName = owner.FirstName,
                        LastName = owner.LastName,
                        Type = "Individual",
                        IsPrincipal = owner.IsPrincipal,
                    },
                    PastDueAmount = 0,
                    InquiriesDuringLast6Months = owner.InquiriesDuringLast6Months,
                    Score = owner.FICOScore,
                    LastBankruptcyDate = owner.LastPersonalBankruptcyDate
                });
            }

            var jsonData = result.ToJson();
            dynamic data = JsonConvert.DeserializeObject<ExpandoObject>(jsonData)!;

            document = new OutputDocument()
            {
                Step = "creditStatus",
                Data = data
            };
        }
        else
        {
            document = firstLoanApplication?.Outputs?.FirstOrDefault(x => x.Step == "creditStatus");
        }

        await loanApplicationRepository.UpsertOutputDocument(loanApplicationId, document, cancellationToken);
    }

    public async Task SyncBluetape(LoanApplicationDocument loanApplication, string creditApplicationId, CancellationToken cancellationToken)
    {
        var blueTapeStep = await bluetapeService.LoanApplicationBluetapeStep(loanApplication, cancellationToken);
        var data = JsonConvert.DeserializeObject<ExpandoObject>(blueTapeStep.ToJson());

        var document = new OutputDocument()
        {
            Step = "BlueTape",
            Data = data
        };
        await loanApplicationRepository.UpsertOutputDocument(loanApplication.Id, document, cancellationToken);
    }

    public async Task SyncBankAccounts(string loanApplicationId, string creditApplicationId, CancellationToken cancellationToken)
    {
        var bank = await decisionEngineStepsRepository.GetByStepName(creditApplicationId, StepName.BankAccountVerification, cancellationToken);
        var status = bank is { Status: "Skipped" } ? "no-data" : "plaid";

        var document = new OutputDocument()
        {
            Step = "CheckBankData",
            Data = new { status }
        };

        await loanApplicationRepository.UpsertOutputDocument(loanApplicationId, document, cancellationToken);
    }

    public Task SyncKnockout(LoanApplicationDocument loanApplication, string creditApplicationId, CancellationToken cancellationToken)
    {
        var knockout = knockoutService.CalculateScore(loanApplication);
        var data = JsonConvert.DeserializeObject<ExpandoObject>(knockout.ToJson());

        var document = new OutputDocument()
        {
            Step = "Knockout",
            Data = data
        };

        return loanApplicationRepository.UpsertOutputDocument(loanApplication.Id, document, cancellationToken);
    }

    public async Task SyncGiact(string loanApplicationId, string creditApplicationId, LoanApplicationDocument loanApplication, CancellationToken cancellationToken)
    {
        var giact = await decisionEngineStepsBviResultsService.GetGiactRawData(creditApplicationId, cancellationToken);

        var data = JsonConvert.DeserializeObject<ExpandoObject>(giact);

        var document = new OutputDocument()
        {
            Step = "VerifyGiactAccount",
            Data = !giact.IsNullOrEmpty() ? data : null
        };

        await loanApplicationRepository.UpsertOutputDocument(loanApplicationId, document, cancellationToken);
    }
}
