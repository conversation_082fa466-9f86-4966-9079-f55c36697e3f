﻿using BlueTape.Services.OnBoardingService.Domain.Serializers;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class ExperianData
{
    [BsonElement("businessData")]
    public List<BusinessData>? BusinessData { get; set; }

    [BsonElement("ownersData")]
    public List<OwnerData>? OwnersData { get; set; }

    [BsonElement("reliabilityCode")]
    public double? ReliabilityCode { get; set; }

    [BsonElement("bankruptcyIndicator")]
    public bool? BankruptcyIndicator { get; set; }

    [BsonElement("judgmentBalance")]
    public double? JudgmentBalance { get; set; }

    [BsonElement("lienBalance")]
    public double? LienBalance { get; set; }

    [BsonSerializer(typeof(StringSerializer))]
    [BsonElement("tradelinesDebt")]
    public string? TradelinesDebt { get; set; }
}

[BsonIgnoreExtraElements]
public class BusinessData
{
    [BsonElement("owner")]
    public Owner? Owner { get; set; }

    [BsonElement("reliabilityCode")]
    public double? ReliabilityCode { get; set; }

    [BsonElement("bankruptcyIndicator")]
    public bool? BankruptcyIndicator { get; set; }

    [BsonElement("judgmentBalance")]
    public double? JudgmentBalance { get; set; }

    [BsonElement("lienBalance")]
    public double? LienBalance { get; set; }

    [BsonElement("accountBalanceDebt")]
    public double? AccountBalanceDebt { get; set; }

    [BsonElement("tradelinesBalance")]
    public string? TradelinesBalance { get; set; }

    [BsonElement("tradelinesPercentage")]
    public string? TradelinesPercentage { get; set; }

    [BsonElement("tradelinesDebt")]
    public string? TradelinesDebt { get; set; }

    [BsonElement("yearsOnFile")]
    public string? YearsOnFile { get; set; }
}

[BsonIgnoreExtraElements]
public class OwnerData
{
    [BsonElement("owner")]
    public Owner? Owner { get; set; }

    [BsonElement("pastDueAmount")]
    public double? PastDueAmount { get; set; }

    [BsonElement("inquiriesDuringLast6Months")]
    public string? InquiriesDuringLast6Months { get; set; }

    [BsonElement("score")]
    public string? Score { get; set; }

    [BsonElement("lastBankruptcyDate")]
    [BsonDateTimeOptions(Representation = BsonType.String)]
    public DateTime? LastBankruptcyDate { get; set; }
}
