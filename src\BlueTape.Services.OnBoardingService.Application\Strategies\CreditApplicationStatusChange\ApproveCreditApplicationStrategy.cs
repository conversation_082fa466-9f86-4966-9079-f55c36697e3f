﻿using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.LS.Domain.Enums;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Credit;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using PurchaseTypeOption = BlueTape.OBS.Enums.PurchaseTypeOption;
using CompanyDebtInvestorType = BlueTape.CompanyService.Common.Enums.DebtInvestorType;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;

public class ApproveCreditApplicationStrategy : ChangeCreditApplicationStatusStrategy
{
    private readonly ILoanService _loanService;
    private readonly ICompanyExternalService _companyExternalService;

    public ApproveCreditApplicationStrategy(IDateProvider dateProvider,
        IMapper mapper,
        IAccountStatusService accountStatusService,
        ICreditApplicationRepository creditApplicationRepository,
        ILoanService loanService,
        ICreditApplicationSyncService creditApplicationSyncService,
        ICompanyExternalService companyExternalService,
        ICreditApplicationNotesService creditApplicationNotesService,
        ILogger<ApproveCreditApplicationStrategy> logger)
        : base(dateProvider, mapper, accountStatusService, creditApplicationRepository, creditApplicationSyncService,
            creditApplicationNotesService, logger)
    {
        _loanService = loanService;
        _companyExternalService = companyExternalService;
    }

    public override bool IsApplicable(string status) => CreditApplicationStatus.Approved.IsEnum(status);

    public override async Task<CreditApplication> ChangeStatus(CreditApplicationDocument creditApplicationDocument,
        ReviewCreditApplicationDto reviewCreditApplication, string userId, CancellationToken ct)
    {
        if (CreditApplicationType.GetPaid.IsEnum(creditApplicationDocument.Type))
        {
            return await ApproveGetPaidCreditApplication(creditApplicationDocument, new MerchantSettings()
            {
                CardPricingPackageId = reviewCreditApplication.CardPricingPackageId,
                LoanPricingPackageId = reviewCreditApplication.LoanPricingPackageId,
                IsAchDelay = reviewCreditApplication.IsAchDelay,
                DebtInvestor = reviewCreditApplication.LoanPricingPackageId == null
                    ? null
                    : reviewCreditApplication.DebtInvestor
            }, userId, reviewCreditApplication.Note, ct);
        }

        reviewCreditApplication.RevenueFallPercentage ??= 0;

        if (reviewCreditApplication.ApprovedCreditLimit is null)
            throw new ValidationException("Approved credit limit must be provided");
        if (reviewCreditApplication.ApprovedCreditLimit <= 0)
            throw new ValidationException("Approved credit limit must be greater than 0");
        if (reviewCreditApplication.RevenueFallPercentage is < 0 or > 100)
            throw new ValidationException("Revenue fall percentage must be between 0 and 100");
        if (string.IsNullOrEmpty(reviewCreditApplication.PurchaseType) && string.Equals(creditApplicationDocument.Type,
                CreditApplicationType.LineOfCredit.ToString(), StringComparison.InvariantCultureIgnoreCase))
            throw new ValidationException("The purchase type must be provided");

        var currentDateTime = DateProvider.CurrentDateTime;

        creditApplicationDocument.ApprovedCreditLimit = reviewCreditApplication.ApprovedCreditLimit!.Value;
        creditApplicationDocument.PurchaseTypeOption = reviewCreditApplication.PurchaseType;
        creditApplicationDocument.RevenueFallPercentage = reviewCreditApplication.RevenueFallPercentage;
        creditApplicationDocument.Status = CreditApplicationStatus.Approved.ToString().ToLower();
        creditApplicationDocument.ApprovedAt = currentDateTime;
        creditApplicationDocument.ApprovedBy = userId;
        if (reviewCreditApplication.DebtInvestor is not null)
        {
            creditApplicationDocument.MerchantSettings ??= new MerchantSettingsDocument();
            creditApplicationDocument.MerchantSettings.DebtInvestor = reviewCreditApplication.DebtInvestor;
        }

        Logger.LogInformation("Started update of credit application date {Id}. New Status: {Status}",
            creditApplicationDocument.Id, reviewCreditApplication.NewStatus);

        var creditApplication = await CreditApplicationRepository.Update(creditApplicationDocument, ct);
        await AccountStatusService.ChangeAccountStatus(creditApplicationDocument, ct);
        await CreateCredit(creditApplication, reviewCreditApplication.PurchaseType,
            reviewCreditApplication.ApprovedCreditLimit, reviewCreditApplication.RevenueFallPercentage.Value, ct);

        await CreditApplicationSyncService.SyncApplicableCreditApplication(creditApplication, ct);

        if (CreditApplicationType.InHouseCredit.IsEnum(creditApplication.Type)
            && creditApplicationDocument.CustomerAccount is not null)
        {
            var merchant = await _companyExternalService.GetById(creditApplication.MerchantId!, ct);
            await _companyExternalService.SetCustomerIhcSettings(creditApplicationDocument.CustomerAccount.Id,
                new UpdateCustomerModel
                {
                    ResourcePercentage = reviewCreditApplication.RecoursePercentage,
                    FactoringTerm = merchant?.Settings?.ARAdvance?.DefaultFactoringTerm,
                    SupplierPackage = merchant?.Settings?.ARAdvance?.DefaultSupplierPackage,
                    Limit = decimal.ToDouble(creditApplication.ApprovedCreditLimit!.Value)
                }, ct);
        }

        await UpdateCompanySettingsBasedOnCreditApplicationType(creditApplicationDocument, ct);

        Logger.LogInformation("Finished updating credit application {Id}", creditApplication.Id);
        return Mapper.Map<CreditApplication>(creditApplication);
    }

    private async Task<CreditApplication> ApproveGetPaidCreditApplication(
        CreditApplicationDocument creditApplicationDocument,
        MerchantSettings merchantSettings,
        string userId,
        string? note,
        CancellationToken ct)
    {
        var currentDateTime = DateProvider.CurrentDateTime;

        creditApplicationDocument.MerchantSettings = Mapper.Map<MerchantSettingsDocument>(merchantSettings);
        creditApplicationDocument.ApprovedCreditLimit = null;
        creditApplicationDocument.RevenueFallPercentage = null;
        creditApplicationDocument.PurchaseTypeOption = null;
        creditApplicationDocument.ApprovedAt = currentDateTime;
        creditApplicationDocument.ApprovedBy = userId;
        creditApplicationDocument.StatusNote = note;
        creditApplicationDocument.Status = CreditApplicationStatus.Approved.ToString().ToLower();

        var creditApplication = await CreditApplicationRepository.Update(creditApplicationDocument, ct);
        await GenerateStatusNote(creditApplication, note, userId, ct);
        await AccountStatusService.ChangeAccountStatus(creditApplicationDocument, ct);
        await CreditApplicationSyncService.SyncApplicableCreditApplication(creditApplicationDocument, ct);
        await UpdateCompanySettingsBasedOnCreditApplicationType(creditApplicationDocument, ct);

        return Mapper.Map<CreditApplication>(creditApplication);
    }

    private async Task UpdateCompanySettingsBasedOnCreditApplicationType(
        CreditApplicationDocument creditApplicationDocument, CancellationToken ct)
    {
        var creditApplicationType = creditApplicationDocument.Type.ParseToEnum<CreditApplicationType>();

        if (creditApplicationType is null
            || creditApplicationDocument.MerchantSettings?.DebtInvestor is null
            || creditApplicationDocument.CompanyId is null
            || (creditApplicationType is not CreditApplicationType.GetPaid
                && creditApplicationType is not CreditApplicationType.ARAdvance))
            return;

        var updateModel = new UpdateCompanyModel
        {
            Settings = new CompanySettingsModel()
        };
        var companyDebtInvestor =
            (CompanyDebtInvestorType)creditApplicationDocument.MerchantSettings.DebtInvestor.Value;

        switch (creditApplicationType)
        {
            case CreditApplicationType.GetPaid:
                updateModel.Settings.DefaultDebtInvestorTradeCredit = companyDebtInvestor;
                break;
            case CreditApplicationType.ARAdvance:
                updateModel.Settings.ARAdvance = new ArAdvanceModel { DefaultDebtInvestor = companyDebtInvestor };
                break;
            default:
                throw new ValidationException($"Unsupported credit application type: {creditApplicationType}");
        }

        await _companyExternalService.UpdateCompany(creditApplicationDocument.CompanyId,
            creditApplicationDocument.ApprovedBy ?? creditApplicationDocument.UpdatedBy ?? string.Empty,
            updateModel, ct);
    }

    private Task CreateCredit(CreditApplicationDocument creditApplication, string? purchaseType, decimal? creditLimit,
        double revenueFallPercentage, CancellationToken ct)
    {
        if (!creditLimit.HasValue) return Task.CompletedTask;

        if (creditApplication.CompanyId is null)
            throw new ValidationException(
                $"Cannot create credit for credit application {creditApplication.Id} with empty company id.");

        return _loanService.CreateCredit(new CreateCreditModel
        {
            CreditApplicationId = creditApplication.Id,
            CompanyId = creditApplication.CompanyId,
            CreditLimit = creditLimit.Value,
            Currency = "usd",
            StartDate = DateProvider.CurrentDate,
            MerchantId = creditApplication.MerchantId,
            Product = creditApplication.Type.ParseToEnum<ProductType>() ?? ProductType.LineOfCredit,
            PurchaseType = purchaseType is not null
                ? Enum.Parse<PurchaseTypeOption>(string.Concat(purchaseType[0].ToString().ToUpper(),
                    purchaseType.AsSpan(1)))
                : null,
            RevenueFallPercentage = revenueFallPercentage
        }, ct);
    }
}