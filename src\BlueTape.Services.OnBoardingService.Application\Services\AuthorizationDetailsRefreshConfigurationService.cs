﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Services
{
    public class AuthorizationDetailsRefreshConfigurationService(
        IAuthorizationDetailsRefreshConfigurationRepository refreshConfigurationRepository,
        IMapper mapper)
        : IAuthorizationDetailsRefreshConfigurationService
    {
        public async Task<RefreshCheckConfiguration?> GetRefreshCheckByScheduledUpdateType(string scheduledUpdate, CancellationToken ct)
        {
            var configuration = await refreshConfigurationRepository.GetConfiguration(ct);

            var check = configuration.Checks.FirstOrDefault(x => x.ScheduledUpdate == scheduledUpdate);

            return mapper.Map<RefreshCheckConfiguration>(check);
        }

        public async Task<IReadOnlyList<RefreshCheckConfiguration>> GetRefreshChecks(CancellationToken ct)
        {
            var configuration = await refreshConfigurationRepository.GetConfiguration(ct);

            return mapper.Map<IReadOnlyList<RefreshCheckConfiguration>>(configuration.Checks);
        }
    }
}
