﻿using AutoMapper;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.DecisionEngineSteps)]
[ApiController]
public class DecisionEngineStepsController : ControllerBase
{
    private readonly IDecisionEngineStepsService _decisionEngineStepsService;
    private readonly IMapper _mapper;
    private readonly IValidator<CreateDecisionEngineStepsDto> _createDecisionEngineStepsDtoValidator;
    private readonly IValidator<UpdateDecisionEngineStepsDto> _updateDecisionEngineStepsDtoValidator;

    public DecisionEngineStepsController(IDecisionEngineStepsService decisionEngineStepsService,
        IMapper mapper,
        IValidator<CreateDecisionEngineStepsDto> createDecisionEngineStepsDtoValidator,
        IValidator<UpdateDecisionEngineStepsDto> updateDecisionEngineStepsDtoValidator)
    {
        _decisionEngineStepsService = decisionEngineStepsService;
        _mapper = mapper;
        _createDecisionEngineStepsDtoValidator = createDecisionEngineStepsDtoValidator;
        _updateDecisionEngineStepsDtoValidator = updateDecisionEngineStepsDtoValidator;
    }

    /// <summary>
    /// Get decision engine steps by credit application id
    /// </summary>
    /// <param name="id">Credit application id</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /DecisionEngineSteps/CreditApplication/6576f3f6741f7b4727a693d0
    /// 
    /// </remarks>

    [HttpGet($"{EndpointConstants.CreditApplication}/{EndpointConstants.Id}")]
    public async Task<IEnumerable<DecisionEngineStepsDto>> GetByCreditApplicationId(string id, CancellationToken ct) =>
        _mapper.Map<IEnumerable<DecisionEngineStepsDto>>(await _decisionEngineStepsService.GetByCreditApplicationId(id, ct));

    /// <summary>
    /// Get decision engine steps by draw approval id
    /// </summary>
    /// <param name="id">Draw approval id</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /DecisionEngineSteps/DrawApproval/6576f3f6741f7b4727a693d0
    /// 
    /// </remarks>

    [HttpGet($"{EndpointConstants.DrawApproval}/{EndpointConstants.Id}")]
    public async Task<IEnumerable<DecisionEngineStepsDto>> GetByDrawApprovalId(string id, CancellationToken ct) =>
        _mapper.Map<IEnumerable<DecisionEngineStepsDto>>(await _decisionEngineStepsService.GetByDrawApprovalId(id, ct));

    /// <summary>
    /// Get decision engine steps by execution id
    /// </summary>
    /// <param name="id">Execution id</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /DecisionEngineSteps/Execution/6576f3f6741f7b4727a693d0
    /// 
    /// </remarks>

    [HttpGet($"{EndpointConstants.Execution}/{EndpointConstants.Id}")]
    public async Task<IEnumerable<DecisionEngineStepsDto>> GetByExecutionIdId(string id, CancellationToken ct)
        => _mapper.Map<IEnumerable<DecisionEngineStepsDto>>(await _decisionEngineStepsService.GetByExecutionId(id, ct));

    /// <summary>
    /// Get decision engine steps by Id
    /// </summary>
    /// <param name="id">The DecisionEngineSteps id</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     GET /DecisionEngineSteps/650b28825f07d3ca092f294a
    /// 
    /// </remarks>

    [HttpGet(EndpointConstants.Id)]
    public async Task<DecisionEngineStepsDto> GetById(string id, CancellationToken ct) =>
        _mapper.Map<DecisionEngineStepsDto>(await _decisionEngineStepsService.GetById(id, ct));

    /// <summary>
    /// Create new DecisionEngineSteps
    /// </summary>
    /// <param name="createDecisionEngineSteps">Model to create DecisionEngineSteps</param>
    /// <remarks>
    /// Sample request:
    ///
    /// POST /DecisionEngineSteps
    /// {
    ///  "executionId": "string",
    ///  "creditApplicationId": "string",
    ///  "step": "string",
    ///  "policyVersion": "string",
    ///  }
    /// 
    /// </remarks>

    [HttpPost]
    public async Task<DecisionEngineStepsDto> Post([FromBody] CreateDecisionEngineStepsDto createDecisionEngineSteps, CancellationToken ct)
    {
        await _createDecisionEngineStepsDtoValidator.ValidateAndThrowAsync(createDecisionEngineSteps, ct);

        var createModel = _mapper.Map<CreateDecisionEngineSteps>(createDecisionEngineSteps);

        return _mapper.Map<DecisionEngineStepsDto>(await _decisionEngineStepsService.Create(createModel, ct));
    }

    /// <summary>
    /// Attach new step with status to DecisionEngineSteps
    /// </summary>
    /// <param name="updateDecisionEngineSteps">Model to update DecisionEngineSteps</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     PATCH /DecisionEngineSteps/650b28825f07d3ca092f294a
    /// {
    ///  "newStatus": "string",
    ///  "results": [
    ///  {
    ///      "code": "string",
    ///      "scope": "string",
    ///      "ownerIdentifier": "string",
    ///      "ownerSsnHash": "string",
    ///      "comparisonSource": "string",
    ///      "comparisonJustification": "string",
    ///      "comparisonValue": "string",
    ///      "thresholdValue": "string",
    ///      "result": "string"
    ///  }
    ///  ],
    ///  "updatedBy": "string",
    ///  }
    /// 
    /// </remarks>

    [HttpPatch(EndpointConstants.Id)]
    public async Task<DecisionEngineStepsDto> Patch(string id, [FromBody] UpdateDecisionEngineStepsDto updateDecisionEngineSteps, CancellationToken ct)
    {
        await _updateDecisionEngineStepsDtoValidator.ValidateAndThrowAsync(updateDecisionEngineSteps, ct);

        var updateModel = _mapper.Map<UpdateDecisionEngineSteps>(updateDecisionEngineSteps);
        updateModel.Id = id;

        return _mapper.Map<DecisionEngineStepsDto>(await _decisionEngineStepsService.Update(updateModel, ct));
    }

    [HttpGet]
    public async Task<IEnumerable<DecisionEngineStepsDto>> GetByQuery([FromQuery] DecisionEngineStepQueryDto query, CancellationToken ct)
        => _mapper.Map<IEnumerable<DecisionEngineStepsDto>>(await _decisionEngineStepsService.GetByQuery(_mapper.Map<DecisionEngineStepsQueryModel>(query), ct));

    [HttpGet($"{EndpointConstants.Company}/{EndpointConstants.Id}/{EndpointConstants.LastExecutions}")]
    public async Task<IEnumerable<DecisionEngineStepsDto>> GetByCompanyId([FromRoute] string id, [FromQuery] CreditApplicationType? creditApplicationType, CancellationToken ct)
        => _mapper.Map<IEnumerable<DecisionEngineStepsDto>>(await _decisionEngineStepsService.GetByCompanyId(id, creditApplicationType, ct));
}
