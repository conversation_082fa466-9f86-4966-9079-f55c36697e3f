﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using System.Globalization;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class YearsInBusinessScorer : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        ScoringResult pass = ScoringResult.Review;
        string? score = decision?.Decision?.BusinessAge;

        if (!string.IsNullOrEmpty(score))
        {
            DateTime age;
            bool parsed = DateTime.TryParseExact(score, "MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out age);

            if (parsed)
            {
                int yearsDifference = (DateTime.Now.Year - age.Year) - (DateTime.Now.Month < age.Month ? 1 : 0);
                pass = yearsDifference < 2 ? ScoringResult.Reject : ScoringResult.Pass;
            }
        }

        var principalBusiness = kyb?.KYB?.FirstOrDefault(
            data => data.Owner?.IsPrincipal == true
        )?.Owner;


        return [new OwnerScore
            {
                Owner = principalBusiness,
                Scores = new List<Score>()
                {
                    new()
                    {
                        Name = "yearsInBusiness",
                        Value = score,
                        Pass = pass
                    }
                }
            }];
    }
}
