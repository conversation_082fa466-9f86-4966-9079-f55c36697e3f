﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CreditApplicationAuthorizationDetailsServiceTests
{
    private readonly ICreditApplicationAuthorizationDetailsService _creditApplicationAuthorizationDetailsService;

    private readonly Mock<ICreditApplicationAuthorizationDetailsRepository> _creditApplicationAuthorizationDetailsRepository = new();
    private readonly Mock<IMapper> _mapperMock = new();
    private readonly Mock<ILogger<CreditApplicationAuthorizationDetailsService>> _logger = new();
    private readonly Mock<IAccountAuthorizationsService> _accountAuthorizationsService = new();

    public CreditApplicationAuthorizationDetailsServiceTests()
    {
        _creditApplicationAuthorizationDetailsService = new CreditApplicationAuthorizationDetailsService(_creditApplicationAuthorizationDetailsRepository.Object, _mapperMock.Object, _logger.Object, _accountAuthorizationsService.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetByCreditApplicationId_EntryExists_ReturnsCreditApplicationAuthorizationDetails(string creditAppId, List<CreditApplicationAuthorizationDetailsDocument> documents,
        CreditApplicationAuthorizationDetails creditApplicationAuthorization)
    {
        _creditApplicationAuthorizationDetailsRepository.Setup(x => x.GetByCreditApplicationId(creditAppId, default))
            .ReturnsAsync(documents);
        _mapperMock.Setup(x => x.Map<CreditApplicationAuthorizationDetails>(documents[0])).Returns(creditApplicationAuthorization);
        var result = await _creditApplicationAuthorizationDetailsService.GetByCreditApplicationId(creditAppId, default);

        result.ShouldBe(creditApplicationAuthorization);
    }

    [Theory, CustomAutoData]
    public async Task GetByCreditApplicationId_EntryExist_ReturnsCreditApplicationAuthorizationDetails(string creditAppId)
    {
        _creditApplicationAuthorizationDetailsRepository.Setup(x => x.GetByCreditApplicationId(creditAppId, default))
            .ReturnsAsync(Enumerable.Empty<CreditApplicationAuthorizationDetailsDocument>());
        _mapperMock.Setup(x => x.Map<CreditApplicationAuthorizationDetails>(null)).Returns((CreditApplicationAuthorizationDetails)null!);
        var result = await _creditApplicationAuthorizationDetailsService.GetByCreditApplicationId(creditAppId, default);

        result.ShouldBeNull();
    }

    [Theory, CustomAutoData]
    public async Task UpdateByCreditApplicationId_ValidData_ReturnsUpdatedCreditApplicationAuthorizationDetails(UpdateCreditApplicationAuthorizationDetails updateCreditApplicationAuthorizationDetails,
        CreditApplicationAuthorizationDetailsDocument creditApplicationAuthorizationDetailsDocument)
    {
        _creditApplicationAuthorizationDetailsRepository.Setup(x =>
            x.UpdateByCreditApplicationId(creditApplicationAuthorizationDetailsDocument, default));
        _mapperMock.Setup(x => x.Map<CreditApplicationAuthorizationDetailsDocument>(updateCreditApplicationAuthorizationDetails)).Returns(creditApplicationAuthorizationDetailsDocument);

        await _creditApplicationAuthorizationDetailsService.UpdateByCreditApplicationId(updateCreditApplicationAuthorizationDetails, default);

        _creditApplicationAuthorizationDetailsRepository.Verify(x =>
            x.UpdateByCreditApplicationId(creditApplicationAuthorizationDetailsDocument, default));
    }

    [Theory, CustomAutoData]
    public async Task Create_ValidData_ReturnsUpdatedCreditApplicationAuthorizationDetails(CreditApplicationAuthorizationDetails creditApplicationAuthorizationDetails,
        CreditApplicationAuthorizationDetailsDocument creditApplicationAuthorizationDetailsDocument)
    {
        _creditApplicationAuthorizationDetailsRepository.Setup(x =>
            x.Add(creditApplicationAuthorizationDetailsDocument, default));
        _mapperMock.Setup(x => x.Map<CreditApplicationAuthorizationDetailsDocument>(creditApplicationAuthorizationDetails)).Returns(creditApplicationAuthorizationDetailsDocument);

        await _creditApplicationAuthorizationDetailsService.Create(creditApplicationAuthorizationDetails, default);

        _creditApplicationAuthorizationDetailsRepository.Verify(x =>
            x.Add(creditApplicationAuthorizationDetailsDocument, default));
    }
}