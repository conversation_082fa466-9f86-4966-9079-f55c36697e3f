﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<AzureFunctionsVersion>v4</AzureFunctionsVersion>
		<OutputType>Exe</OutputType>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
	  <Content Include="appsettings.beta.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Include="appsettings.dev.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Include="appsettings.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Include="appsettings.prod.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Include="appsettings.qa.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	</ItemGroup>
	<ItemGroup>
		<None Update="local.settings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.ServiceBus" Version="5.22.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.3.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\BlueTape.Functions.Hosting\BlueTape.Functions.Hosting.csproj" />
	</ItemGroup>
</Project>