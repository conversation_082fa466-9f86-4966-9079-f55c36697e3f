﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class CreditApplicationAuthorizationDetailsService : ICreditApplicationAuthorizationDetailsService
{
    private readonly ICreditApplicationAuthorizationDetailsRepository _creditApplicationAuthorizationDetailsRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreditApplicationAuthorizationDetailsService> _logger;
    private readonly IAccountAuthorizationsService _accountAuthorizationsService;

    public CreditApplicationAuthorizationDetailsService(ICreditApplicationAuthorizationDetailsRepository creditApplicationAuthorizationDetailsRepository,
        IMapper mapper, ILogger<CreditApplicationAuthorizationDetailsService> logger, IAccountAuthorizationsService accountAuthorizationsService)
    {
        _mapper = mapper;
        _creditApplicationAuthorizationDetailsRepository = creditApplicationAuthorizationDetailsRepository;
        _logger = logger;
        _accountAuthorizationsService = accountAuthorizationsService;
    }

    public async Task<CreditApplicationAuthorizationDetails?> GetByCreditApplicationId(string creditApplicationId, CancellationToken ct)
    {
        var documents = await _creditApplicationAuthorizationDetailsRepository.GetByCreditApplicationId(creditApplicationId, ct);
        var document = documents.FirstOrDefault();

        return _mapper.Map<CreditApplicationAuthorizationDetails>(document);
    }

    public Task UpdateByCreditApplicationId(UpdateCreditApplicationAuthorizationDetails model, CancellationToken ct)
    {
        var updateDocument = _mapper.Map<CreditApplicationAuthorizationDetailsDocument>(model);
        return _creditApplicationAuthorizationDetailsRepository.UpdateByCreditApplicationId(updateDocument, ct);
    }

    public Task<CreditApplicationAuthorizationDetailsDocument> Create(CreditApplicationAuthorizationDetails model, CancellationToken ct)
    {
        var document = _mapper.Map<CreditApplicationAuthorizationDetailsDocument>(model);
        return _creditApplicationAuthorizationDetailsRepository.Add(document, ct);
    }

    public async Task CreateOrUpdateAccountAuthDetailsSnapshot(UpsertAccountAuthDetailsSnapshotModel upsertAccountAuthDetailsSnapshot, CancellationToken ct)
    {
        var creditApplicationId = upsertAccountAuthDetailsSnapshot.CreditApplicationId;
        _logger.LogInformation("Credit app {id}: getting account auth details for snapshot ", creditApplicationId);

        var accountAuthorizationDetailsCollection =
            await _accountAuthorizationsService.GetAllByFilters(null, upsertAccountAuthDetailsSnapshot.CompanyId, upsertAccountAuthDetailsSnapshot.EinHash, null, ct);
        var accountAuthorizationDetails = accountAuthorizationDetailsCollection.FirstOrDefault();

        _logger.LogInformation("Credit app {id}: finished getting account auth details for snapshot: {accountAuthDetailsId}. Checking if snapshot already exists",
            creditApplicationId, accountAuthorizationDetails?.Id);

        var existingSnapshot = await GetByCreditApplicationId(creditApplicationId, ct);
        if (existingSnapshot != null)
        {
            _logger.LogInformation("Found existing snapshot {snapshotId} for credit application {id}. Started updating snapshot in database",
                existingSnapshot.Id, creditApplicationId);

            await UpdateByCreditApplicationId(
                new UpdateCreditApplicationAuthorizationDetails()
                {
                    CreditApplicationId = creditApplicationId,
                    AccountAuthorizationDetailsSnapshot = accountAuthorizationDetails
                }, ct);
        }
        else
        {
            _logger.LogInformation("Snapshot for credit application {id} does not exist yet. Started creating snapshot in database", creditApplicationId);
            await Create(new CreditApplicationAuthorizationDetails()
            {
                CreditApplicationId = creditApplicationId,
                AccountAuthorizationDetailsSnapshot = accountAuthorizationDetails
            }, ct);
        }
    }
}