﻿using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

public interface ICreditApplicationAuthorizationDetailsRepository : IGenericRepository<CreditApplicationAuthorizationDetailsDocument>
{
    Task<IEnumerable<CreditApplicationAuthorizationDetailsDocument>> GetByCreditApplicationId(string creditApplicationId, CancellationToken ct);

    Task<CreditApplicationAuthorizationDetailsDocument> UpdateByCreditApplicationId(
        CreditApplicationAuthorizationDetailsDocument updateDocument, CancellationToken ct);
}