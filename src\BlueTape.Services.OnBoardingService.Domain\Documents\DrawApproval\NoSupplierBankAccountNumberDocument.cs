﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class NoSupplierBankAccountNumberDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("display")]
    public string? Display { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("cipher")]
    public string? Cipher { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("hash")]
    public string? Hash { get; set; }
}