﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.ParsedDraft;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;
public class ParsedDraftRepository : GenericRepository<ParsedDraftDocument>, IParsedDraftRepository
{
    public ParsedDraftRepository(IObsMongoDBContext context, ILogger<GenericRepository<ParsedDraftDocument>> logger) : base(context, logger)
    {
    }

    public async Task<ParsedDraftDocument?> GetByCompanyId(string companyId, CancellationToken ct)
    {
        var document = Collection.Find(x => x.CompanyId == companyId);
        var result = await document.FirstOrDefaultAsync(ct);

        return result;
    }

    public async Task<ParsedDraftDocument?> GetByDraftId(string draftId, CancellationToken ct)
    {
        var document = Collection.Find(x => x.DraftId == draftId);
        var result = await document.FirstOrDefaultAsync(ct);

        return result;
    }
}
