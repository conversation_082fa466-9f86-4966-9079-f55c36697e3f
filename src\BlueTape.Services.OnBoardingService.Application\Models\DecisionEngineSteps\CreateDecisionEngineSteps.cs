﻿namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;

public class CreateDecisionEngineSteps
{
    public string? ExecutionId { get; set; }

    public string? CreditApplicationId { get; set; }

    public string? ExecutionType { get; set; }

    public string? DrawApprovalId { get; set; }

    public string? AccountAuthorizationDetailsId { get; set; }

    public string? Status { get; set; }
    
    public string? Step { get; set; }
    
    public string? PreviousStep { get; set; }

    public string? PolicyVersion { get; set; }

    public IEnumerable<DecisionEngineStepResult>? Results { get; set; }

    public IEnumerable<DecisionEngineStepThresholdModel>? Thresholds { get; set; }
    
    public string? CreatedBy { get; set; }
}
