﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
public class OwnerDetailsBaseDocument
{

    [BsonElement("id")]
    public string Id { get; set; } = string.Empty;

    [BsonElement("identifier")]
    public string Identifier { get; set; } = string.Empty;

    [BsonElement("percentOwned")]
    public decimal PercentOwned { get; set; }

    [BsonElement("phone")]
    public string? Phone { get; set; }

    [BsonElement("email")]
    public string? Email { get; set; }

    [BsonElement("address")]
    public AddressDocument Address { get; set; } = new();

    [BsonElement("isPrincipal")]
    public bool IsPrincipal { get; set; }

    [BsonElement("InquiriesDuringLast6Months")]
    public string? InquiriesDuringLast6Months { get; set; }
}
