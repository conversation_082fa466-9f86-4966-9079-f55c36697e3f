﻿using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.LS.DTOs.Credit;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using CompanyDebtInvestorType = BlueTape.CompanyService.Common.Enums.DebtInvestorType;
using CompanyUpdateModelFromOnboarding = BlueTape.Services.OnBoardingService.Application.Models.Company.UpdateCompanyModel;

namespace BlueTape.Services.OnBoardingService.Application.Services
{
    public class CreditApplicationService : ICreditApplicationService
    {
        private readonly ICreditApplicationRepository _creditApplicationRepository;
        private readonly ICreditApplicationAuthorizationDetailsService _creditApplicationAuthorizationDetailsService;
        private readonly IAccountStatusService _accountStatusService;
        private readonly IDateProvider _dateProvider;
        private readonly IMapper _mapper;
        private readonly ICompanyService _companyService;
        private readonly ICreditApplicationNotesService _creditApplicationNotesService;
        private readonly ICreditApplicationSyncService _creditApplicationSyncService;
        private readonly IEnumerable<ChangeCreditApplicationStatusStrategy> _changeCreditApplicationStatusStrategies;
        private readonly ILogger<CreditApplicationService> _logger;
        private readonly ICompanyExternalService _companyExternalService;
        private readonly ILoanExternalService _loanExternalService;

        public CreditApplicationService(
            ICreditApplicationRepository creditApplicationRepository,
            ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService,
            IAccountStatusService accountStatusService,
            IDateProvider dateProvider,
            IMapper mapper,
            ICompanyService companyService,
            ICreditApplicationNotesService creditApplicationNotesService,
            ICreditApplicationSyncService creditApplicationSyncService,
            IEnumerable<ChangeCreditApplicationStatusStrategy> changeCreditApplicationStatusStrategies,
            ICompanyExternalService companyExternalService,
            ILoanExternalService loanExternalService,
            ILogger<CreditApplicationService> logger)
        {
            _creditApplicationRepository = creditApplicationRepository;
            _creditApplicationAuthorizationDetailsService = creditApplicationAuthorizationDetailsService;
            _accountStatusService = accountStatusService;
            _dateProvider = dateProvider;
            _mapper = mapper;
            _companyService = companyService;
            _creditApplicationNotesService = creditApplicationNotesService;
            _creditApplicationSyncService = creditApplicationSyncService;
            _changeCreditApplicationStatusStrategies = changeCreditApplicationStatusStrategies;
            _companyExternalService = companyExternalService;
            _loanExternalService = loanExternalService;
            _logger = logger;
        }

        public async Task<IEnumerable<CreditApplication>> GetAllByFilters(GetCreditApplicationQuery query, CancellationToken ct)
        {
            var creditApplicationDocuments = await _creditApplicationRepository.GetAllByFilters(query, ct);

            var creditApplications = _mapper.Map<IEnumerable<CreditApplication>>(creditApplicationDocuments);

            await FillGetPaidApplicationsDataForArAdvanceApplications(creditApplications, ct);

            return creditApplications;
        }

        public async Task<IEnumerable<CreditApplication>> GetByIds(string[] ids, CancellationToken ct)
        {
            var creditApplicationDocuments = await _creditApplicationRepository.GetByIds(ids, ct);
            var creditApplications = _mapper.Map<IEnumerable<CreditApplication>>(creditApplicationDocuments);
            await FillGetPaidApplicationsDataForArAdvanceApplications(creditApplications, ct);

            return creditApplications;
        }

        public async Task<IEnumerable<CreditApplication>> GetByEinHashes(string[] einHashes, CancellationToken ct)
        {
            var creditApplicationDocuments = await _creditApplicationRepository.GetByEinHashes(einHashes, ct);
            var creditApplications = _mapper.Map<IEnumerable<CreditApplication>>(creditApplicationDocuments);
            await FillGetPaidApplicationsDataForArAdvanceApplications(creditApplications, ct);

            return creditApplications;
        }

        public async Task<GetQueryWithPaginationResultModel<CreditApplication>> GetAllByFiltersWithPagination(GetCreditApplicationQueryWithPagination query, CancellationToken ct)
        {
            var creditApplicationDocuments = await _creditApplicationRepository.GetAllByFiltersWithPagination(query, ct);

            var credits = new List<CreditDto?>();

            if (query.CreditDetails == true)
            {
                var creditFilterDto = new CreditFilterDto()
                {
                    CreditApplicationId = creditApplicationDocuments.Result.Select(x => x.Id).ToArray(),
                    Detailed = true,
                };

                credits = (await _loanExternalService.GetCreditsByFilters(creditFilterDto, ct)).ToList();
            }

            var creditApplications = _mapper.Map<GetQueryWithPaginationResultModel<CreditApplication>>(creditApplicationDocuments);


            foreach (var credit in creditApplications.Result)
            {
                credit.CreditDetails = credits.FirstOrDefault(x => x.CreditApplicationId == credit.Id)?.CreditDetails;
            }

            await FillGetPaidApplicationsDataForArAdvanceApplications(creditApplications.Result, ct);

            return creditApplications;
        }

        public async Task<IEnumerable<CreditApplication>> GetAll(CancellationToken ct)
        {
            var creditApplicationDocuments = await _creditApplicationRepository.GetAll(ct);
            var creditApplications = _mapper.Map<IEnumerable<CreditApplication>>(creditApplicationDocuments);
            await FillGetPaidApplicationsDataForArAdvanceApplications(creditApplications, ct);

            return creditApplications;
        }

        public async Task<CreditApplication> GetById(string id, CancellationToken ct)
        {
            var creditApplicationDocument = await _creditApplicationRepository.GetById(id, ct);
            var creditApplication = _mapper.Map<CreditApplication>(creditApplicationDocument);

            await FillGetPaidApplicationsDataForArAdvanceApplications(new[] { creditApplication }, ct);

            return creditApplication;
        }

        public async Task<IEnumerable<CreditApplication>> GetByCompanyIds(
            GetCreditApplicationsByCompanyIdsQuery query,
            CancellationToken ct)
        {
            var creditApplicationDocuments = await _creditApplicationRepository.GetByCompanyIds(query, ct);
            var creditApplications = _mapper.Map<IEnumerable<CreditApplication>>(creditApplicationDocuments);
            await FillGetPaidApplicationsDataForArAdvanceApplications(creditApplications, ct);

            return creditApplications;
        }

        public async Task<IEnumerable<CreditApplication>> CreateRange(IEnumerable<CreateCreditApplication> createCreditApplicationCollection, CancellationToken ct)
        {
            _logger.LogInformation("Started creation of credit application list");
            var documents = new List<CreditApplicationDocument>();
            foreach (var createCreditApplication in createCreditApplicationCollection)
            {
                var creditApplicationDocument = _mapper.Map<CreditApplicationDocument>(createCreditApplication);
                if (createCreditApplication.Status is null) creditApplicationDocument.Status = CreditApplicationStatus.New.ToString().ToLower();
                if (createCreditApplication.PurchaseType is not null) creditApplicationDocument.PurchaseTypeOption = createCreditApplication.PurchaseType;

                documents.Add(creditApplicationDocument);
            }

            var creditApplications = await _creditApplicationRepository.AddRange(documents, ct);
            _logger.LogInformation("Finished creation of credit applications");

            return _mapper.Map<IEnumerable<CreditApplication>>(creditApplications);
        }

        public async Task<CreditApplication> Create(CreateCreditApplication createCreditApplication, CancellationToken ct, bool shouldIgnoreSync = false)
        {
            _logger.LogInformation("Started creation of credit application");

            var creditApplicationDocument = _mapper.Map<CreditApplicationDocument>(createCreditApplication);
            if (createCreditApplication.Status is null) creditApplicationDocument.Status = CreditApplicationStatus.New.ToString().ToLower();
            if (createCreditApplication.PurchaseType is not null) creditApplicationDocument.PurchaseTypeOption = createCreditApplication.PurchaseType;

            _mapper.Map(creditApplicationDocument, creditApplicationDocument);
            var creditApplication = await _creditApplicationRepository.Add(creditApplicationDocument, ct);
            _logger.LogInformation("Finished creation of credit application {id}. Sending message to compatibility", creditApplication.Id);

            if (ShouldSyncCreditApplication(createCreditApplication, shouldIgnoreSync))
            {
                await _creditApplicationSyncService.SyncApplicableCreditApplication(creditApplicationDocument, ct);
            }

            return _mapper.Map<CreditApplication>(creditApplication);
        }

        public async Task<CreditApplication> Update(UpdateCreditApplication updateCreditApplication, CancellationToken ct)
        {
            _logger.LogInformation("Started update of credit application {id}", updateCreditApplication.Id);
            var currentDateTime = _dateProvider.CurrentDateTime;

            var newStatus = updateCreditApplication.NewStatus;
            if (CreditApplicationStatus.Approved.IsEnum(newStatus) ||
                CreditApplicationStatus.Rejected.IsEnum(newStatus) ||
                CreditApplicationStatus.Canceled.IsEnum(newStatus) ||
                CreditApplicationStatus.SentBack.IsEnum(newStatus))
            {
                throw new ValidationException($"Cannot move credit application to status: {newStatus} as this status has to be set by admin only.");
            }

            var creditApplicationDocument = await _creditApplicationRepository.GetById(updateCreditApplication.Id!, ct);
            var currentCreditApplicationStatus = creditApplicationDocument.Status;

            _mapper.Map(updateCreditApplication, creditApplicationDocument);
            if (IsStatusChanged(currentCreditApplicationStatus, updateCreditApplication.NewStatus))
            {
                creditApplicationDocument.StatusNote = updateCreditApplication.StatusNote;
                creditApplicationDocument.LastStatusChangedAt = currentDateTime;
                creditApplicationDocument.LastStatusChangedBy = creditApplicationDocument.UpdatedBy;
            }

            _logger.LogInformation("Started update of credit application date {id}. New Status: {status}",
                updateCreditApplication.Id, updateCreditApplication.NewStatus);

            var creditApplication = await _creditApplicationRepository.Update(creditApplicationDocument, ct);
            _logger.LogInformation("Finished update of credit application {id}. Started processing account authorization snapshot",
                updateCreditApplication.Id);

            await _accountStatusService.ChangeAccountStatus(creditApplicationDocument, ct);
            await CreateOrUpdateAccountAuthDetailsSnapshot(creditApplication, ct);

            _logger.LogInformation("Finished processing account authorization snapshot for credit application {id}",
                updateCreditApplication.Id);

            if (string.IsNullOrEmpty(updateCreditApplication.NewStatus))
                return _mapper.Map<CreditApplication>(creditApplication);

            await _creditApplicationSyncService.SyncApplicableCreditApplication(creditApplicationDocument, ct);
            return _mapper.Map<CreditApplication>(creditApplication);
        }

        public async Task<CreditApplication> PatchAdmin(PatchCreditApplicationAdminModel patchModel, CancellationToken ct)
        {
            _logger.LogInformation("Started patch of credit application {Id} (Admin request)", patchModel.Id!);

            var creditApplicationDocument = await _creditApplicationRepository.GetById(patchModel.Id!, ct);
            if (IsApprovedCreditLimitChanged(patchModel, creditApplicationDocument))
            {
                return await ProcessCreditApplicationLimitUpdates(patchModel, creditApplicationDocument, ct);
            }

            _mapper.Map(patchModel, creditApplicationDocument);
            if (patchModel.DebtInvestor is not null)
            {
                creditApplicationDocument.MerchantSettings ??= new MerchantSettingsDocument();
                creditApplicationDocument.MerchantSettings.DebtInvestor = patchModel.DebtInvestor;
            }
            var result = await _creditApplicationRepository.Update(creditApplicationDocument, ct);

            if (CreditApplicationType.LineOfCredit.IsEnum(creditApplicationDocument.Type))
            {
                await _companyService.PatchCompanyCredit(result.CompanyId!, new CompanyUpdateModelFromOnboarding
                {
                    Limit = Convert.ToInt32(patchModel.ApprovedCreditLimit),
                    PurchaseType = patchModel.PurchaseTypeOption?.ToLower()
                }, ct);
            }

            await UpdateCompanySettingsBasedOnCreditApplicationType(patchModel.DebtInvestor,
                creditApplicationDocument, patchModel.UpdatedBy, ct);

            _logger.LogInformation("End patch of credit application {Id} (Admin request)", patchModel.Id!);
            return _mapper.Map<CreditApplication>(result);
        }

        private async Task UpdateCompanySettingsBasedOnCreditApplicationType(
            DebtInvestorType? debtInvestorType,
            CreditApplicationDocument creditApplicationDocument,
            string updatedBy,
            CancellationToken ct)
        {
            if ((CreditApplicationType.GetPaid.IsEnum(creditApplicationDocument.Type) ||
                 CreditApplicationType.ARAdvance.IsEnum(creditApplicationDocument.Type)) &&
                debtInvestorType is not null && creditApplicationDocument.CompanyId is not null)
            {
                var updateModel = new UpdateCompanyModel
                {
                    Settings = new CompanySettingsModel()
                };
                var companyDebtInvestor = (CompanyDebtInvestorType)debtInvestorType.Value;
                if (CreditApplicationType.GetPaid.IsEnum(creditApplicationDocument.Type))
                {
                    updateModel.Settings.DefaultDebtInvestorTradeCredit = companyDebtInvestor;
                }
                else if (CreditApplicationType.ARAdvance.IsEnum(creditApplicationDocument.Type))
                {
                    updateModel.Settings.ARAdvance = new ArAdvanceModel { DefaultDebtInvestor = companyDebtInvestor };
                }

                await _companyExternalService.UpdateCompany(creditApplicationDocument.CompanyId, updatedBy,
                    updateModel, ct);
            }
        }

        private async Task UpdateCompanySettings(
            CreditApplicationDocument creditApplicationDocument,
            ReviewCreditApplicationDto reviewCreditApplication,
            string userId, CancellationToken ct)
        {
            if (creditApplicationDocument.CompanyId is null)
            {
                _logger.LogWarning("CompanyId is null for CreditApplicationDocument");
                return;
            }
            var companyModel = new UpdateCompanyModel
            {
                Settings = new CompanySettingsModel
                {
                    InHouseCredit = new CompanyInHouseCreditModel
                    {
                        IsAutoPayRequired = reviewCreditApplication.IsInHouseCreditAutoPayRequired
                    }
                }
            };

            if (CreditApplicationType.LineOfCredit.IsEnum(creditApplicationDocument.Type))
            {
                if (reviewCreditApplication.IsSecured.HasValue)
                {
                    creditApplicationDocument.IsSecured = reviewCreditApplication.IsSecured.Value;
                    creditApplicationDocument.DepositAmount = reviewCreditApplication.DepositAmount;

                    companyModel.Settings.DepositDetails = new DepositDetailsModel
                    {
                        DepositAmount = reviewCreditApplication.DepositAmount,
                        IsSecured = reviewCreditApplication.IsSecured.Value,
                    };
                }
                if (reviewCreditApplication.ApprovedCreditLimit is > 0)
                {
                    companyModel.Credit = new CompanyCreditModel
                    {
                        Limit = (double?)reviewCreditApplication.ApprovedCreditLimit
                    };
                }
            }

            await _companyExternalService.UpdateCompany(creditApplicationDocument.CompanyId!, userId, companyModel, ct);
        }

        private async Task<CreditApplication> ProcessCreditApplicationLimitUpdates(
            PatchCreditApplicationAdminModel patchModel,
            CreditApplicationDocument creditApplicationDocument,
            CancellationToken ct)
        {
            var caption = creditApplicationDocument.Type.ToCreditApplicationCaption();

            if (string.Equals(
                    creditApplicationDocument.Type,
                    CreditApplicationType.LineOfCredit.ToString(),
                    StringComparison.OrdinalIgnoreCase))
            {
                var updateModel = new UpdateCompanyModel
                {
                    Credit = new CompanyCreditModel
                    {
                        Limit = (double?)patchModel.ApprovedCreditLimit
                    }
                };

                if (creditApplicationDocument.CompanyId is null)
                    throw new ValidationException("Company is not set for credit application");
                await _companyExternalService.UpdateCompany(creditApplicationDocument.CompanyId, patchModel.UpdatedBy, updateModel, ct);
            }
            else if (string.Equals(
                    creditApplicationDocument.Type,
                    CreditApplicationType.InHouseCredit.ToString(),
                    StringComparison.OrdinalIgnoreCase))
            {
                var updateModel = new UpdateCustomerModel
                {
                    Limit = (double)patchModel.ApprovedCreditLimit!.Value
                };

                if (creditApplicationDocument.CustomerAccount is null)
                    throw new ValidationException("Customer account is not set for credit application");
                await _companyExternalService.SetCustomerIhcSettings(creditApplicationDocument.CustomerAccount.Id, updateModel, ct);
            }
            else if (string.Equals(
                         creditApplicationDocument.Type,
                         CreditApplicationType.ARAdvance.ToString(),
                         StringComparison.OrdinalIgnoreCase))
            {
                var updateModel = new UpdateCompanyModel
                {
                    Settings = new CompanySettingsModel
                    {
                        ARAdvance = new ArAdvanceModel
                        {
                            MerchantLimit = patchModel.ApprovedCreditLimit!.Value,
                            DefaultDebtInvestor = (CompanyDebtInvestorType?)(int?)patchModel.DebtInvestor ?? CompanyDebtInvestorType.Arcadia
                        }
                    }
                };
 
                if (creditApplicationDocument.CompanyId is null)
                    throw new ValidationException("Company is not set for credit application");
                await _companyExternalService.UpdateCompany(creditApplicationDocument.CompanyId, patchModel.UpdatedBy, updateModel, ct);
            }

            var noteText = NoteExtensions.FormatLimitChange(
                creditApplicationDocument.ApprovedCreditLimit,
                patchModel.ApprovedCreditLimit!.Value
            );

            var finalNoteText = noteText;

            if (!string.IsNullOrWhiteSpace(patchModel.Note))
                finalNoteText = $"{noteText}{Environment.NewLine}{patchModel.Note}";

            var note = new CreateCreditApplicationNote
            {
                Caption = caption,
                CreatedBy = patchModel.UpdatedBy,
                CreatedAt = _dateProvider.CurrentDateTime,
                CreditApplicationId = creditApplicationDocument.Id,
                Note = finalNoteText
            };

            await _creditApplicationNotesService.Add(note, ct);
            return _mapper.Map<CreditApplication>(creditApplicationDocument);
        }

        private static bool IsApprovedCreditLimitChanged(PatchCreditApplicationAdminModel patchModel, CreditApplicationDocument creditApplicationDocument)
        {
            return patchModel.ApprovedCreditLimit.HasValue && patchModel.ApprovedCreditLimit.Value != creditApplicationDocument.ApprovedCreditLimit;
        }

        public async Task<CreditApplication> Review(string creditApplicationId, string userId,
            ReviewCreditApplicationDto reviewCreditApplication, CancellationToken ct)
        {
            _logger.LogInformation("Updating credit application state: {CreditApplicationId}", creditApplicationId);
            if (string.IsNullOrEmpty(reviewCreditApplication.NewStatus))
                throw new ValidationException("The new credit application status must be provided");

            var creditApplicationDocument = await _creditApplicationRepository.GetById(creditApplicationId, ct);
            if (creditApplicationDocument == null)
                throw new ValidationException("The credit application was not found");
            if (string.IsNullOrEmpty(creditApplicationDocument.CompanyId))
                throw new ValidationException("The credit application doesn't belong to a company");
            if (!CreditApplicationStatus.Processed.IsEnum(creditApplicationDocument.Status))
                throw new ValidationException("The credit application is not in the correct state to be reviewed");
            if (reviewCreditApplication.IsSecured.HasValue && !CreditApplicationType.LineOfCredit.IsEnum(creditApplicationDocument.Type))
                throw new ValidationException($"The credit application with type {creditApplicationDocument.Type} cannot be secured");

            creditApplicationDocument.UpdatedAt = _dateProvider.CurrentDateTime;
            creditApplicationDocument.UpdatedBy = userId;
            creditApplicationDocument.LastStatusChangedAt = _dateProvider.CurrentDateTime;
            creditApplicationDocument.LastStatusChangedBy = userId;

            await UpdateCompanySettings(creditApplicationDocument, reviewCreditApplication, userId, ct);

            var changeStatusStrategy = GetChangeStatusStrategy(reviewCreditApplication.NewStatus);

            var result = await changeStatusStrategy.ChangeStatus(creditApplicationDocument, reviewCreditApplication, userId, ct);

            return result;
        }

        public async Task<IReadOnlyList<LightCreditApplication>> GetLightCreditApps(string[] companyIds, string status, CancellationToken ct)
        {
            var result = _mapper.Map<IReadOnlyList<LightCreditApplication>>(await _creditApplicationRepository.GetLightCreditApplications(companyIds, status, ct));
            return result;
        }

        private ChangeCreditApplicationStatusStrategy GetChangeStatusStrategy(string newStatus)
        {
            var changeStatusStrategy = _changeCreditApplicationStatusStrategies
                                           .FirstOrDefault(x => x.IsApplicable(newStatus))
                                       ?? throw new ValidationException(
                                           $"Cannot change credit application status to {newStatus}");
            return changeStatusStrategy;
        }

        private async Task CreateOrUpdateAccountAuthDetailsSnapshot(CreditApplicationDocument creditApplication, CancellationToken ct)
        {
            if (string.IsNullOrEmpty(creditApplication.AutomatedDecisionResult))
            {
                _logger.LogWarning("Credit app {id} update is not related to step execution result." +
                                       " Abort processing snapshot ", creditApplication.Id);
                return;
            }

            _logger.LogInformation("Credit app {id}: getting account auth details for snapshot ", creditApplication.Id);

            await _creditApplicationAuthorizationDetailsService.CreateOrUpdateAccountAuthDetailsSnapshot(new UpsertAccountAuthDetailsSnapshotModel()
            {
                CompanyId = creditApplication.CompanyId ?? string.Empty,
                CreditApplicationId = creditApplication.Id,
                EinHash = creditApplication.EinHash ?? string.Empty
            }, ct);
        }

        private static bool ShouldSyncCreditApplication(CreateCreditApplication createCreditApplication, bool shouldIgnoreSync)
        {
            return !shouldIgnoreSync && (createCreditApplication.IsNotNotifyUser is null || !createCreditApplication.IsNotNotifyUser.Value);
        }

        private static bool IsStatusChanged(string? currentStatus, string? newStatus)
        {
            return !string.IsNullOrEmpty(newStatus) && !string.Equals(currentStatus, newStatus, StringComparison.InvariantCultureIgnoreCase);
        }

        private async Task FillGetPaidApplicationsDataForArAdvanceApplications(IEnumerable<CreditApplication?> creditApplications, CancellationToken ctx)
        {
            var arAdvanceCreditApplications = creditApplications.Where(x => x?.Type == CreditApplicationType.ARAdvance);
            var arAdvanceApplicationsIds = arAdvanceCreditApplications.Select(a => a?.Id).ToArray();
            var approvedGetPaidApplications = await _creditApplicationRepository.GetAllByFilters(
                new GetCreditApplicationQuery()
                {
                    ArAdvanceApplicationIds = arAdvanceApplicationsIds,
                    Type = [CreditApplicationType.GetPaid.ToString()],
                    Status = [CreditApplicationStatus.Approved.ToString()]
                }, ctx);

            foreach (var arAdvanceCreditApplication in arAdvanceCreditApplications)
            {
                var getPaidApplication = approvedGetPaidApplications.FirstOrDefault(x => x.ArAdvanceApplicationId == arAdvanceCreditApplication.Id);

                arAdvanceCreditApplication.GetPaidApplicationId = getPaidApplication?.Id;
                arAdvanceCreditApplication.GetPaidCreatedAt = getPaidApplication?.CreatedAt;
                arAdvanceCreditApplication.GetPaidApplicationDate = getPaidApplication?.ApplicationDate;
                arAdvanceCreditApplication.GetPaidApprovalDate = getPaidApplication?.ApprovedAt;
                arAdvanceCreditApplication.GetPaidApprovedBy = getPaidApplication?.ApprovedBy;
            }
        }
    }
}