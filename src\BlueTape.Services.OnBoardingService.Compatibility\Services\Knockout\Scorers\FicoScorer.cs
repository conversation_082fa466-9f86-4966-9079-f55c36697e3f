﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class FicoScorer : IScoring
{
    public const int OwnerScoreThreshold = 620;

    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        var result = new List<OwnerScore>();

        if (experian?.OwnersData != null && experian.OwnersData.Any())
        {
            foreach (var item in experian.OwnersData)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                if (item.Score != null)
                    ownerScore.Scores?.Add(Calculate("FICO", decision, item.Score));

                result.Add(ownerScore);
            }
        }

        // TODO Not full conversion
        return EvaluateScores(result);
    }

    private Score Calculate(string name, LoanDecisionData? decision, string score)
    {
        return new Score()
        {
            Name = name,
            Value = score,
            Pass = EvaluateDecision(decision?.Decision, score)
        };
    }

    private ScoringResult EvaluateDecision(Domain.Documents.LoanApplication.Decision? decision, string score)
    {
        if ((decision?.LoanRevenue ?? 0) > 5000000)
        {
            return ScoringResult.Pass;
        }
        else if (double.TryParse(score, out double numericScore) && numericScore >= OwnerScoreThreshold)
        {
            return ScoringResult.Pass;
        }
        else if (score == "LOCKED")
        {
            return ScoringResult.Review;
        }
        else
        {
            return ScoringResult.Reject;
        }
    }

    public static List<OwnerScore> EvaluateScores(List<OwnerScore> scores)
    {
        if (scores.Count > 0)
        {
            if (scores.Any(item => item.Owner == null))
            {
                return [new OwnerScore
                {
                    Scores = new List<Score>()
                    {
                        new()
                        {
                            Name = scores[0].Scores?[0].Name,
                            Value = scores[0].Scores?[0].Value,
                            Pass = scores[0].Scores![0].Pass
                        }

                    }

                }];
            }
            else
            {
                return scores;
            }
        }
        else
        {
            return [new OwnerScore
            {
                Scores = new List<Score>()
                {
                    new()
                    {
                        Name = "FICO",
                        Value = null,
                        Pass = ScoringResult.Review
                    }
                }
            }];
        }
    }
}
