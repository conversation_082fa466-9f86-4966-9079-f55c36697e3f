﻿using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Cipher;

namespace BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;

public class ParsedDraftModel
{
    public string Id { get; set; } = null!;

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string DraftId { get; set; } = null!;

    public AddressModel? BusinessAddress { get; set; }

    public BusinessNameModel? BusinessName { get; set; }

    public string? BusinessPhone { get; set; }

    public string? BusinessStartDate { get; set; }

    public CipherModel? Ein { get; set; }

    public string DecryptedEin { get; set; } = null!;

    public List<CoOwnerModel> CoOwnersInfo { get; set; } = new();

    public string? OwnerFirstName { get; set; }

    public string? OwnerLastName { get; set; }

    public AddressModel? OwnerAddress { get; set; }

    public string? OwnerPhone { get; set; }

    public string? OwnerEmail { get; set; }

    public string? OwnerBirthDate { get; set; }

    public CipherModel? OwnerSsn { get; set; }

    public BankDetailsModel? BankDetails { get; set; }

    public decimal? Revenue { get; set; }

    public decimal? Debt { get; set; }

    public decimal? RequestedAmount { get; set; }

    public string? CompanyId { get; set; }

    public string? OwnerId { get; set; }

    public bool? IsPrincipal { get; set; }

    public int OwnerPercentOwned { get; set; }

    public bool? ArAdvanceRequested { get; set; }
}
