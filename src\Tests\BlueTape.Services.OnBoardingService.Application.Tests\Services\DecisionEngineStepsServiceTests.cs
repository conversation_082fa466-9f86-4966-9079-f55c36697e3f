﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DecisionEngineSteps;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using System.Linq.Expressions;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class DecisionEngineStepsServiceTests
{
    private readonly IDecisionEngineStepsService _decisionEngineStepsService;
    private readonly Mock<IDecisionEngineStepsRepository> _decisionEngineStepsRepository = new();
    private readonly Mock<ICreditApplicationNotesService> _creditApplicationNotesServiceMock = new();
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepository = new();
    private readonly Mock<IAccountAuthorizationsService> _accountAuthorizationsServiceMock = new();
    private readonly Mock<IDrawApprovalRepository> _drawApprovalRepository = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();

    public DecisionEngineStepsServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        _decisionEngineStepsService =
            new DecisionEngineStepsService(_decisionEngineStepsRepository.Object, _creditApplicationRepository.Object, new Mapper(mapperConfig), _creditApplicationNotesServiceMock.Object,
            _accountAuthorizationsServiceMock.Object, _dateProviderMock.Object, _drawApprovalRepository.Object);
    }

    [Fact]
    public async Task GetLatestByCompanyIds_ReturnsExpectedDictionary_WhenDataExists()
    {
        var companyIds = new[] { "Company1", "Company2" };

        var mockCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1" },
            new() { Id = "CreditApp2", CompanyId = "Company2" }
        };

        _creditApplicationRepository
            .Setup(repo => repo.GetLightCreditApplications(companyIds, default))
            .ReturnsAsync(mockCreditApplications);

        var mockStepsDocuments = new List<DecisionEngineStepsDocument>
        {
            new() { CreditApplicationId = "CreditApp1", Step = "Step1", UpdatedAt = DateTime.Now.AddHours(-1) },
            new() { CreditApplicationId = "CreditApp2", Step = "Step2", UpdatedAt = DateTime.Now }
        };

        _decisionEngineStepsRepository
            .Setup(repo => repo.GetByCreditApplicationIds(It.IsAny<string[]>(), default))
            .ReturnsAsync(mockStepsDocuments);

        var result = await _decisionEngineStepsService.GetLatestByCompanyIds(companyIds, default);

        result.ShouldNotBeNull();
        result.Count.ShouldBe(2);
        Assert.Contains("Company1", result.Keys);
        Assert.Contains("Company2", result.Keys);
        Assert.Single(result["Company1"]);
        Assert.Single(result["Company2"]);
    }

    [Fact]
    public async Task GetLatestByCompanyIds_ReturnsEmptyDictionary_WhenNoCompaniesMatch()
    {
        var companyIds = new[] { "NonExistentCompany" };
        var cancellationToken = CancellationToken.None;

        _creditApplicationRepository
            .Setup(repo => repo.GetLightCreditApplications(companyIds, default))
            .ReturnsAsync(new List<LightCreditApplicationDocument>());

        var result = await _decisionEngineStepsService.GetLatestByCompanyIds(companyIds, cancellationToken);

        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task GetLatestByCompanyIds_FiltersToLatestSteps()
    {
        var companyIds = new[] { "Company1" };

        var mockCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1" }
        };

        _creditApplicationRepository
            .Setup(repo => repo.GetLightCreditApplications(companyIds, default))
            .ReturnsAsync(mockCreditApplications);

        var currentDate = DateTime.Now;
        var mockStepsDocuments = new List<DecisionEngineStepsDocument>
        {
            new() { CreditApplicationId = "CreditApp1", Step = "Step1", UpdatedAt = currentDate.AddMinutes(-10) },
            new() { CreditApplicationId = "CreditApp1", Step = "Step1", UpdatedAt = currentDate.AddMinutes(-5) },
            new() { CreditApplicationId = "CreditApp1", Step = "Step2", UpdatedAt = currentDate }
        };

        _decisionEngineStepsRepository
            .Setup(repo => repo.GetByCreditApplicationIds(It.IsAny<string[]>(), default))
            .ReturnsAsync(mockStepsDocuments);

        var result = await _decisionEngineStepsService.GetLatestByCompanyIds(companyIds, default);

        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result["Company1"].Count().ShouldBe(2);
        Assert.Contains(result["Company1"], step => step.Step == "Step1" && step.UpdatedAt == currentDate.AddMinutes(-5));
        Assert.Contains(result["Company1"], step => step.Step == "Step2");
    }

    [Fact]
    public async Task Create_ValidCreditApplicationStepModel_ReturnsCreatedModel()
    {
        var createModel = new CreateDecisionEngineSteps()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Step = "step"
        };

        var createdModel = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetByCreditApplicationId(createModel.CreditApplicationId, default))
            .ReturnsAsync(new DecisionEngineStepsDocument[] { });
        _decisionEngineStepsRepository.Setup(x => x.Add(It.IsAny<DecisionEngineStepsDocument>(), default))
            .ReturnsAsync(createdModel);

        var result = await _decisionEngineStepsService.Create(createModel, default);
        _decisionEngineStepsRepository.Verify(x => x.GetByCreditApplicationId(createModel.CreditApplicationId, default), Times.Once);
        _decisionEngineStepsRepository.Verify(x => x.GetByDrawApprovalId(It.IsAny<string>(), default), Times.Never);
        result.CreatedBy.ShouldBeEquivalentTo(createdModel.CreatedBy);
        result.Id.ShouldBeEquivalentTo(createdModel.Id);
        result.PreviousStep.ShouldBeEquivalentTo(createdModel.PreviousStep);
    }

    [Fact]
    public async Task Create_ValidDrawApprovalStepModel_ReturnsCreatedModel()
    {
        var createModel = new CreateDecisionEngineSteps()
        {
            DrawApprovalId = "drawApproval",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Step = "step"
        };

        var createdModel = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            DrawApprovalId = "drawApproval",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.Add(It.IsAny<DecisionEngineStepsDocument>(), default))
            .ReturnsAsync(createdModel);
        _decisionEngineStepsRepository.Setup(x => x.GetByDrawApprovalId(createModel.DrawApprovalId, default))
            .ReturnsAsync(new DecisionEngineStepsDocument[] { });

        var result = await _decisionEngineStepsService.Create(createModel, default);

        _decisionEngineStepsRepository.Verify(x => x.GetByCreditApplicationId(It.IsAny<string>(), default), Times.Never);
        _decisionEngineStepsRepository.Verify(x => x.GetByDrawApprovalId(createModel.DrawApprovalId, default), Times.Once);
        result.CreatedBy.ShouldBeEquivalentTo(createdModel.CreatedBy);
        result.Id.ShouldBeEquivalentTo(createdModel.Id);
        result.PreviousStep.ShouldBeEquivalentTo(createdModel.PreviousStep);
    }

    [Fact]
    public Task Create_BothCreditApplicationAndDrawApprovalInModel_ThrowsValidationException()
    {
        var createModel = new CreateDecisionEngineSteps()
        {
            DrawApprovalId = "drawApproval",
            CreditApplicationId = "creditApplicationId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Step = "step"
        };
        var act = async () => await _decisionEngineStepsService.Create(createModel, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public Task Create_NoCreditApplicationAndDrawApprovalInModel_ThrowsValidationException()
    {
        var createModel = new CreateDecisionEngineSteps()
        {
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Step = "step"
        };
        var act = async () => await _decisionEngineStepsService.Create(createModel, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task Patch_ValidModel_ReturnsCreatedModel()
    {
        var updateModel = new UpdateDecisionEngineSteps()
        {
            Id = "id",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResult>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        var updatedModel = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(updatedModel);
        _decisionEngineStepsRepository.Setup(x => x.Update(It.IsAny<DecisionEngineStepsDocument>(), default))
            .ReturnsAsync(updatedModel);

        var result = await _decisionEngineStepsService.Update(updateModel, default);

        result.CreatedBy.ShouldBeEquivalentTo(updatedModel.CreatedBy);
        result.Id.ShouldBeEquivalentTo(updatedModel.Id);
        result.PreviousStep.ShouldBeEquivalentTo(updatedModel.PreviousStep);
        result.Status.ShouldBe(updatedModel.Status);
        result.Thresholds.ShouldBeEmpty();
    }

    [Fact]
    public async Task Patch_ValidModelWithThresholds_ReturnsCreatedModel()
    {
        var updateModel = new UpdateDecisionEngineSteps()
        {
            Id = "id",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResult>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            },
            Thresholds = new List<DecisionEngineStepThresholdModel>()
            {
                new()
                {
                    Name = "name",
                    HardFail = "256",
                    SoftFail = 25
                }
            }
        };

        var updatedModel = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            },
            Thresholds = new List<DecisionEngineThresholdDocument>()
            {
                new()
                {
                    Name = "name",
                    HardFail = "256",
                    SoftFail = 25
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(updatedModel);
        _decisionEngineStepsRepository.Setup(x => x.Update(It.IsAny<DecisionEngineStepsDocument>(), default))
            .ReturnsAsync(updatedModel);

        var result = await _decisionEngineStepsService.Update(updateModel, default);

        result.CreatedBy.ShouldBeEquivalentTo(updatedModel.CreatedBy);
        result.Id.ShouldBeEquivalentTo(updatedModel.Id);
        result.PreviousStep.ShouldBeEquivalentTo(updatedModel.PreviousStep);
        result.Status.ShouldBe(updatedModel.Status);
        result.Thresholds!.First().Pass.ShouldBeNull();
        result.Thresholds!.First().SoftFail.ShouldBe("25");
        result.Thresholds!.First().HardFail.ShouldBe("256");
    }

    [Fact]
    public async Task GetByCreditApplicationId_ValidModel_ReturnsCreatedModel()
    {
        var dto = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetByCreditApplicationId(It.IsAny<string>(), default))
            .ReturnsAsync(new List<DecisionEngineStepsDocument>() { dto });

        var result = (await _decisionEngineStepsService.GetByCreditApplicationId(dto.Id, default)).ToList();

        result.Count.ShouldBe(1);
        result[0].CreatedBy.ShouldBeEquivalentTo(dto.CreatedBy);
        result[0].Id.ShouldBeEquivalentTo(dto.Id);
        result[0].PreviousStep.ShouldBeEquivalentTo(dto.PreviousStep);
    }

    [Fact]
    public async Task GetByDrawApprovalId_ValidModel_ReturnsCreatedModel()
    {
        var dto = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            DrawApprovalId = "drawId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetByDrawApprovalId(dto.DrawApprovalId, default))
            .ReturnsAsync(new List<DecisionEngineStepsDocument>() { dto });

        var result = (await _decisionEngineStepsService.GetByDrawApprovalId(dto.DrawApprovalId, default)).ToList();

        result.Count.ShouldBe(1);
        result[0].CreatedBy.ShouldBeEquivalentTo(dto.CreatedBy);
        result[0].Id.ShouldBeEquivalentTo(dto.Id);
        result[0].PreviousStep.ShouldBeEquivalentTo(dto.PreviousStep);
    }

    [Fact]
    public async Task GetById_ValidModel_ReturnsCreatedModel()
    {
        var dto = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(dto);

        var result = await _decisionEngineStepsService.GetById(dto.Id, default);

        result.CreatedBy.ShouldBeEquivalentTo(dto.CreatedBy);
        result.Id.ShouldBeEquivalentTo(dto.Id);
        result.PreviousStep.ShouldBeEquivalentTo(dto.PreviousStep);
    }

    [Fact]
    public async Task GetByExecutionId_ValidModel_ReturnsModel()
    {
        var dto = new DecisionEngineStepsDocument()
        {
            Id = "id",
            CreatedBy = "by me",
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            PreviousStep = " prev",
            Status = "status",
            Step = "step",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDocument>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        _decisionEngineStepsRepository.Setup(x => x.GetByExecutionId(It.IsAny<string>(), default))
            .ReturnsAsync(new List<DecisionEngineStepsDocument>() { dto });

        var result = (await _decisionEngineStepsService.GetByExecutionId(dto.ExecutionId, default)).ToList();

        result.Count.ShouldBe(1);
        result[0].CreatedBy.ShouldBeEquivalentTo(dto.CreatedBy);
        result[0].ExecutionId.ShouldBeEquivalentTo(dto.ExecutionId);
        result[0].PreviousStep.ShouldBeEquivalentTo(dto.PreviousStep);

        _decisionEngineStepsRepository.Verify(x => x.GetByExecutionId(It.IsAny<string>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByQuery_ValidQuery_ShouldReturnDecisionEngineSteps(DecisionEngineStepsQueryModel query, List<DecisionEngineStepsDocument> decisionEngineSteps, List<CreditApplicationDocument> creditApplicationDocuments)
    {
        var expectedCreditAppIds = query.CreditApplicationIds!.Union(creditApplicationDocuments.Select(x => x.Id)).ToArray();
        _creditApplicationRepository.Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(f => f.CompanyId == query.CompanyId), default)).ReturnsAsync(creditApplicationDocuments);
        _decisionEngineStepsRepository.Setup(x => x.GetByFilters(It.Is<DecisionEngineStepsQuery>(f
            => f.CreditApplicationIds == expectedCreditAppIds
            && f.ExecutionTypes == query.ExecutionTypes
            && f.ExecutionIds == query.ExecutionIds
            && f.Statuses == query.Statuses
            && f.StepNames == query.StepNames
            && f.DrawApprovalIds == query.DrawApprovalIds), default)).ReturnsAsync(decisionEngineSteps);

        await _decisionEngineStepsService.GetByQuery(query, default);

        _creditApplicationRepository.Verify(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default), Times.Once);
        _decisionEngineStepsRepository.Verify(x => x.GetByFilters(It.IsAny<DecisionEngineStepsQuery>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyId_ValidCompanyIdAndFilerApplied_ShouldReturnDecisionEngineStepsWithoutDrawApprovals(
     string companyId,
     List<DecisionEngineStepsDocument> decisionEngineSteps,
     List<CreditApplicationDocument> creditApplicationDocuments,
     AccountAuthorization accAuthDocument)
    {
        var creditAppIds = creditApplicationDocuments.Select(x => x.Id).ToArray();

        _creditApplicationRepository
            .Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(q => q.CompanyId == companyId && q.Type!.Contains(CreditApplicationType.LineOfCredit.ToString())), It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplicationDocuments);

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAllByFilters(null, companyId, null, null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AccountAuthorization> { accAuthDocument });

        _decisionEngineStepsRepository
            .Setup(x => x.GetByCreditApplicationIds(It.Is<string[]>(ids => ids.SequenceEqual(creditAppIds)), It.IsAny<CancellationToken>()))
            .ReturnsAsync(decisionEngineSteps);

        _decisionEngineStepsRepository
            .Setup(x => x.GetByAccountAuthorizationId(accAuthDocument.Id!, It.IsAny<CancellationToken>()))
            .ReturnsAsync(decisionEngineSteps);

        await _decisionEngineStepsService.GetByCompanyId(companyId, CreditApplicationType.LineOfCredit, default);

        _creditApplicationRepository.Verify(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(q => q.CompanyId == companyId && q.Type!.Contains(CreditApplicationType.LineOfCredit.ToString())), It.IsAny<CancellationToken>()), Times.Once);
        _accountAuthorizationsServiceMock.Verify(x => x.GetAllByFilters(null, companyId, null, null, It.IsAny<CancellationToken>()), Times.Once);
        _drawApprovalRepository.Verify(x => x.GetAll(It.IsAny<Expression<Func<DrawApprovalDocument, bool>>>(), It.IsAny<CancellationToken>()), Times.Never);
        _decisionEngineStepsRepository.Verify(x => x.GetByCreditApplicationIds(It.Is<string[]>(ids => ids.SequenceEqual(creditAppIds)), It.IsAny<CancellationToken>()), Times.Once);
        _decisionEngineStepsRepository.Verify(x => x.GetByAccountAuthorizationId(accAuthDocument.Id!, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyId_ValidCompanyIdWithoutFilters_ShouldReturnDecisionEngineSteps(
    string companyId,
    List<DecisionEngineStepsDocument> decisionEngineSteps,
    List<CreditApplicationDocument> creditApplicationDocuments,
    AccountAuthorization accAuthDocument,
    List<DrawApprovalDocument> drawApprovalDocuments)
    {
        var creditAppIds = creditApplicationDocuments.Select(x => x.Id).ToArray();
        var drawApprovalIds = drawApprovalDocuments.Select(x => x.Id).ToArray();

        _creditApplicationRepository
            .Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(q => q.CompanyId == companyId && q.Type == null), It.IsAny<CancellationToken>()))
            .ReturnsAsync(creditApplicationDocuments);

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAllByFilters(null, companyId, null, null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AccountAuthorization> { accAuthDocument });

        _drawApprovalRepository
            .Setup(x => x.GetAll(It.Is<Expression<Func<DrawApprovalDocument, bool>>>(predicate =>
                predicate.Compile().Invoke(new DrawApprovalDocument { CompanyId = companyId })), It.IsAny<CancellationToken>()))
            .ReturnsAsync(drawApprovalDocuments);

        _decisionEngineStepsRepository
            .Setup(x => x.GetByCreditApplicationIds(It.Is<string[]>(ids => ids.SequenceEqual(creditAppIds)), It.IsAny<CancellationToken>()))
            .ReturnsAsync(decisionEngineSteps);

        _decisionEngineStepsRepository
            .Setup(x => x.GetByAccountAuthorizationId(accAuthDocument.Id!, It.IsAny<CancellationToken>()))
            .ReturnsAsync(decisionEngineSteps);

        _decisionEngineStepsRepository
            .Setup(x => x.GetByDrawApprovalIds(It.Is<string[]>(ids => ids.SequenceEqual(drawApprovalIds)), It.IsAny<CancellationToken>()))
            .ReturnsAsync(decisionEngineSteps);

        await _decisionEngineStepsService.GetByCompanyId(companyId, null, default);

        _creditApplicationRepository.Verify(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(q => q.CompanyId == companyId && q.Type == null), default), Times.Once);
        _accountAuthorizationsServiceMock.Verify(x => x.GetAllByFilters(null, companyId, null, null, It.IsAny<CancellationToken>()), Times.Once);
        _drawApprovalRepository.Verify(x => x.GetAll(It.IsAny<Expression<Func<DrawApprovalDocument, bool>>>(), It.IsAny<CancellationToken>()), Times.Once);
        _decisionEngineStepsRepository.Verify(x => x.GetByCreditApplicationIds(It.Is<string[]>(ids => ids.SequenceEqual(creditAppIds)), It.IsAny<CancellationToken>()), Times.Once);
        _decisionEngineStepsRepository.Verify(x => x.GetByAccountAuthorizationId(accAuthDocument.Id!, It.IsAny<CancellationToken>()), Times.Once);
        _decisionEngineStepsRepository.Verify(x => x.GetByDrawApprovalIds(It.Is<string[]>(ids => ids.SequenceEqual(drawApprovalIds)), It.IsAny<CancellationToken>()), Times.Once);
    }


    [Fact]
    public async Task ReinstateDecisionEngineRule_ShouldClearManualOverrideFields_WhenResultsMatch()
    {
        var companyId = "companyId";
        var identifier = "identifier";
        var userId = "userId";
        var reinstateModel = new ReinstateDecisionEngineRuleModel()
        {
            RuleCode = "Rule-101"
        };
        var accountAuthDetails = new List<AccountAuthorization>
        {
            new() { Id = "authId1", CompanyId = companyId }
        };

        var scheduledSteps = new List<DecisionEngineStepsDocument>
        {
            new()
            {
                AccountAuthorizationDetailsId = "authId1",
                ExecutionType = "Scheduled",
                Step = "KYB",
                UpdatedAt = DateTime.Now,
                Results = new List<DecisionEngineStepResultDocument>
                {
                    new()
                    {
                        Code = reinstateModel.RuleCode,
                        ManualResult = "manualResult",
                        OwnerIdentifier = identifier
                    }
                }
            }
        };

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<AccountAuthorizationDocument, bool>>>(), default))
            .ReturnsAsync(accountAuthDetails);

        _decisionEngineStepsRepository
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(scheduledSteps);

        await _decisionEngineStepsService.ReinstateDecisionEngineRule(companyId, identifier, userId, reinstateModel, default);

        _decisionEngineStepsRepository.Verify(x => x.Update(It.Is<DecisionEngineStepsDocument>(d =>
            d.Results!.Any(r => r.ManualResult == null &&
                               r.ManualResultAt == null &&
                               r.ManualResultBy == null &&
                               r.ManualResultNote == null)), default), Times.Once);

        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }

    [Fact]
    public async Task ReinstateDecisionEngineRule_ShouldNotUpdate_WhenNoResultsMatch()
    {
        var companyId = "companyId";
        var identifier = "identifier";
        var userId = "userId";
        var reinstateModel = new ReinstateDecisionEngineRuleModel()
        {
            RuleCode = "Rule-101"
        };
        var ct = CancellationToken.None;

        var accountAuthDetails = new List<AccountAuthorization>
        {
            new() { Id = "authId1", CompanyId = companyId }
        };

        var scheduledSteps = new List<DecisionEngineStepsDocument>
        {
            new()
            {
                AccountAuthorizationDetailsId = "authId1",
                ExecutionType = "Scheduled",
                Results = new List<DecisionEngineStepResultDocument>
                {
                    new()
                    {  Code = "differentCode",
                        ManualResult = "manualResult",
                        OwnerIdentifier = identifier
                    }
                }
            }
        };

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<AccountAuthorizationDocument, bool>>>(), ct))
            .ReturnsAsync(accountAuthDetails);

        _decisionEngineStepsRepository
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), ct))
            .ReturnsAsync(scheduledSteps);

        await _decisionEngineStepsService.ReinstateDecisionEngineRule(companyId, identifier, userId, reinstateModel, ct);

        _decisionEngineStepsRepository.Verify(x => x.Update(It.IsAny<DecisionEngineStepsDocument>(), ct), Times.Never);
        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }

    [Fact]
    public async Task IgnoreDecisionEngineRule_ShouldFillManualOverrideFields_WhenResultsMatch()
    {
        var companyId = "companyId";
        var identifier = "identifier";
        var userId = "userId";
        var caption = "caption";
        var ignoreModel = new IgnoreDecisionEngineRuleModel()
        {
            RuleCode = "Rule-101"
        };
        var accountAuthDetails = new List<AccountAuthorization>
        {
            new() { Id = "authId1", CompanyId = companyId }
        };

        var scheduledSteps = new List<DecisionEngineStepsDocument>
        {
            new()
            {
                AccountAuthorizationDetailsId = "authId1",
                ExecutionType = "Scheduled",
                Step = "KYB",
                UpdatedAt = DateTime.Now,
                Results = new List<DecisionEngineStepResultDocument>
                {
                    new()
                    {
                        Code = ignoreModel.RuleCode,
                        ManualResult = null,
                        OwnerIdentifier = identifier
                    }
                }
            }
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(DateTime.UtcNow);

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<AccountAuthorizationDocument, bool>>>(), default))
            .ReturnsAsync(accountAuthDetails);

        _decisionEngineStepsRepository
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(scheduledSteps);

        await _decisionEngineStepsService.IgnoreDecisionEngineRule(companyId, identifier, userId, ignoreModel, default);

        _decisionEngineStepsRepository.Verify(x => x.Update(It.Is<DecisionEngineStepsDocument>(d =>
           d.Results!.Any(r => r.ManualResult == AutomatedDecisionResult.Pass.ToString() &&
                              r.ManualResultAt == _dateProviderMock.Object.CurrentDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") &&
                              r.ManualResultBy == userId &&
                              r.ManualResultNote == ignoreModel.Note)), default), Times.Once);

        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }

    [Fact]
    public async Task IgnoreDecisionEngineRule_ShouldNotUpdate_WhenNoResultsMatch()
    {
        var companyId = "companyId";
        var identifier = "identifier";
        var userId = "userId";
        var ignoreModel = new IgnoreDecisionEngineRuleModel()
        {
            RuleCode = "Rule-101"
        };
        var ct = CancellationToken.None;

        var accountAuthDetails = new List<AccountAuthorization>
        {
            new() { Id = "authId1", CompanyId = companyId }
        };

        var scheduledSteps = new List<DecisionEngineStepsDocument>
        {
            new()
            {
                AccountAuthorizationDetailsId = "authId1",
                ExecutionType = "Scheduled",
                Results = new List<DecisionEngineStepResultDocument>
                {
                    new()
                    {  Code = "differentCode",
                        ManualResult = "manualResult",
                        OwnerIdentifier = identifier
                    }
                }
            }
        };

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<AccountAuthorizationDocument, bool>>>(), ct))
            .ReturnsAsync(accountAuthDetails);

        _decisionEngineStepsRepository
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), ct))
            .ReturnsAsync(scheduledSteps);

        await _decisionEngineStepsService.IgnoreDecisionEngineRule(companyId, identifier, userId, ignoreModel, ct);

        _decisionEngineStepsRepository.Verify(x => x.Update(It.IsAny<DecisionEngineStepsDocument>(), ct), Times.Never);
        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }


    [Fact]
    public async Task IgnoreDecisionEngineRule_ShouldHandleGroupRuleCodeAndFillManualOverrideFields_WhenResultsMatch()
    {
        var companyId = "companyId";
        var inputRuleCode = "Rule-302/1";
        var ruleCode1 = "Rule-302/1";
        var ruleCode2 = "Rule-302/2";
        var identifier = "identifier";
        var userId = "userId";
        var caption = "caption";
        var ignoreModel = new IgnoreDecisionEngineRuleModel()
        {
            RuleCode = inputRuleCode
        };
        var accountAuthDetails = new List<AccountAuthorization>
        {
            new() { Id = "authId1", CompanyId = companyId }
        };

        var scheduledSteps = new List<DecisionEngineStepsDocument>
        {
            new()
            {
                AccountAuthorizationDetailsId = "authId1",
                CreditApplicationId = "creditApp1",
                ExecutionType = "Scheduled",
                Step = "KYC",
                UpdatedAt = DateTime.Now,
                Results = new List<DecisionEngineStepResultDocument>
                {
                    new()
                    {
                        Code = ruleCode1,
                        ManualResult = "manualResult",
                        OwnerIdentifier = identifier,
                    }
                }
            },
            new()
            {
                AccountAuthorizationDetailsId = "authId1",
                CreditApplicationId = "creditApp1",
                ExecutionType = "Scheduled",
                Step = "KYB",
                UpdatedAt = DateTime.Now,
                Results = new List<DecisionEngineStepResultDocument>
                {
                    new()
                    {
                        Code = ruleCode2,
                        ManualResult = "manualResult",
                        OwnerIdentifier = identifier,
                    }
                }
            }
        };

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(DateTime.UtcNow);

        _accountAuthorizationsServiceMock
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<AccountAuthorizationDocument, bool>>>(), default))
            .ReturnsAsync(accountAuthDetails);

        _decisionEngineStepsRepository
            .Setup(x => x.GetAll(It.IsAny<Expression<Func<DecisionEngineStepsDocument, bool>>>(), default))
            .ReturnsAsync(scheduledSteps);

        await _decisionEngineStepsService.IgnoreDecisionEngineRule(companyId, identifier, userId, ignoreModel, default);

        _decisionEngineStepsRepository.Verify(x => x.Update(It.Is<DecisionEngineStepsDocument>(d =>
           d.Results!.Any(r => r.ManualResult == AutomatedDecisionResult.Pass.ToString() &&
                              r.ManualResultAt == _dateProviderMock.Object.CurrentDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") &&
                              r.ManualResultBy == userId &&
                              r.ManualResultNote == ignoreModel.Note)), default), Times.AtLeast(2));

        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }
}
