namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;

public class CreditApplicationNote
{
    public string Id { get; set; } = String.Empty;

    public string CreditApplicationId { get; set; } = String.Empty;

    public string? Note { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreatedBy { get; set; } = string.Empty;

    public string? Caption { get; set; }

    public string? DisplayName { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedAt { get; set; }

    public string? ExecutionId { get; set; }

    public bool? IsSystemGenerated { get; set; }
}
