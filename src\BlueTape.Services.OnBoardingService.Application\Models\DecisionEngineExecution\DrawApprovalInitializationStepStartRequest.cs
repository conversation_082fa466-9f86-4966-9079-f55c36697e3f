﻿using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using System.Text.Json.Serialization;

namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;

public class DrawApprovalInitializationStepStartRequest
{
    [JsonPropertyName("jobId")]
    public Guid JobId { get; set; }

    [JsonPropertyName("creditId")]
    public string? CreditId { get; set; } = string.Empty;

    [JsonPropertyName("arAdvanceCreditId")]
    public string? ArAdvanceCreditId { get; set; } = string.Empty;

    [JsonPropertyName("inHouseCreditId")]
    public string? InHouseCreditId { get; set; } = string.Empty;

    [JsonPropertyName("merchantId")]
    public string? MerchantId { get; set; } = string.Empty;

    [JsonPropertyName("drawApprovalId")]
    public string DrawApprovalId { get; set; } = string.Empty;

    [JsonPropertyName("companyId")]
    public string CompanyId { get; set; } = string.Empty;

    [JsonPropertyName("paymentPlanId")]
    public string PaymentPlanId { get; set; } = string.Empty;

    [JsonPropertyName("projectId")]
    public string? ProjectId { get; set; } = string.Empty;

    [JsonPropertyName("drawAmount")]
    public decimal DrawAmount { get; set; }

    [JsonPropertyName("creditHoldAmount")]
    public decimal? CreditHoldAmount { get; set; }

    [JsonPropertyName("expirationDate")]
    public DateTime? ExpirationDate { get; set; }

    [JsonPropertyName("loanOrigin")]
    public string? LoanOrigin { get; set; }

    [JsonPropertyName("payables")]
    public IList<InputPayableItem> Payables { get; set; } = new List<InputPayableItem>();

    [JsonPropertyName("noSupplierDetails")]
    public NoSupplierDetailsModel? NoSupplierDetails { get; set; }

    [JsonPropertyName("downPaymentDetails")]
    public DownPaymentDetails? DownPaymentDetails { get; set; }
}
