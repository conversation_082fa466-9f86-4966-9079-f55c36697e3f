using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Models.Notes;
using BlueTape.Services.OnBoardingService.Application.Templates;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Serilog;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class CreditApplicationNotesService : ICreditApplicationNotesService
{
    private readonly ICreditApplicationNotesRepository _repository;
    private readonly IMapper _mapper;

    public CreditApplicationNotesService(ICreditApplicationNotesRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CreditApplicationNote>> GetByApplicationId(string id, CancellationToken ct)
    {
        Log.Information("Retrieving notes for credit application {ApplicationId}", id);
        var result = await _repository.GetByApplicationId(id, ct);
        Log.Information("Found {Count} notes for credit application {ApplicationId}", result.Count(), id);

        return _mapper.Map<IEnumerable<CreditApplicationNote>>(result);
    }

    public async Task<CreditApplicationNote> AddSystemNote(CreateSystemCreditApplicationNote note, CancellationToken ct)
    {
        Log.Information("Adding system generated note {@Note} for application {ApplicationId}", note, note.CreditApplicationId);
        var noteBuilder = SystemNotesTemplates.CreditApplicationsNoteBuilders[note.SystemNoteTemplate];

        var templateInfo = _mapper.Map<SystemNoteTemplateInfo>(note);

        var document = _mapper.Map<CreditApplicationNoteDocument>(note);
        document.Note = noteBuilder(templateInfo);
        document.IsSystemGenerated = true;

        var result = await _repository.Add(document, ct);

        Log.Information("Added system generated note {@Note} for application {ApplicationId}", note, note.CreditApplicationId);

        return _mapper.Map<CreditApplicationNote>(result);
    }

    public async Task<CreditApplicationNote> Add(CreateCreditApplicationNote note, CancellationToken ct)
    {
        var document = _mapper.Map<CreditApplicationNoteDocument>(note);
        document.IsSystemGenerated = false;

        Log.Information("Adding note {@Note} for application {ApplicationId}", note, note.CreditApplicationId);
        var result = await _repository.Add(document, ct);
        Log.Information("Added note {@Note} for application {ApplicationId}", result, result.CreditApplicationId);

        return _mapper.Map<CreditApplicationNote>(result);
    }

    public async Task<IEnumerable<CreditApplicationNote>> AddRange(IEnumerable<CreateCreditApplicationNote> notes, CancellationToken ct)
    {
        Log.Information("Started creation of credit application note list");
        var documents = new List<CreditApplicationNoteDocument>();
        foreach (var note in notes)
        {
            var noteDocument = _mapper.Map<CreditApplicationNoteDocument>(note);
            noteDocument.IsSystemGenerated = false;
            documents.Add(noteDocument);
        }

        if (!documents.Any())
        {
            Log.Information("Credit application note list is empty.");
            return [];
        }

        var addedDocuments = await _repository.AddRange(documents, ct);
        Log.Information("Finished creation of creation of credit application notes");

        return _mapper.Map<IEnumerable<CreditApplicationNote>>(addedDocuments);
    }

    public async Task<CreditApplicationNote> Patch(PatchCreditApplicationNote patchModel, CancellationToken ct)
    {
        Log.Information("Updating note {@note} for credit application {id}", patchModel, patchModel.CreditApplicationId);
        var existingNote = await _repository.GetById(patchModel.Id, ct);
        if (IsSystemGeneratedNote(existingNote))
        {
            throw new ValidationException("Could not edit system generated note.");
        }

        _mapper.Map(patchModel, existingNote);
        var result = await _repository.Update(existingNote, ct);
        Log.Information("Added note {@Note} for credit application {id}", result, result.CreditApplicationId);
        return _mapper.Map<CreditApplicationNote>(result);
    }

    public async Task Delete(DeleteCreditApplicationNote note, CancellationToken ct)
    {
        Log.Information("Deleting note with id {id} for credit application {ApplicationId}", note.Id, note.CreditApplicationId);
        var existingNote = await _repository.GetById(note.Id, ct);
        if (IsSystemGeneratedNote(existingNote))
        {
            throw new ValidationException("Could not delete system generated note.");
        }

        await _repository.SoftDelete(note.Id, note.UserId, ct);
        Log.Information("Soft deleted note with id: {id} for credit application {ApplicationId}", note.Id, note.CreditApplicationId);
    }

    private static bool IsSystemGeneratedNote(CreditApplicationNoteDocument note)
    {
        return note.IsSystemGenerated.HasValue && note.IsSystemGenerated.Value;
    }
}
