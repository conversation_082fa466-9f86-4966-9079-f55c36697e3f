﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.User;

[BsonIgnoreExtraElements]
[MongoCollection("userroles")]
public class UserRoleDocument : Document
{
    [BsonElement("sub")]
    public string? Sub { get; set; }

    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("role")]
    public string? Role { get; set; }
}
