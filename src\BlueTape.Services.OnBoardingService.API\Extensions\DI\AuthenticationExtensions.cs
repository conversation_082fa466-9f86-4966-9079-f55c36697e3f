﻿using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.API.Handlers;
using Microsoft.AspNetCore.Authentication;

namespace BlueTape.Services.OnBoardingService.API.Extensions.DI
{
    public static class AuthenticationExtensions
    {
        public static void ConfigureAuthentication(this WebApplicationBuilder builder)
        {
            builder.Services.AddAuthentication(o =>
            {
                o.DefaultScheme = AuthenticationConstants.ApiKeyAuthScheme;
                o.DefaultChallengeScheme = AuthenticationConstants.ApiKeyAuthScheme;
            }).AddScheme<AuthenticationSchemeOptions, ApiKeyHandler>(AuthenticationConstants.ApiKeyAuthScheme, o => { });
        }
    }
}
