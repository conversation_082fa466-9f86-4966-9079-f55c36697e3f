﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BlueTape.Common.Extensions" Version="1.1.1" />
    <PackageReference Include="BlueTape.MongoDB" Version="1.1.32" />
    <PackageReference Include="BlueTape.OBS" Version="1.6.71" />
    <PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.Domain\BlueTape.Services.OnBoardingService.Domain.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.Infrastructure\BlueTape.Services.OnBoardingService.Infrastructure.csproj" />
  </ItemGroup>

</Project>
