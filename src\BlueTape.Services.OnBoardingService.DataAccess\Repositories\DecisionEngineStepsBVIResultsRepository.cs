﻿using BlueTape.MongoDB.DTO.Base;
using BlueTape.MongoDB.DTO.ExperianLogging;
using BlueTape.MongoDB.DTO.Giact;
using BlueTape.MongoDB.DTO.LexisNexisLogging;
using BlueTape.MongoDB.DTO.Plaid;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class DecisionEngineStepsBviResultsRepository : GenericRepository<DecisionEngineStepsBviResultsDocument>,
    IDecisionEngineStepsBviResultsRepository
{
    private readonly IObsMongoDBContext _context;

    public DecisionEngineStepsBviResultsRepository(IObsMongoDBContext context,
        ILogger<GenericRepository<DecisionEngineStepsBviResultsDocument>> logger) : base(context, logger)
    {
        _context = context;
    }

    public async Task<IEnumerable<DecisionEngineStepsBviResultsDocument>> GetAllByFilters(string? id, string? executionId, string? integrationLogId, string? decisionEngineStepId, string? creditApplicationId, CancellationToken ct)
    {
        Logger.LogInformation("Get DecisionEngineStepsBVIResults by filters: {filters}", string.Join(", ", id, executionId, integrationLogId, decisionEngineStepId, creditApplicationId));

        var filterDefinition = Builders<DecisionEngineStepsBviResultsDocument>.Filter.Empty;

        if (id is not null) filterDefinition &= Builders<DecisionEngineStepsBviResultsDocument>.Filter.Eq(x => x.Id, id);
        if (executionId is not null) filterDefinition &= Builders<DecisionEngineStepsBviResultsDocument>.Filter.Eq(x => x.ExecutionId, executionId);
        if (integrationLogId is not null) filterDefinition &= Builders<DecisionEngineStepsBviResultsDocument>.Filter.Eq(x => x.IntegrationLogId, integrationLogId);
        if (decisionEngineStepId is not null) filterDefinition &= Builders<DecisionEngineStepsBviResultsDocument>.Filter.Eq(x => x.DecisionEngineStepId, decisionEngineStepId);
        if (creditApplicationId is not null) filterDefinition &= Builders<DecisionEngineStepsBviResultsDocument>.Filter.Eq(x => x.CreditApplicationId, creditApplicationId);

        var documents = Collection.Find(filterDefinition);

        return await documents.ToListAsync(ct);
    }

    public async Task<IEnumerable<DecisionEngineStepsBviResultsDocument>> AddRange(IEnumerable<DecisionEngineStepsBviResultsDocument> documents, CancellationToken ct)
    {
        Logger.LogInformation("Add new DecisionEngineStepsBVIResults documents");
        foreach (var document in documents)
        {
            document.CreatedAt = DateTime.UtcNow;
            document.UpdatedAt = DateTime.UtcNow;
        }

        await Collection.InsertManyAsync(documents, cancellationToken: ct);

        return documents;
    }

    public async Task<IEnumerable<RequestResponseBaseDocument?>> GetBviResponsesByStepId(string stepId, CancellationToken ct)
    {
        var stepsResults = await Collection.Aggregate()
            .Match(results => results.DecisionEngineStepId == stepId).ToListAsync(ct);

        if (!stepsResults.Any()) return new List<RequestResponseBaseDocument>();

        var tasks = stepsResults.Select(x => GetFromIntegrationLogsCollectionById(x, ct));

        return await Task.WhenAll(tasks);
    }

    public async Task<RequestResponseBaseDocument?> GetLexisNexisResponse(string creditApplicationId, LexisNexisSourceType type, string? reference, CancellationToken ct)
    {
        var results = await Collection.Aggregate()
            .Match(results => results.CreditApplicationId == creditApplicationId && results.IntegrationSource == "LexisNexis").ToListAsync(ct);

        if (!results.Any()) return null;

        var tasks = results.Select(x => GetFromIntegrationLogsCollectionById(x, ct));

        var responses = await Task.WhenAll(tasks);

        var query = responses.Where(x => x != null && x.RequestType == type.ToString());
        if (type != LexisNexisSourceType.BusinessInstantId && !string.IsNullOrEmpty(reference))
        {
            query = query.Where(x => x?.Reference == reference);
        }

        return query.MaxBy(x => x!.CreatedAt);
    }

    public async Task<RequestResponseBaseDocument?> GetGiactResponse(string creditApplicationId, CancellationToken ct)
    {
        var results = await Collection.Aggregate()
            .Match(results => results.CreditApplicationId == creditApplicationId && results.IntegrationSource == "Giact").ToListAsync(ct);

        if (!results.Any()) return null;

        var tasks = results.Select(x => GetFromIntegrationLogsCollectionById(x, ct));

        var responses = await Task.WhenAll(tasks);

        return responses.Where(x => x != null).MaxBy(x => x!.CreatedAt);
    }

    public async Task<RequestResponseBaseDocument?> GetExperianResponse(string creditApplicationId, ExperianSourceType type, CancellationToken ct)
    {
        var results = await Collection.Aggregate()
            .Match(results => results.CreditApplicationId == creditApplicationId && results.IntegrationSource == "Experian").ToListAsync(ct);

        if (!results.Any()) return null;

        var tasks = results.Select(x => GetFromIntegrationLogsCollectionById(x, ct));

        var responses = await Task.WhenAll(tasks);

        return responses.Where(x => x != null && x.RequestType == type.ToString()).MaxBy(x => x!.CreatedAt);
    }

    private async Task<RequestResponseBaseDocument?> GetFromIntegrationLogsCollectionById(DecisionEngineStepsBviResultsDocument stepsBviResults,
        CancellationToken ct)
    {
        var id = stepsBviResults.IntegrationLogId;
        switch (stepsBviResults.IntegrationSource)
        {
            case "Experian":
                {
                    var experian = _context.GetCollection<ExperianLoggingDocument>();
                    var result = experian.Find(x => x.Id == id);
                    return await result.FirstOrDefaultAsync(cancellationToken: ct);
                }
            case "LexisNexis":
                {
                    var lexNex = _context.GetCollection<LexisNexisLoggingDocument>();
                    var result = lexNex.Find(x => x.Id == id);
                    return await result.FirstOrDefaultAsync(cancellationToken: ct);
                }
            case "Plaid":
                {
                    var plaid = _context.GetCollection<PlaidLoggingDocument>();
                    var result = plaid.Find(x => x.Id == id);
                    return await result.FirstOrDefaultAsync(cancellationToken: ct);
                }
            case "Giact":
                {
                    var giact = _context.GetCollection<GiactLoggingDocument>();
                    var result = giact.Find(x => x.Id == id);
                    return await result.FirstOrDefaultAsync(cancellationToken: ct);
                }
            default: return null;
        }
    }

}
