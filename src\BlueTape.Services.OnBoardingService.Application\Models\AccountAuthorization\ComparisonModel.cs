﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class ComparisonModel
{
    public string CompanyId { get; set; } = string.Empty;
    public string EinHash { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public string CreditApplicationId { get; set; } = string.Empty;
    public string ExecutionId { get; set; } = string.Empty;
    public string AccountAuthorizationDetailsId { get; set; } = string.Empty;
    public string ChangeType { get; set; } = string.Empty;
}
