﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories
{
    public class DraftRepository(IObsMongoDBContext context, ILogger<DraftRepository> logger, ICreditApplicationRepository creditApplicationRepository)
        : GenericRepository<DraftDocument>(context, logger), IDraftRepository
    {
        public async Task<IEnumerable<DraftDocument>> GetAllByFilters(string? id, string? companyId, string? creditApplicationId, CancellationToken ct)
        {
            Logger.LogInformation("Get drafts by filters: {filters}", string.Join(", ", id, companyId));

            var filterDefinition = Builders<DraftDocument>.Filter.Empty;
            string? draftId = null;

            if (!string.IsNullOrEmpty(creditApplicationId))
            {
                var creditApplication = await creditApplicationRepository.GetById(creditApplicationId, ct);
                draftId = creditApplication.DraftId;
            }
            if (!string.IsNullOrEmpty(id))
            {
                filterDefinition &= Builders<DraftDocument>.Filter.Eq(x => x.Id, id);
            }
            else if (!string.IsNullOrEmpty(draftId)) filterDefinition &= Builders<DraftDocument>.Filter.Eq(x => x.Id, draftId);
            if (!string.IsNullOrEmpty(companyId)) filterDefinition &= Builders<DraftDocument>.Filter.Eq(x => x.CompanyId, companyId);

            var drafts = Collection.Find(filterDefinition);

            return await drafts.ToListAsync(ct);
        }

        public async Task<IEnumerable<DraftDocument>> GetByCompanyIds(string[] companyIds, CancellationToken ct)
        {
            var filterDefinition = Builders<DraftDocument>.Filter.Empty;
            filterDefinition &= Builders<DraftDocument>.Filter.In(x => x.CompanyId, companyIds);
            var drafts = Collection.Find(filterDefinition);

            return await drafts.ToListAsync(ct);
        }
    }
}
