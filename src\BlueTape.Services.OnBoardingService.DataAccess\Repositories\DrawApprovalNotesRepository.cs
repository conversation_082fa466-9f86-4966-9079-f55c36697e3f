using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApprovalNotes;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class DrawApprovalNotesRepository : GenericRepository<DrawApprovalNoteDocument>, IDrawApprovalNotesRepository
{
    public DrawApprovalNotesRepository(IObsMongoDBContext context, ILogger<DrawApprovalNotesRepository> logger) : base(context, logger)
    {
    }
    public Task<IEnumerable<DrawApprovalNoteDocument>> GetByDrawApprovalId(string id, CancellationToken ct)
        => GetAll(doc => doc.DrawApprovalId == id && doc.DeletedAt == null, ct);

    public async Task SoftDelete(string id, string userId, CancellationToken ct)
    {
        var document = await GetById(id, ct);
        document.DeletedAt = DateTime.UtcNow;
        document.DeletedBy = userId;
        await Update(document, ct);
    }
}