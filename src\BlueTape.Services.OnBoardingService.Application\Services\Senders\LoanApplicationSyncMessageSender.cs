﻿using BlueTape.ServiceBusMessaging;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Models.Compatibility;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services.Senders;

public class LoanApplicationSyncMessageSender(IConfiguration configuration, ILogger<LoanApplicationSyncMessageSender> logger)
    : ServiceBusMessageSender<SyncLoanApplicationMessagePayload>(
        configuration,
        logger,
        InfrastructureConstants.LoanApplicationSyncQueueName,
        InfrastructureConstants.LoanApplicationSyncQueueConnectionString), ILoanApplicationSyncMessageSender
{
}
