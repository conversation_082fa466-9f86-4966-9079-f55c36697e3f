﻿namespace BlueTape.Services.OnBoardingService.Application.Models.Draft;

public class Draft
{
    public string Id { get; set; } = null!;

    public string? Sub { get; set; }

    public string? CompanyId { get; set; }

    public string? Type { get; set; }

    public Data? Data { get; set; }

    public string? Current { get; set; }

    public IEnumerable<string>? Filled { get; set; }

    public string? CreditApplicationId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }
    public DateTime? SubmitDate { get; set; }
}