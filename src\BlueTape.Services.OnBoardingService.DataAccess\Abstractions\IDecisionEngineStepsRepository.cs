﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DecisionEngineSteps;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

public interface IDecisionEngineStepsRepository : IGenericRepository<DecisionEngineStepsDocument>
{
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByCreditApplicationId(string creditApplicationId, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByDrawApprovalId(string drawApprovalId, CancellationToken ct);
    Task<DecisionEngineStepsDocument?> GetByStepName(string creditApplicationId, StepName stepName, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByCreditApplicationIds(string[] creditApplicationIds, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByDrawApprovalIds(string[] drawApprovalIds, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByAccountAuthorizationId(string accountAuthorizationId, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByExecutionId(string executionId, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsDocument>> GetByFilters(DecisionEngineStepsQuery query, CancellationToken ct);
    Task MigrateExecutionType(CancellationToken ct);
}
