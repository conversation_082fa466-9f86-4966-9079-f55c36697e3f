# Service Bus Namespace
resource "azurerm_servicebus_namespace" "sbn" {
  name                = "service-bus-namespace-${var.application_name.full}-${var.environment}"
  location            = var.resource_group_location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"

  tags = {
    environment = title(var.environment)
    source      = "Terraform"
    app         = title(var.application_name.full)
    CreatedOn   = formatdate("YYYY-MM-DD hh:mm ZZZ", timestamp())
    Type        = "Microsoft Azure Service Bus Namespace"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedOn"]
    ]
  }
}

resource "azurerm_servicebus_queue" "sblas" {
  name         = "loanapplicationsync-${var.environment}"
  namespace_id = azurerm_servicebus_namespace.sbn.id
} 

resource "azurerm_key_vault_secret" "obs-bus-loan-application-sync-queue-connection-secret" {
  name         = "${var.loan_application_sync_queue_connection}"
  value        = azurerm_servicebus_queue_authorization_rule.obs-bus-loan-application-sync-queue-read-write-rule.primary_connection_string
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "obs-bus-loan-application-sync-queue-name-secret" {
  name         = "${var.loan_application_sync_queue_name}"
  value        = azurerm_servicebus_queue.sblas.name
  key_vault_id = var.key_vault_id
} 

resource "azurerm_servicebus_queue_authorization_rule" "obs-bus-loan-application-sync-queue-read-write-rule" {
  name     = "${var.application_name.slug}-bus-queue-connection-auth-rule"
  queue_id = azurerm_servicebus_queue.sblas.id
  listen = true
  send = true
}

resource "azurerm_servicebus_queue" "sbdeadrs" {
  name         = "authorization-details-refresh-${var.environment}"
  namespace_id = azurerm_servicebus_namespace.sbn.id
} 

resource "azurerm_servicebus_queue_authorization_rule" "obs-bus-authorization-details-refresh-queue-read-write-rule" {
  name     = "${var.application_name.slug}-bus-deadrs-queue-connection-auth-rule"
  queue_id = azurerm_servicebus_queue.sbdeadrs.id
  listen = true
  send = true
}

resource "azurerm_key_vault_secret" "obs-bus-authorization-details-refresh-queue-connection-secret" {
  name         = "${var.authorization_details_refresh_queue_connection}"
  value        = azurerm_servicebus_queue_authorization_rule.obs-bus-authorization-details-refresh-queue-read-write-rule.primary_connection_string
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "obs-bus-authorization-details-refresh-queue-name-secret" {
  name         = "${var.authorization_details_refresh_queue_name}"
  value        = azurerm_servicebus_queue.sbdeadrs.name
  key_vault_id = var.key_vault_id
} 