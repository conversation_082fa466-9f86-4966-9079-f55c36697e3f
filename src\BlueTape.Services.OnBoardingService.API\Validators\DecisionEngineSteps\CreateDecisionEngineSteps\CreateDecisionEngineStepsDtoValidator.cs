﻿using BlueTape.OBS.DTOs.DecisionEngineSteps;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.DecisionEngineSteps.CreateDecisionEngineSteps;

public class CreateDecisionEngineStepsDtoValidator : AbstractValidator<CreateDecisionEngineStepsDto>
{
    public CreateDecisionEngineStepsDtoValidator()
    {
        RuleFor(x => x.Step).NotEmpty().NotNull();
        RuleFor(x => x.ExecutionId).NotEmpty().NotNull();
        RuleFor(x => x.PolicyVersion).NotEmpty().NotNull();
    }
}