﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Models.IntegrationLogs;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using Newtonsoft.Json;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class DecisionEngineStepsBviResultsService : IDecisionEngineStepsBviResultsService
{
    private readonly IDecisionEngineStepsBviResultsRepository _decisionEngineStepsBviResultsRepository;
    private readonly IMapper _mapper;

    public DecisionEngineStepsBviResultsService(IDecisionEngineStepsBviResultsRepository decisionEngineStepsBVIResultsRepository, IMapper mapper)
    {
        _mapper = mapper;
        _decisionEngineStepsBviResultsRepository = decisionEngineStepsBVIResultsRepository;
    }

    public async Task<IEnumerable<DecisionEngineStepsBviResultsModel>> AddRange(IEnumerable<CreateDecisionEngineStepsBviResultsModel> createModels, CancellationToken ct)
    {
        if (!createModels.Any()) return Enumerable.Empty<DecisionEngineStepsBviResultsModel>();

        var documents = _mapper.Map<IEnumerable<DecisionEngineStepsBviResultsDocument>>(createModels);
        var entries = await _decisionEngineStepsBviResultsRepository.AddRange(documents, ct);

        return _mapper.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(entries);
    }

    public async Task<IEnumerable<DecisionEngineStepsBviResultsModel>> GetAllByFilters(string? id, string? executionId, string? integrationLogId, string? decisionEngineStepId,
        string? creditApplicationId, CancellationToken ct)
    {
        var entries = await _decisionEngineStepsBviResultsRepository
            .GetAllByFilters(id, executionId, integrationLogId, decisionEngineStepId, creditApplicationId, ct);

        return _mapper.Map<IEnumerable<DecisionEngineStepsBviResultsModel>>(entries);
    }

    public async Task<IEnumerable<RequestResponseBaseModel?>> GetBviResponsesByStepId(string decisionEngineStepId, CancellationToken ct)
    {
        var entries = await _decisionEngineStepsBviResultsRepository
            .GetBviResponsesByStepId(decisionEngineStepId, ct);

        return _mapper.Map<IEnumerable<RequestResponseBaseModel?>>(entries);
    }

    public async Task<string> GetLexisNexisRawData(string creditApplicationId, LexisNexisSourceType? type, string? reference, CancellationToken ct)
    {
        var response = await GetLexisNexisResponse(creditApplicationId,
            type ?? LexisNexisSourceType.BusinessInstantId, reference, ct);

        return GetRawJsonBviData(response);
    }

    public async Task<string> GetExperianRawData(string creditApplicationId, ExperianSourceType? type, CancellationToken ct)
    {
        var response = await GetExperianResponse(creditApplicationId,
            type ?? ExperianSourceType.BusinessCreditStatus, ct);

        return GetRawJsonBviData(response);
    }

    public async Task<string> GetGiactRawData(string creditApplicationId, CancellationToken ct)
    {
        var response = await GetGiactResponse(creditApplicationId, ct);

        return GetRawJsonBviData(response);
    }

    public async Task<RequestResponseBaseModel?> GetLexisNexisResponse(string creditApplicationId, LexisNexisSourceType type, string? reference, CancellationToken ct)
    {
        var response = await _decisionEngineStepsBviResultsRepository.GetLexisNexisResponse(creditApplicationId, type, reference, ct);

        return _mapper.Map<RequestResponseBaseModel?>(response);
    }

    public async Task<RequestResponseBaseModel?> GetGiactResponse(string creditApplicationId, CancellationToken ct)
    {
        var response = await _decisionEngineStepsBviResultsRepository.GetGiactResponse(creditApplicationId, ct);

        return _mapper.Map<RequestResponseBaseModel?>(response);
    }

    public async Task<RequestResponseBaseModel?> GetExperianResponse(string creditApplicationId, ExperianSourceType type, CancellationToken ct)
    {
        var response = await _decisionEngineStepsBviResultsRepository.GetExperianResponse(creditApplicationId, type, ct);

        return _mapper.Map<RequestResponseBaseModel?>(response);
    }

    private static string GetRawJsonBviData(RequestResponseBaseModel? response)
    {
        return response is not null ? JsonConvert.SerializeObject(response.Response?.ParsedBody ?? new object()).ToFormattedJsonString() : string.Empty;
    }

}