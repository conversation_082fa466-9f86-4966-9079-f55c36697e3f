﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.ParsedDraft;

[BsonIgnoreExtraElements]
public class BankDetailsDocument
{
    [BsonElement("id")]
    public string? Id { get; set; }

    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("accountholderName")]
    public string? AccountHolderName { get; set; }

    [BsonElement("routingNumber")]
    public string? RoutingNumber { get; set; }

    [BsonElement("accountNumber")]
    public string? AccountNumber { get; set; }

    [BsonElement("accountName")]
    public string? AccountName { get; set; }

    [BsonElement("paymentMethodType")]
    public string? PaymentMethodType { get; set; }

    [BsonElement("isPrimary")]
    public bool IsPrimary { get; set; } = false;

    [BsonElement("accountType")]
    public string? AccountType { get; set; }
}
