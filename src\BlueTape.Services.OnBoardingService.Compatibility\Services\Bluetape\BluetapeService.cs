﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Models;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Bluetape;

public class BluetapeService(ICompanyExternalService companyExternalService,
    IDraftRepository draftRepository,
    ILoanApplicationRepository loanApplicationRepository,
    ILogger<BluetapeService> logger) : IBluetapeService

{
    public async Task<BluetapeData> LoanApplicationBluetapeStep(LoanApplicationDocument application, CancellationToken cancellationToken)
    {
        BluetapeData result = new();
        var step = "Bluetape";

        logger.LogInformation("Starting step: {step} for applicationId: {applicationId}", step, application.Id);

        try
        {
            if (application.Draft == null)
            {
                await AttachDraft(application, cancellationToken);
            }

            if (application == null)
            {
                logger.LogError("Loan application not found.");
                throw new ArgumentNullException(nameof(application), "Loan application could not be found.");
            }

            if (application.CompanyId == null)
            {
                logger.LogError("Company ID is missing in the loan application.");
                throw new ArgumentNullException(nameof(application.CompanyId), "Company ID is required for the bluetape step.");
            }

            var collectLoanInfoTask = CollectLoanInfo(application.CompanyId, cancellationToken);
            var loanApplicationDuplicatesTask = LoanApplicationDuplicates(application, cancellationToken);
            var rejectedApplicationsMatchingEinTask = RejectedApplicationsMatchingEin(application, cancellationToken);

            await Task.WhenAll(collectLoanInfoTask, loanApplicationDuplicatesTask, rejectedApplicationsMatchingEinTask);

            var info = await collectLoanInfoTask;
            var matches = await loanApplicationDuplicatesTask;
            var rejectedMatches = await rejectedApplicationsMatchingEinTask;

            result.FailedApplications = info.FailedApplications;
            result.Defaulted = info.Defaulted;
            result.CurrentLate = info.CurrentLate;
            result.AggregateCreditLine = info.AggregateCreditLine;
            result.BusinessOutstandingBalance = info.BusinessOutstandingBalance;
            result.PrincipalOutstandingBalance = info.PrincipalOutstandingBalance;

            //result.EinMatches = matches.EinMatches;
            //result.SsnMatches = matches.SsnMatches;

            result.RejectedApplicationsMatchingSsnEin = rejectedMatches;

            logger.LogInformation("Step Bluetape completed successfully for applicationId: {applicationId}", application.Id);

            return result;
        }
        catch (Exception e)
        {
            logger.LogError("Error occurred during step: {step} for applicationId: {applicationId}. Error: {message}", step, application!.Id, e.Message);
            throw;
        }
    }

    private async Task<LoanInfo> CollectLoanInfo(string companyId, CancellationToken cancellationToken)
    {
        var businessOutstanding =
            await loanApplicationRepository.GetTotalOutstandingApprovedLoanAmount(companyId, cancellationToken);

        var failed = await loanApplicationRepository.GetRejectedLoanApplicationsCount(companyId,
            LoanApplicationStatusConstants.Rejected, cancellationToken);

        var lateApps = await loanApplicationRepository
            .GetApprovedLoanApplicationsWithOutstandingAmount(companyId, LoanApplicationStatusConstants.Approved, cancellationToken);

        return new LoanInfo
        {
            FailedApplications = failed?.GetValue("count").ToInt32() ?? 0,
            Defaulted = 0,
            CurrentLate = lateApps.Count,
            AggregateCreditLine = 0,
            BusinessOutstandingBalance = businessOutstanding?.GetValue("total").ToDouble() ?? 0,
            PrincipalOutstandingBalance = 0
        };
    }

    private async Task<LoanApplicationDuplicatesResult> LoanApplicationDuplicates(
    LoanApplicationDocument app,
    CancellationToken cancellationToken,
    List<PipelineStageDefinition<LoanApplicationDocument, BsonDocument>>? prePipeline = null)
    {
        if (app.Draft == null)
        {
            logger.LogError("App should have draft for the bluetape step.");
            throw new ArgumentNullException(nameof(app.Draft), "App should have draft for the bluetape step");
        }

        app.Draft.TryGetValue("normalized", out var normalized);

        var normalizedValueBool = normalized as bool?;

        if (normalizedValueBool != true) return new LoanApplicationDuplicatesResult();

        string? einHash = null;
        app.Draft.TryGetValue("businessInfo_ein", out var businessInfoEin);

        if (businessInfoEin is string einString)
        {
            einHash = einString;
        }
        else if (businessInfoEin is EncryptedValue einEncrypted)
        {
            einHash = einEncrypted.Hash;
        }

        string? ssnHash = null;
        app.Draft.TryGetValue("businessOwner_ssn", out var businessOwnerSsn);

        if (businessOwnerSsn is string ssnString)
        {
            ssnHash = ssnString;
        }
        else if (businessOwnerSsn is EncryptedValue ssnEncrypted)
        {
            ssnHash = ssnEncrypted.Hash;
        }

        if (app.CompanyId == null)
        {
            logger.LogError("App should have company Id for the bluetape step.");
            throw new ArgumentNullException(app.CompanyId, "App should have company Id for the bluetape step");
        }

        var company = await companyExternalService.GetById(app.CompanyId, cancellationToken);

        if (company == null)
        {
            logger.LogError("Can`t find company for this company id. CompanyId: {companyId}", app.CompanyId);
            throw new ArgumentNullException(app.CompanyId, "Can`t find company for this company id");
        }

        var lookupPipeline = new List<BsonDocument>
        {
            new ("$match", new BsonDocument("company_id", new BsonDocument("$ne", app.CompanyId))),
            new ("$addFields", new BsonDocument("company_id", new BsonDocument("$toObjectId", "$company_id"))),
            new ("$lookup", new BsonDocument
            {
                { "from", company.Name },
                { "localField", "company_id" },
                { "foreignField", "_id" },
                { "as", "company" }
            }),
            new ("$unwind", "$company"),
            new ("$project", new BsonDocument
            {
                { "_id", "$company._id" },
                { "name", "$company.name" }
            }),
            new ("$group", new BsonDocument
            {
                { "_id", new BsonDocument
                    {
                        { "_id", "$_id" },
                        { "name", "$name" }
                    }
                }
            }),
            new ("$replaceRoot", new BsonDocument("newRoot", "$_id")),
            new ("$sort", new BsonDocument("name", 1))
        };

        List<BsonDocument> einMatches = new List<BsonDocument>();
        if (einHash != null)
        {
            einMatches =
                await loanApplicationRepository.GetDocumentsByEinHashWithPipeline(lookupPipeline, prePipeline, einHash,
                    cancellationToken);
        }

        List<BsonDocument> ssnMatches = new List<BsonDocument>();
        if (ssnHash != null)
        {
            ssnMatches =
                await loanApplicationRepository.GetDocumentsBySsnHashWithPipeline(ssnHash, prePipeline, lookupPipeline,
                    cancellationToken);
        }

        return new LoanApplicationDuplicatesResult
        {
            EinMatches = einMatches,
            SsnMatches = ssnMatches
        };
    }

    private async Task<int> RejectedApplicationsMatchingEin(LoanApplicationDocument app, CancellationToken cancellationToken)
    {
        var result = await LoanApplicationDuplicates(app, cancellationToken);

        return result.EinMatches?.Count ?? 0;
    }

    private Dictionary<string, object> DraftNormalizer(DraftDocument draft)
    {
        var normalizedDraft = new Dictionary<string, object>();

        if (draft.Data != null && draft.Data.BusinessInfo != null && draft.Data.BusinessInfo.Items != null)
        {
            foreach (var item in draft.Data.BusinessInfo.Items)
            {
                normalizedDraft.Add($"{draft.Data.BusinessInfo.Group}_{item.Identifier}", item);
            }

            normalizedDraft = normalizedDraft
                .OrderBy(kv => kv.Key)
                .ToDictionary(kv => kv.Key, kv => kv.Value);
        }

        normalizedDraft["normalized"] = true;

        return normalizedDraft;
    }

    private async Task AttachDraft(LoanApplicationDocument application, CancellationToken cancellationToken)
    {
        var draftsCollection = draftRepository.GetAllByFilters(null, application.CompanyId, null, cancellationToken);
        var draft = (await draftsCollection)
            .FirstOrDefault(x => x.Type == "loan_application");

        if (draft != null)
        {
            application.Draft = DraftNormalizer(draft);
        }
    }
}
