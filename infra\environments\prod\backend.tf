##################################################################
##  Terraform Remote Azure Backend (via Azure Srorage Account)  ##
##################################################################

terraform {
  # ## Configure the backend Azure Storage Account to keep "*.tfstate" file in
  backend "azurerm" {
    resource_group_name  = "prod"
    storage_account_name = "tfstatebtprod9fde916"
    container_name       = "tfstate-applications"
    key                  = "terraform-obs.tfstate"
  }

  required_providers {

    # ## Connect "Azurerm" provider for creation and interaction with all major resources
    # ## Ref.:https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs
    azurerm = {
      source = "hashicorp/azurerm"
      version = "3.85.0"
    }

    # ## Connect "Random" provider for creation of random strings
    # ## Ref.: https://registry.terraform.io/providers/hashicorp/random/latest/docs
    random = {
      source = "hashicorp/random"
      version = "3.6.0"
    }
  }

}

############################
##  Terrafrorm Providers  ##
############################

# ## Connect "Azurerm" provider for creation and interaction with all major resources
# ## Terraform will automatically recover a soft-deleted Key Vault during Creation if one is found
# ##  this can be opt out using the features block within the Provider block.
provider "azurerm" {
  skip_provider_registration = true

  features {

  }
}