﻿namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;

public class DecisionEngineStepResult
{
    public string? Code { get; set; }

    public string? Scope { get; set; }

    public string? BankAccountIdentifier { get; set; }

    public string? OwnerIdentifier { get; set; }

    public string? OwnerSsnHash { get; set; }

    public string? ComparisonSource { get; set; }

    public string? ComparisonJustification { get; set; }

    public object? ComparisonValue { get; set; }

    public object? ThresholdValue { get; set; }

    public string? Result { get; set; }

    public string? ManualResult { get; set; }
    
    public string? ManualResultAt { get; set; }
    
    public string? ManualResultBy { get; set; }
    
    public string? ManualResultNote { get; set; }
}
