﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.Settings;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class SettingsRepository
        : GenericRepository<SettingDocument>, ISettingsRepository
{
    public SettingsRepository(IObsMongoDBContext context, ILogger<GenericRepository<SettingDocument>> logger) : base(context, logger)
    {
    }

    public async Task<SettingDocument?> GetSetting(string settingKey, CancellationToken cancellationToken)
    {
        var filter = Builders<SettingDocument>.Filter.Eq(x => x.Key, settingKey);
        var result = Context.GetCollection<SettingDocument>()
            .Find(filter);
        var settingsDocument = await result.FirstOrDefaultAsync(cancellationToken);

        return settingsDocument;
    }
}
