﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.LoanApplication;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class LoanApplicationRepository : GenericRepository<LoanApplicationDocument>, ILoanApplicationRepository
{
    public LoanApplicationRepository(IObsMongoDBContext context, ILogger<GenericRepository<LoanApplicationDocument>> logger) : base(context, logger)
    {
    }

    public Task<List<LoanApplicationDocument>> GetAllByFilters(GetLoanApplicationQuery query,
        CancellationToken ct)
    {
        Logger.LogInformation("Get loan applications by filters: {@Query}", query);
        var expression = Builders<LoanApplicationDocument>.Filter;
        var filter = Builders<LoanApplicationDocument>.Filter.Empty;
        if (query.Id is not null) filter &= expression.Eq(x => x.Id, query.Id);
        if (query.CompanyId is not null) filter &= expression.Eq(x => x.CompanyId, query.CompanyId);
        if (query.Status is not null) filter &= expression.Eq(x => x.Status, query.Status);
        if (query.AppDateFrom.HasValue) filter &= expression.Gte(x => x.UpdatedAt, query.AppDateFrom.Value);
        if (query.AppDateTo.HasValue) filter &= expression.Lt(x => x.UpdatedAt, query.AppDateTo.Value);
        if (query.IsInvoiceDetailsExists.HasValue && query.IsInvoiceDetailsExists.Value)
            filter &= expression.Ne(x => x.InvoiceDetails, null);

        var documents = Collection.Find(filter);
        return documents.ToListAsync(ct);
    }

    public async Task<LoanApplicationDocument?> GetFirstApprovedLegacyLoanByCompany(string companyId, CancellationToken ct)
    {
        Logger.LogInformation("Getting the first approved loan application for company id: {CompanyId}", companyId);

        var expression = Builders<LoanApplicationDocument>.Filter;
        var filter = expression.Eq(x => x.CompanyId, companyId) &
                     expression.Eq(x => x.Status, "approved") &
                     expression.Eq(x => x.CreditApplicationId, null) &
                     expression.Eq(x => x.DrawApprovalId, null);

        var document = await Collection
             .Find(filter)
             .SortByDescending(x => x.CreatedAt)
             .FirstOrDefaultAsync(ct);

        return document;
    }

    public async Task<List<LoanApplicationDocument>?> GetByCreditApplicationInfo(string creditApplicationId, string einHash, CancellationToken ct)
    {
        var filterBuilder = Builders<LoanApplicationDocument>.Filter;

        var filters = new List<FilterDefinition<LoanApplicationDocument>>();

        if (!string.IsNullOrEmpty(creditApplicationId)) filters.Add(filterBuilder.Eq(x => x.CreditApplicationId, creditApplicationId));
        if (!string.IsNullOrEmpty(einHash)) filters.Add(filterBuilder.Eq("draft.businessInfo_ein.hash", einHash));
        if (!filters.Any()) return null;

        var combinedFilter = filterBuilder.Or(filters);
        var documents = Collection.Find(combinedFilter);

        return await documents.ToListAsync(ct);
    }

    public async Task<LoanApplicationDocument?> GetByInvoicesIds(IEnumerable<string> invoiceIds, CancellationToken ct)
    {
        var filter = Builders<LoanApplicationDocument>.Filter.In("invoiceDetails.invoiceId", invoiceIds);
        var sort = Builders<LoanApplicationDocument>.Sort.Descending("createdAt");

        var document = await Collection.Find(filter).Sort(sort).FirstOrDefaultAsync(ct);
        return document;
    }

    public async Task UpsertOutputDocument(string loanApplicationId, OutputDocument outputDocument,
        CancellationToken ct)
    {
        var filter = Builders<LoanApplicationDocument>.Filter.Eq(x => x.Id, loanApplicationId);

        var loanApplication = await Context.GetCollection<LoanApplicationDocument>()
            .Find(filter)
            .FirstOrDefaultAsync(ct);

        if (loanApplication == null)
        {
            throw new VariableNullException($"Loan application with ID {loanApplicationId} not found.");
        }

        var existingOutput = loanApplication.Outputs?
            .FirstOrDefault(o => o.Step == outputDocument.Step);

        if (existingOutput != null)
        {
            var arrayFilter = Builders<LoanApplicationDocument>.Filter.And(
                Builders<LoanApplicationDocument>.Filter.Eq(x => x.Id, loanApplicationId),
                Builders<LoanApplicationDocument>.Filter.Eq($"{nameof(LoanApplicationDocument.Outputs)}.Step", outputDocument.Step)
            );

            var update = Builders<LoanApplicationDocument>.Update
                .Set($"{nameof(LoanApplicationDocument.Outputs)}.$", outputDocument);

            await Context.GetCollection<LoanApplicationDocument>()
                .UpdateOneAsync(arrayFilter, update, cancellationToken: ct);
        }
        else
        {
            var update = Builders<LoanApplicationDocument>.Update
                .Push(x => x.Outputs, outputDocument);

            await Context.GetCollection<LoanApplicationDocument>()
                .UpdateOneAsync(filter, update, cancellationToken: ct);
        }
    }

    public async Task<BsonDocument> GetRejectedLoanApplicationsCount(string companyId, string status,
        CancellationToken ct)
    {
        var failedPipeline = new[]
        {
            Builders<LoanApplicationDocument>.Filter.Eq(x => x.CompanyId, companyId),
            Builders<LoanApplicationDocument>.Filter.Eq(x => x.Status, status),
            Builders<LoanApplicationDocument>.Filter.Gt(x => x.UpdatedAt, DateTime.UtcNow.AddMonths(-6))
        };

        var failedGroup = new BsonDocument
        {
            { "_id", "$company_id" },
            { "count", new BsonDocument("$sum", 1) }
        };

        var failed = await Collection.Aggregate()
            .Match(Builders<LoanApplicationDocument>.Filter.And(failedPipeline))
            .Group(failedGroup)
            .FirstOrDefaultAsync(ct);

        return failed;
    }

    public Task<BsonDocument> GetTotalOutstandingApprovedLoanAmount(string companyId,
        CancellationToken ct)
    {
        var businessOutstandingPipeline = new[]
        {
            Builders<LoanApplicationDocument>.Filter.Eq(x => x.CompanyId, companyId),
            Builders<LoanApplicationDocument>.Filter.Eq(x => x.Status, LoanApplicationStatusConstants.Approved),
            Builders<LoanApplicationDocument>.Filter.Ne(x => x.InvoiceDetails, null)
        };

        return Collection.Aggregate()
            .Match(Builders<LoanApplicationDocument>.Filter.And(businessOutstandingPipeline))
            .Group(new BsonDocument
            {
                { "_id", "$company_id" },
                {
                    "total",
                    new BsonDocument("$sum",
                        new BsonDocument("$ifNull", new BsonArray { "$remainingAmount", "$approvedAmount" }))
                }
            })
            .FirstOrDefaultAsync(ct);
    }

    public Task<List<LoanApplicationDocument>> GetApprovedLoanApplicationsWithOutstandingAmount(string companyId,
        string status, CancellationToken ct)
    {
        var lateAppsPipeline = new[]
        {
            Builders<LoanApplicationDocument>.Filter.Eq(x => x.CompanyId, companyId),
            Builders<LoanApplicationDocument>.Filter.Eq(x => x.Status, status),
            Builders<LoanApplicationDocument>.Filter.Gt(x => x.AmountDue, 0)
        };

        return Collection.Aggregate()
            .Match(Builders<LoanApplicationDocument>.Filter.And(lateAppsPipeline))
            .ToListAsync(ct);
    }

    public Task<List<BsonDocument>> GetDocumentsByEinHashWithPipeline(List<BsonDocument> lookupPipeline,
        List<PipelineStageDefinition<LoanApplicationDocument, BsonDocument>>? prePipeline, string einHash,
        CancellationToken ct)
    {

        var aggregateFluent = Collection.Aggregate()
            .AppendStage<BsonDocument>(new BsonDocument("$match",
                new BsonDocument("draft.businessInfo_ein.hash", einHash)));

        if (prePipeline != null)
        {
            foreach (var item in prePipeline)
            {
                aggregateFluent = aggregateFluent.AppendStage<BsonDocument>(item.ToString());
            }
        }

        foreach (var stage in lookupPipeline)
        {
            aggregateFluent = aggregateFluent.AppendStage<BsonDocument>(stage);
        }

        return aggregateFluent.ToListAsync(ct);
    }

    public Task<List<BsonDocument>> GetDocumentsBySsnHashWithPipeline(string ssnHash,
        List<PipelineStageDefinition<LoanApplicationDocument, BsonDocument>>? prePipeline,
        List<BsonDocument> lookupPipeline, CancellationToken ct)
    {
        var aggregateFluent = Collection.Aggregate()
            .AppendStage<BsonDocument>(new BsonDocument("$match", new BsonDocument("$or", new BsonArray
            {
                new BsonDocument("draft.businessOwner_ssn.hash", ssnHash),
                new BsonDocument("draft.personalInfo_ssn.hash", ssnHash),
                new BsonDocument("draft.coOwnerInfo_coOwner1.hash", ssnHash),
                new BsonDocument("draft.coOwnerInfo_coOwner2.hash", ssnHash),
                new BsonDocument("draft.coOwnerInfo_coOwner3.hash", ssnHash)
            })));

        if (prePipeline != null)
        {
            foreach (var item in prePipeline)
            {
                aggregateFluent = aggregateFluent.AppendStage<BsonDocument>(
                    new BsonDocument("$addFields", new BsonDocument(item.OperatorName, new BsonString("value"))));
            }
        }

        foreach (var stage in lookupPipeline)
        {
            aggregateFluent = aggregateFluent.AppendStage<BsonDocument>(stage);
        }

        return aggregateFluent.ToListAsync(ct);
    }

    public Task UpdateManyByIds(IEnumerable<string> loanApplicationIds, UpdateLoanApplicationDocument updateLoanApplication, CancellationToken cancellationToken)
    {
        if (!loanApplicationIds.Any()) return Task.CompletedTask;
        var collection = Context.GetCollection<LoanApplicationDocument>();

        var filterBuilder = new FilterDefinitionBuilder<LoanApplicationDocument>();
        var filter = filterBuilder.Where(x => loanApplicationIds.Contains(x.Id));

        var updateDefinition = BuildUpdateDefinition(updateLoanApplication);

        return collection.UpdateManyAsync(filter, updateDefinition, null, cancellationToken);
    }

    public async Task<LoanApplicationDocument?> GetByDrawId(string id, CancellationToken ct)
    {
        var filterBuilder = Builders<LoanApplicationDocument>.Filter.Eq(x => x.DrawApprovalId, id);
        var result = await Collection.Find(filterBuilder).FirstOrDefaultAsync(ct);

        return result;
    }

    private static UpdateDefinition<LoanApplicationDocument> BuildUpdateDefinition(UpdateLoanApplicationDocument updateLoanApplication)
    {
        var updateBuilder = new UpdateDefinitionBuilder<LoanApplicationDocument>();
        var updateDefinition = updateBuilder
            .Set(x => x.UpdatedAt, DateTime.UtcNow);

        if (!string.IsNullOrEmpty(updateLoanApplication.CompanyId))
            updateDefinition = updateDefinition.Set(x => x.CompanyId, updateLoanApplication.CompanyId);

        if (updateLoanApplication.InvoiceDetails != null)
            updateDefinition = updateDefinition.Set(x => x.InvoiceDetails, updateLoanApplication.InvoiceDetails);

        if (updateLoanApplication.Outputs != null)
            updateDefinition = updateDefinition.Set(x => x.Outputs, updateLoanApplication.Outputs);

        if (!string.IsNullOrEmpty(updateLoanApplication.Status))
            updateDefinition = updateDefinition.Set(x => x.Status, updateLoanApplication.Status);

        if (updateLoanApplication.PrevOutputs != null)
            updateDefinition = updateDefinition.Set(x => x.Outputs, updateLoanApplication.PrevOutputs);

        if (updateLoanApplication.AmountDue.HasValue)
            updateDefinition = updateDefinition.Set(x => x.AmountDue, updateLoanApplication.AmountDue);

        if (!string.IsNullOrEmpty(updateLoanApplication.CreditApplicationId))
            updateDefinition = updateDefinition.Set(x => x.CreditApplicationId, updateLoanApplication.CreditApplicationId);

        if (!string.IsNullOrEmpty(updateLoanApplication.DrawApprovalId))
            updateDefinition = updateDefinition.Set(x => x.DrawApprovalId, updateLoanApplication.DrawApprovalId);

        if (updateLoanApplication.SubmitDate.HasValue)
            updateDefinition = updateDefinition.Set(x => x.SubmitDate, updateLoanApplication.SubmitDate);

        if (updateLoanApplication.DecisionDate.HasValue)
            updateDefinition = updateDefinition.Set(x => x.DecisionDate, updateLoanApplication.DecisionDate);

        if (!string.IsNullOrEmpty(updateLoanApplication.ApprovedBy))
            updateDefinition = updateDefinition.Set(x => x.ApprovedBy, updateLoanApplication.ApprovedBy);

        if (updateLoanApplication.ApprovedAmount.HasValue)
            updateDefinition = updateDefinition.Set(x => x.ApprovedAmount, updateLoanApplication.ApprovedAmount);

        if (updateLoanApplication.IsSentBack.HasValue)
            updateDefinition = updateDefinition.Set(x => x.IsSentBack, updateLoanApplication.IsSentBack);

        if (updateLoanApplication.Notes != null)
            updateDefinition = updateDefinition.Set(x => x.Notes, updateLoanApplication.Notes);

        if (updateLoanApplication.UpdatedAt.HasValue)
            updateDefinition = updateDefinition.Set(x => x.UpdatedAt, updateLoanApplication.UpdatedAt);

        if (updateLoanApplication.UpdatedAtByCompatibility.HasValue)
            updateDefinition = updateDefinition.Set(x => x.UpdatedAtByCompatibility, updateLoanApplication.UpdatedAtByCompatibility);

        return updateDefinition;
    }
}
