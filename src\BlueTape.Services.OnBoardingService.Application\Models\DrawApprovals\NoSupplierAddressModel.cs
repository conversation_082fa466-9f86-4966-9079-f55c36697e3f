﻿using System.Text.Json.Serialization;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class NoSupplierAddressModel
{
    [JsonPropertyName("address")]
    public string? Address { get; set; }
    [JsonPropertyName("city")]
    public string? City { get; set; }
    [JsonPropertyName("state")]
    public string? State { get; set; }
    [JsonPropertyName("zip")]
    public string? Zip { get; set; }
}

