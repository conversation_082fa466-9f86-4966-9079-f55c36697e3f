﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.PaymentPlans;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class LoanPaymentPlanService(ILoanPaymentPlanRepository repository, IMapper mapper) : ILoanPaymentPlanService
{
    public async Task<List<LoanPaymentPlan>> Get(CancellationToken ctx)
    {
        var documents = await repository.GetAll(ctx);

        return mapper.Map<List<LoanPaymentPlan>>(documents);
    }
}
