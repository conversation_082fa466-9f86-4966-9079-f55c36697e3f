﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DecisionEngineSteps;
using BlueTape.Utilities.Providers;
using Serilog;
using System.Collections.Immutable;
using System.Text.RegularExpressions;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class DecisionEngineStepsService : IDecisionEngineStepsService
{
    private readonly IDecisionEngineStepsRepository _decisionEngineStepsRepository;
    private readonly ICreditApplicationRepository _creditApplicationRepository;
    private readonly IMapper _mapper;
    private readonly ICreditApplicationNotesService _creditApplicationNotesService;
    private readonly IAccountAuthorizationsService _accountAuthorizationsService;
    private readonly IDrawApprovalRepository _drawApprovalRepository;
    private readonly IDateProvider _dateProvider;

    public DecisionEngineStepsService(IDecisionEngineStepsRepository decisionEngineStepsRepository,
        ICreditApplicationRepository creditApplicationRepository,
        IMapper mapper,
        ICreditApplicationNotesService creditApplicationNotesService,
        IAccountAuthorizationsService accountAuthorizationsService,
        IDateProvider dateProvider,
        IDrawApprovalRepository drawApprovalRepository)
    {
        _decisionEngineStepsRepository = decisionEngineStepsRepository;
        _creditApplicationRepository = creditApplicationRepository;
        _dateProvider = dateProvider;
        _mapper = mapper;
        _creditApplicationNotesService = creditApplicationNotesService;
        _accountAuthorizationsService = accountAuthorizationsService;
        _drawApprovalRepository = drawApprovalRepository;
    }

    public async Task<DecisionEngineSteps> Create(CreateDecisionEngineSteps createModel, CancellationToken ct)
    {
        Log.Information("Started step creation process. Started previous step search for credit application {id}", createModel.CreditApplicationId);

        ValidateDecisionEngineStepCreate(createModel);

        var previousSteps = new List<DecisionEngineStepsDocument>();
        if (!string.IsNullOrEmpty(createModel.CreditApplicationId))
        {
            previousSteps = (await _decisionEngineStepsRepository.GetByCreditApplicationId(createModel.CreditApplicationId, ct)).ToList();
        }
        else if (!string.IsNullOrEmpty(createModel.AccountAuthorizationDetailsId))
        {
            previousSteps = (await _decisionEngineStepsRepository.GetByAccountAuthorizationId(createModel.AccountAuthorizationDetailsId, ct)).ToList();
        }
        else if (!string.IsNullOrEmpty(createModel.DrawApprovalId))
        {
            previousSteps = (await _decisionEngineStepsRepository.GetByDrawApprovalId(createModel.DrawApprovalId, ct)).ToList();
        }

        var previousStep = previousSteps.MaxBy(x => x.CreatedAt)?.Step ?? string.Empty;
        createModel.PreviousStep = previousStep;

        Log.Information("Found previous step {previousStep} for credit application {id}", previousStep, createModel.CreditApplicationId);

        var decisionEngineStepsDocument = _mapper.Map<DecisionEngineStepsDocument>(createModel);

        if (string.IsNullOrEmpty(decisionEngineStepsDocument.Status))
        {
            decisionEngineStepsDocument.Status = "started";
        }

        var decisionEngineSteps = await _decisionEngineStepsRepository.Add(decisionEngineStepsDocument, ct);

        Log.Information("Finished step creation process for credit application {id}. Step id {stepId}",
            createModel.CreditApplicationId, decisionEngineSteps.Id);

        return _mapper.Map<DecisionEngineSteps>(decisionEngineSteps);
    }

    public async Task<IEnumerable<DecisionEngineSteps>> GetByCreditApplicationId(string creditApplicationId, CancellationToken ct)
    {
        return _mapper.Map<IEnumerable<DecisionEngineSteps>>(
            await _decisionEngineStepsRepository.GetByCreditApplicationId(creditApplicationId, ct));
    }

    public async Task<IEnumerable<DecisionEngineSteps>> GetByQuery(DecisionEngineStepsQueryModel query, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(query.CompanyId))
        {
            var creditApplications = await _creditApplicationRepository.GetAllByFilters(new GetCreditApplicationQuery()
            {
                CompanyId = query.CompanyId
            }, ct);
            var mergedIds = query.CreditApplicationIds?
                .Union(creditApplications.Select(x => x.Id))
                .ToArray();

            query.CreditApplicationIdsForCompanyFilter = mergedIds;

            var accAuth = await _accountAuthorizationsService.GetAllByFilters(null, query.CompanyId, null, null, ct);

            query.AccountAuthorizationDetailIdsForCompanyFilter = accAuth.Select(x => x.Id!).ToArray();
        }

        return _mapper.Map<IEnumerable<DecisionEngineSteps>>(await _decisionEngineStepsRepository.GetByFilters(_mapper.Map<DecisionEngineStepsQuery>(query), ct));
    }

    public Task ReinstateDecisionEngineRule(string id, string? identifier, string userId,
        ReinstateDecisionEngineRuleModel reinstateModel, CancellationToken ct)
    {
        return ProcessDecisionEngineRule(id, reinstateModel.RuleCode, identifier, userId, reinstateModel.Note, DecisionEngineRuleExecutionFlow.Reinstate, ct,
            ClearExecutionResultManualOverrideFields);
    }

    public Task IgnoreDecisionEngineRule(string id, string? identifier, string userId,
        IgnoreDecisionEngineRuleModel ignoreModel, CancellationToken ct)
    {
        return ProcessDecisionEngineRule(id, ignoreModel.RuleCode, identifier, userId, ignoreModel.Note, DecisionEngineRuleExecutionFlow.Ignore, ct,
               (result) => FillExecutionResultManualOverrideFields(result, userId, ignoreModel.Note));
    }

    private void ClearExecutionResultManualOverrideFields(DecisionEngineStepResultDocument decisionEngineStepResultDocument)
    {
        decisionEngineStepResultDocument.ManualResult = null;
        decisionEngineStepResultDocument.ManualResultAt = null;
        decisionEngineStepResultDocument.ManualResultBy = null;
        decisionEngineStepResultDocument.ManualResultNote = null;
    }

    private void FillExecutionResultManualOverrideFields(DecisionEngineStepResultDocument decisionEngineStepResultDocument,
    string userId, string note)
    {
        decisionEngineStepResultDocument.ManualResult = AutomatedDecisionResult.Pass.ToString();
        decisionEngineStepResultDocument.ManualResultAt = _dateProvider.CurrentDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
        decisionEngineStepResultDocument.ManualResultBy = userId;
        decisionEngineStepResultDocument.ManualResultNote = note;
    }

    private async Task ProcessDecisionEngineRule(
    string id, string ruleCode, string? identifier, string userId, string note, DecisionEngineRuleExecutionFlow flow,
    CancellationToken ct, Action<DecisionEngineStepResultDocument> processResult)
    {
        var baseRuleCode = ruleCode;

        ruleCode = ExtractBaseRuleCode(ruleCode);

        if (!IsValidRuleCode(ruleCode))
            throw new ValidationException($"Rule code:{ruleCode} cannot be ignored or reinstated");

        var relatedRules = GetRelatedRules(ruleCode);
        relatedRules = relatedRules.Any() ? relatedRules : GetRelatedRules(baseRuleCode);

        var accountAuthDetails = await _accountAuthorizationsService.GetAll(x => x.CompanyId == id, ct);
        var accountAuthDetailsIds = accountAuthDetails.Select(x => x.Id).ToList();

        var scheduledSteps = accountAuthDetailsIds.Any()
            ? await _decisionEngineStepsRepository.GetAll(x => accountAuthDetailsIds.Contains(x.AccountAuthorizationDetailsId), ct)
            : Enumerable.Empty<DecisionEngineStepsDocument>();

        var latestSteps = GetLatestCompanySteps(scheduledSteps)
            .Where(x => x is { ExecutionType: "Scheduled", Results: not null });

        foreach (var latestStep in latestSteps)
        {
            var resultsToProcess = latestStep.Results?.Where(r =>
                relatedRules.Any(targetRule => r.Code!.Contains(targetRule)));

            if (!string.IsNullOrEmpty(identifier))
            {
                resultsToProcess = resultsToProcess?.Where(r =>
                    string.Equals(r.OwnerIdentifier, identifier, StringComparison.CurrentCultureIgnoreCase) ||
                    string.Equals(r.BankAccountIdentifier, identifier, StringComparison.CurrentCultureIgnoreCase));
            }

            if (resultsToProcess == null || !resultsToProcess.Any()) continue;

            foreach (var result in resultsToProcess)
            {
                processResult(result);
            }

            await _decisionEngineStepsRepository.Update(latestStep, ct);
        }

        var creditApplicationIds = accountAuthDetails
            .SelectMany(x => x.CreditApplicationIds ?? Enumerable.Empty<string>())
            .Distinct();

        await GenerateCreditApplicationNotes(userId, note, ruleCode, flow, creditApplicationIds, ct);
    }

    private HashSet<string> GetRelatedRules(string code)
    {
        if (string.IsNullOrEmpty(code))
            return [];

        var result = DecisionEngineStepsConstants.RuleCodes
            .GroupBy(kv => kv.Value)
            .Where(g => g.Any(kv => string.Equals(kv.Key, code, StringComparison.InvariantCultureIgnoreCase)))
            .SelectMany(g => g.Select(kv => kv.Key))
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        return result.Any() ? result : [];
    }

    public async Task<IEnumerable<DecisionEngineSteps>> GetByCompanyId(string companyId, CreditApplicationType? creditApplicationType, CancellationToken ct)
    {
        ArgumentException.ThrowIfNullOrEmpty(companyId);

        var creditApplicationIds = (await _creditApplicationRepository.GetAllByFilters(new GetCreditApplicationQuery()
        {
            CompanyId = companyId,
            Type = creditApplicationType != null ? new[] { creditApplicationType.ToString()! } : null
        }, ct)).Select(x => x.Id).ToArray();

        var accAuth = (await _accountAuthorizationsService.GetAllByFilters(null, companyId, null, null, ct)).MaxBy(x => x.CreatedAt);
        var drawApprovalsIds = creditApplicationType.HasValue ? Array.Empty<string>()
            : (await _drawApprovalRepository.GetAll(x => x.CompanyId == companyId, ct)).Select(x => x.Id).ToArray();

        var drawApprovalSteps = drawApprovalsIds.Length != 0
            ? await _decisionEngineStepsRepository.GetByDrawApprovalIds(drawApprovalsIds, ct)
            : Enumerable.Empty<DecisionEngineStepsDocument>();

        var creditAppSteps = creditApplicationIds.Length != 0
            ? await _decisionEngineStepsRepository.GetByCreditApplicationIds(creditApplicationIds, ct)
            : Enumerable.Empty<DecisionEngineStepsDocument>();

        var scheduledSteps = !string.IsNullOrEmpty(accAuth?.Id)
            ? await _decisionEngineStepsRepository.GetByAccountAuthorizationId(accAuth.Id, ct)
            : Enumerable.Empty<DecisionEngineStepsDocument>();

        var companySteps = creditAppSteps.Union(scheduledSteps).Union(drawApprovalSteps);
        return _mapper.Map<IEnumerable<DecisionEngineSteps>>(GetLatestCompanySteps(companySteps));
    }

    public async Task<IEnumerable<DecisionEngineSteps>> GetByExecutionId(string executionId, CancellationToken ct)
        => _mapper.Map<IEnumerable<DecisionEngineSteps>>(await _decisionEngineStepsRepository.GetByExecutionId(executionId, ct));

    public async Task<Dictionary<string, IEnumerable<DecisionEngineSteps>>> GetLatestByCompanyIds(string[] companyIds, CancellationToken ct)
    {
        var dictionary = new Dictionary<string, IEnumerable<DecisionEngineSteps>>();
        if (companyIds.Length == 0) return dictionary;
        var creditApplications = await _creditApplicationRepository.GetLightCreditApplications(companyIds, ct);
        var creditApplicationIds = creditApplications.Select(creditApplication => creditApplication.Id).ToArray();
        var drawApprovals = (await _drawApprovalRepository.GetAll(x => companyIds.Contains(x.CompanyId), ct));
        var drawApprovalIds = drawApprovals.Select(x => x.Id).ToArray();
        var accountAuthDetails = (await _accountAuthorizationsService.GetAll(x => companyIds.Contains(x.CompanyId), ct));
        var accountAuthDetailsIds = accountAuthDetails.Select(x => x.Id);

        var creditAppSteps = await _decisionEngineStepsRepository.GetByCreditApplicationIds(creditApplicationIds, ct);
        var scheduledSteps = accountAuthDetailsIds.Any()
            ? await _decisionEngineStepsRepository.GetAll(x => accountAuthDetailsIds.Contains(x.AccountAuthorizationDetailsId), ct)
            : Enumerable.Empty<DecisionEngineStepsDocument>();
        var drawApprovalSteps = drawApprovalIds.Any()
            ? await _decisionEngineStepsRepository.GetByDrawApprovalIds(drawApprovalIds, ct)
            : Enumerable.Empty<DecisionEngineStepsDocument>();

        var companiesSteps = creditAppSteps.Union(scheduledSteps).Union(drawApprovalSteps);
        var steps = _mapper.Map<IEnumerable<DecisionEngineSteps>>(companiesSteps);

        var companyStepsGroups = steps.GroupBy(step =>
        {
            var companyId = string.Empty;
            companyId = creditApplications
                .FirstOrDefault(creditApp => step.CreditApplicationId == creditApp.Id)?
                .CompanyId;
            if (string.IsNullOrEmpty(companyId))
            {
                companyId = accountAuthDetails
                    .FirstOrDefault(accountAuthDetails => step.AccountAuthorizationDetailsId == accountAuthDetails.Id)?
                    .CompanyId;
            }
            if (string.IsNullOrEmpty(companyId))
            {
                companyId = drawApprovals
                    .FirstOrDefault(drawApproval => step.DrawApprovalId == drawApproval.Id)?
                    .CompanyId;
            }

            return companyId;
        });

        foreach (var groupedSteps in companyStepsGroups)
        {
            if (string.IsNullOrEmpty(groupedSteps.Key)) continue;

            var latestSteps = GetLatestCompanySteps(groupedSteps);
            dictionary.Add(groupedSteps.Key, latestSteps);
        }

        return dictionary;
    }

    public async Task<IEnumerable<DecisionEngineSteps>> GetByDrawApprovalId(string drawApprovalId, CancellationToken ct)
    {
        return _mapper.Map<IEnumerable<DecisionEngineSteps>>(
            await _decisionEngineStepsRepository.GetByDrawApprovalId(drawApprovalId, ct));
    }

    public async Task<DecisionEngineSteps> GetById(string id, CancellationToken ct)
    {
        return _mapper.Map<DecisionEngineSteps>(await _decisionEngineStepsRepository.GetById(id, ct));
    }

    public async Task<DecisionEngineSteps> Update(UpdateDecisionEngineSteps updateModel, CancellationToken ct)
    {
        Log.Information("Started step update process for step {id}", updateModel.Id);

        var decisionEngineSteps = await _decisionEngineStepsRepository.GetById(updateModel.Id!, ct);
        var newStepsResults = _mapper.Map<List<DecisionEngineStepResultDocument>>(updateModel.Results);

        decisionEngineSteps.Results = decisionEngineSteps.Results == null
            ? newStepsResults
            : decisionEngineSteps.Results.Concat(newStepsResults);
        if (updateModel.Thresholds != null && updateModel.Thresholds.Any())
        {
            decisionEngineSteps.Thresholds = _mapper.Map<IEnumerable<DecisionEngineThresholdDocument>>(updateModel.Thresholds);
        }
        decisionEngineSteps.Status = updateModel.NewStatus;
        decisionEngineSteps.UpdatedBy = updateModel.UpdatedBy;

        var result = await _decisionEngineStepsRepository.Update(decisionEngineSteps, ct);

        Log.Information("Finished step update process for step {id}", updateModel.Id);

        return _mapper.Map<DecisionEngineSteps>(result);
    }

    private static IEnumerable<DecisionEngineSteps> GetLatestCompanySteps(IGrouping<string?, DecisionEngineSteps> groupedSteps)
    {
        var latestSteps = groupedSteps
            .GroupBy(x => x.Step)
            .Select(group => group
                .MaxBy(step => step.UpdatedAt));
        return latestSteps!;
    }

    private static IEnumerable<DecisionEngineStepsDocument> GetLatestCompanySteps(IEnumerable<DecisionEngineStepsDocument> steps)
    {
        var latestSteps = new Dictionary<string, DecisionEngineStepsDocument>(StringComparer.OrdinalIgnoreCase);

        foreach (var step in steps)
        {
            if (string.IsNullOrEmpty(step.Step)) continue;
            if (!latestSteps.TryGetValue(step.Step, out var existingStep) || step.UpdatedAt > existingStep.UpdatedAt)
            {
                latestSteps[step.Step] = step;
            }
        }

        return latestSteps.Values;
    }

    private static void ValidateDecisionEngineStepCreate(CreateDecisionEngineSteps createModel)
    {
        if (string.IsNullOrEmpty(createModel.CreditApplicationId) && string.IsNullOrEmpty(createModel.DrawApprovalId)
                                                                  && string.IsNullOrEmpty(createModel.AccountAuthorizationDetailsId))
            throw new ValidationException("Cannot create step when neither CreditApplicationId, DrawApprovalId, or AccountAuthorizationDetailsId is provided");

        if (!string.IsNullOrEmpty(createModel.CreditApplicationId) && !string.IsNullOrEmpty(createModel.DrawApprovalId))
            throw new ValidationException("Cannot create step for both CreditApplicationId and DrawApprovalId");

        if (!string.IsNullOrEmpty(createModel.CreditApplicationId) && !string.IsNullOrEmpty(createModel.AccountAuthorizationDetailsId))
            throw new ValidationException("Cannot create step for both CreditApplicationId and AccountAuthorizationDetailsId");

        if (!string.IsNullOrEmpty(createModel.DrawApprovalId) && !string.IsNullOrEmpty(createModel.AccountAuthorizationDetailsId))
            throw new ValidationException("Cannot create step for both DrawApprovalId and AccountAuthorizationDetailsId");
    }

    private bool IsValidRuleCode(string ruleCode)
    {
        return DecisionEngineStepsConstants.RuleCodes.Keys.Any(r => r.Contains(ruleCode, StringComparison.OrdinalIgnoreCase));
    }

    private string ExtractBaseRuleCode(string ruleCode)
    {
        if (string.IsNullOrWhiteSpace(ruleCode))
            return ruleCode;

        Match match = Regex.Match(ruleCode, DecisionEngineStepsConstants.MatchPattern);
        return match.Success ? match.Groups[1].Value : ruleCode.Trim();
    }

    private Task GenerateCreditApplicationNotes(string userId, string note, string ruleCode,
       DecisionEngineRuleExecutionFlow flow, IEnumerable<string> creditApplicationIds, CancellationToken ct)
    {
        var createCreditApplicationNotes = new List<CreateCreditApplicationNote>();

        foreach (var creditApplicationId in creditApplicationIds)
        {
            var ruleCaption = DecisionEngineStepsConstants.RuleCodes
               .FirstOrDefault(r => string.Equals(r.Key, ruleCode, StringComparison.InvariantCultureIgnoreCase)).Value;

            var caption = ruleCaption + (flow.Equals(DecisionEngineRuleExecutionFlow.Ignore)
                ? DecisionEngineStepsConstants.Ignored
                : DecisionEngineStepsConstants.Reinstated);

            createCreditApplicationNotes.Add(new CreateCreditApplicationNote()
            {
                Caption = caption,
                CreditApplicationId = creditApplicationId,
                CreatedAt = _dateProvider.CurrentDateTime,
                CreatedBy = userId,
                Note = note
            });
        }

        return _creditApplicationNotesService.AddRange(createCreditApplicationNotes, ct);
    }
}