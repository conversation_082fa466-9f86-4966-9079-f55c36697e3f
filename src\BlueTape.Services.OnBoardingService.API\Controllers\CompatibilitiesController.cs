﻿using BlueTape.InvoiceService.Messages;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Models.Compatibility;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.Compatibility)]
[ApiController]
public class CompatibilitiesController(
    ICompatibilityService compatibilityService,
    ILoanApplicationSyncMessageSender messageSender,
    IInvoiceSyncMessageSender invoiceMessageSender,
    IDrawApprovalService drawApprovalService) : ControllerBase
{
    [HttpPost("/sync-credit-application")]
    public async Task SyncCreditApplication(string creditApplicationId, CancellationToken cancellationToken)
    {
        await compatibilityService.SyncCreditApplication(creditApplicationId, cancellationToken);
    }

    [HttpPost("/sync-draw-approval")]
    public async Task SyncDrawApproval(string drawApprovalId, CancellationToken cancellationToken)
    {
        await compatibilityService.SyncDrawApproval(drawApprovalId, cancellationToken);
    }

    [HttpPost("/sync-loan-application")]
    public async Task SyncLoanApplication(string loanApplicationId, CancellationToken cancellationToken)
    {
        await compatibilityService.SyncLoanApplication(loanApplicationId, cancellationToken);
    }

    [HttpPost("/call-sync-message-sender")]
    public async Task SendMessage(SyncLoanApplicationMessagePayload payload, CancellationToken cancellationToken)
    {
        await messageSender.SendMessage(new ServiceBusMessageBt<SyncLoanApplicationMessagePayload>(payload), cancellationToken);
    }

    [HttpPost("/call-invoice-sync-message-sender")]
    public async Task SendMessage(SyncInvoiceMessagePayload payload, CancellationToken cancellationToken)
    {
        await invoiceMessageSender.SendMessage(new ServiceBusMessageBt<SyncInvoiceMessagePayload>(payload), cancellationToken);
    }

    [HttpPost("/sync-loan-application-by-company-id/{companyId}")]
    public async Task SyncLoanApplicationsByCompanyId(string companyId, CancellationToken cancellationToken)
    {
        await compatibilityService.SyncLoanApplicationsByCompanyId(companyId, cancellationToken);
    }

    [HttpPost("/sync-draw-approval-changes/{drawApprovalId}")]
    public async Task SyncDrawApprovalChanges(string drawApprovalId, CancellationToken cancellationToken)
    {
        await drawApprovalService.SyncDrawApprovalChanges(drawApprovalId, cancellationToken);
    }

    [HttpPost("/send-connector-event/{drawApprovalId}")]
    public async Task TestSendConnectorEvent(string drawApprovalId, CancellationToken cancellationToken)
    {
        await compatibilityService.SendConnectorEvent(drawApprovalId, cancellationToken);
    }
}
