﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32630.192
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.API", "BlueTape.Services.OnBoardingService.API\BlueTape.Services.OnBoardingService.API.csproj", "{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.Application", "BlueTape.Services.OnBoardingService.Application\BlueTape.Services.OnBoardingService.Application.csproj", "{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.Infrastructure", "BlueTape.Services.OnBoardingService.Infrastructure\BlueTape.Services.OnBoardingService.Infrastructure.csproj", "{EB05F8E5-18C3-41F1-A85E-D98CBB9C9552}"
	ProjectSection(ProjectDependencies) = postProject
		{ADED75CF-BEA6-4A8C-A434-73DAB14641EC} = {ADED75CF-BEA6-4A8C-A434-73DAB14641EC}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.Domain", "BlueTape.Services.OnBoardingService.Domain\BlueTape.Services.OnBoardingService.Domain.csproj", "{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.DataAccess", "BlueTape.Services.OnBoardingService.DataAccess\BlueTape.Services.OnBoardingService.DataAccess.csproj", "{FFB1D083-24EB-4777-ABDB-103935B9E2AE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{C5C54711-68E9-4992-83F0-DC2C3EB90D04}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.Application.Tests", "Tests\BlueTape.Services.OnBoardingService.Application.Tests\BlueTape.Services.OnBoardingService.Application.Tests.csproj", "{04C8B807-B9A8-49C3-BD49-C9269EF4635E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.API.Tests", "BlueTape.Services.OnBoardingService.API.Tests\BlueTape.Services.OnBoardingService.API.Tests.csproj", "{88A49063-C283-41B9-96E8-6A9B23FE79C0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget", "nuget", "{A4EDAD8C-C2BE-42D0-9664-806E03EBD336}"
	ProjectSection(SolutionItems) = preProject
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.DataAccess.LMS", "BlueTape.Services.OnBoardingService.DataAccess.LMS\BlueTape.Services.OnBoardingService.DataAccess.LMS.csproj", "{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.DataAccess.InvoiceService", "BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.csproj", "{071D5773-F59C-4C47-897E-624FCD8850B1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.DataAccess.CompanyService", "BlueTape.Services.OnBoardingService.DataAccess.CompanyService\BlueTape.Services.OnBoardingService.DataAccess.CompanyService.csproj", "{28148A78-1BF4-4390-AF25-586C66EC4D97}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.Compatibility", "BlueTape.Services.OnBoardingService.Compatibility\BlueTape.Services.OnBoardingService.Compatibility.csproj", "{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.OnBoardingJob", "Functions\BlueTape.Functions.OnBoardingJob\BlueTape.Functions.OnBoardingJob.csproj", "{6B6F19A4-05D8-4BB0-B75C-7144E056D087}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.AuthorizationDetailsRefreshDetector", "Functions\BlueTape.Functions.AuthorizationDetailsRefreshDetector\BlueTape.Functions.AuthorizationDetailsRefreshDetector.csproj", "{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.Hosting", "BlueTape.Functions.Hosting\BlueTape.Functions.Hosting.csproj", "{F0478359-F8EE-4D8B-BE45-D70C72FA5727}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Functions", "Functions", "{C0B42771-0206-4DC7-95B9-19E9EC3DD7BF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Functions.AuthorizationDetailsRefreshService", "Functions\BlueTape.Functions.AuthorizationDetailsRefreshService\BlueTape.Functions.AuthorizationDetailsRefreshService.csproj", "{D6DC8ED4-A89B-4091-86A3-6BADE959A547}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.RefreshDetectorService", "BlueTape.Services.OnBoardingService.RefreshDetectorService\BlueTape.Services.OnBoardingService.RefreshDetectorService.csproj", "{94CC420E-5E78-4736-9EBE-3AE0C654FB16}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Services.OnBoardingService.RefreshDetectorTests", "Tests\BlueTape.Services.OnBoardingService.RefreshDetectorTests\BlueTape.Services.OnBoardingService.RefreshDetectorTests.csproj", "{CD1B5613-C1A9-441A-8439-F7198CBB2A5C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB05F8E5-18C3-41F1-A85E-D98CBB9C9552}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB05F8E5-18C3-41F1-A85E-D98CBB9C9552}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB05F8E5-18C3-41F1-A85E-D98CBB9C9552}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB05F8E5-18C3-41F1-A85E-D98CBB9C9552}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFB1D083-24EB-4777-ABDB-103935B9E2AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFB1D083-24EB-4777-ABDB-103935B9E2AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFB1D083-24EB-4777-ABDB-103935B9E2AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFB1D083-24EB-4777-ABDB-103935B9E2AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{04C8B807-B9A8-49C3-BD49-C9269EF4635E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04C8B807-B9A8-49C3-BD49-C9269EF4635E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04C8B807-B9A8-49C3-BD49-C9269EF4635E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04C8B807-B9A8-49C3-BD49-C9269EF4635E}.Release|Any CPU.Build.0 = Release|Any CPU
		{88A49063-C283-41B9-96E8-6A9B23FE79C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88A49063-C283-41B9-96E8-6A9B23FE79C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88A49063-C283-41B9-96E8-6A9B23FE79C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88A49063-C283-41B9-96E8-6A9B23FE79C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}.Release|Any CPU.Build.0 = Release|Any CPU
		{071D5773-F59C-4C47-897E-624FCD8850B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{071D5773-F59C-4C47-897E-624FCD8850B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{071D5773-F59C-4C47-897E-624FCD8850B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{071D5773-F59C-4C47-897E-624FCD8850B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{28148A78-1BF4-4390-AF25-586C66EC4D97}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28148A78-1BF4-4390-AF25-586C66EC4D97}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28148A78-1BF4-4390-AF25-586C66EC4D97}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28148A78-1BF4-4390-AF25-586C66EC4D97}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B6F19A4-05D8-4BB0-B75C-7144E056D087}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B6F19A4-05D8-4BB0-B75C-7144E056D087}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B6F19A4-05D8-4BB0-B75C-7144E056D087}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B6F19A4-05D8-4BB0-B75C-7144E056D087}.Release|Any CPU.Build.0 = Release|Any CPU
		{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0478359-F8EE-4D8B-BE45-D70C72FA5727}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0478359-F8EE-4D8B-BE45-D70C72FA5727}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0478359-F8EE-4D8B-BE45-D70C72FA5727}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0478359-F8EE-4D8B-BE45-D70C72FA5727}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6DC8ED4-A89B-4091-86A3-6BADE959A547}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6DC8ED4-A89B-4091-86A3-6BADE959A547}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6DC8ED4-A89B-4091-86A3-6BADE959A547}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6DC8ED4-A89B-4091-86A3-6BADE959A547}.Release|Any CPU.Build.0 = Release|Any CPU
		{94CC420E-5E78-4736-9EBE-3AE0C654FB16}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94CC420E-5E78-4736-9EBE-3AE0C654FB16}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94CC420E-5E78-4736-9EBE-3AE0C654FB16}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94CC420E-5E78-4736-9EBE-3AE0C654FB16}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD1B5613-C1A9-441A-8439-F7198CBB2A5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD1B5613-C1A9-441A-8439-F7198CBB2A5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD1B5613-C1A9-441A-8439-F7198CBB2A5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD1B5613-C1A9-441A-8439-F7198CBB2A5C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{04C8B807-B9A8-49C3-BD49-C9269EF4635E} = {C5C54711-68E9-4992-83F0-DC2C3EB90D04}
		{88A49063-C283-41B9-96E8-6A9B23FE79C0} = {C5C54711-68E9-4992-83F0-DC2C3EB90D04}
		{6B6F19A4-05D8-4BB0-B75C-7144E056D087} = {C0B42771-0206-4DC7-95B9-19E9EC3DD7BF}
		{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7} = {C0B42771-0206-4DC7-95B9-19E9EC3DD7BF}
		{D6DC8ED4-A89B-4091-86A3-6BADE959A547} = {C0B42771-0206-4DC7-95B9-19E9EC3DD7BF}
		{CD1B5613-C1A9-441A-8439-F7198CBB2A5C} = {C5C54711-68E9-4992-83F0-DC2C3EB90D04}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {924E71B8-FB23-439C-8D0B-A6476F6BFA60}
	EndGlobalSection
EndGlobal
