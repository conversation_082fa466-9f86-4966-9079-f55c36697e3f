﻿using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Configuration.Models;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using BlueTape.Utilities.Constants;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog.Context;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService;

public class AuthorizationDetailsRefreshDetectorService : IAuthorizationDetailsRefreshDetectorService
{
    private readonly IAuthorizationDetailsRefreshConfigurationService _configurationService;
    private readonly ICreditApplicationRepository _creditApplicationRepository;
    private readonly ICompanyScheduledUpdateEventsGenerator _eventsGenerator;
    private readonly IDecisionEngineStepsService _decisionEngineStepsService;
    private readonly IRefreshServiceMessageSender _refreshServiceMessageSender;
    private readonly IDateProvider _dateProvider;
    private readonly ICreditApplicationNotesService _creditApplicationNotesService;
    private readonly ICompanyService _companyService;
    private readonly IKeyVaultService _keyVaultService;
    private readonly AuthorizationDetailsDetectorMessagingOptions _options;
    private readonly ILogger<AuthorizationDetailsRefreshDetectorService> _logger;

    public AuthorizationDetailsRefreshDetectorService(
        IAuthorizationDetailsRefreshConfigurationService configurationService,
        ICreditApplicationRepository creditApplicationRepository,
        ICompanyScheduledUpdateEventsGenerator eventsGenerator,
        IDecisionEngineStepsService decisionEngineStepsService,
        IRefreshServiceMessageSender refreshServiceMessageSender,
        IDateProvider dateProvider,
        ICreditApplicationNotesService creditApplicationNotesService,
        ICompanyService companyService,
        IKeyVaultService keyVaultService,
        IOptions<AuthorizationDetailsDetectorMessagingOptions> options,
        ILogger<AuthorizationDetailsRefreshDetectorService> logger)
    {
        _configurationService = configurationService;
        _creditApplicationRepository = creditApplicationRepository;
        _eventsGenerator = eventsGenerator;
        _decisionEngineStepsService = decisionEngineStepsService;
        _refreshServiceMessageSender = refreshServiceMessageSender;
        _companyService = companyService;
        _creditApplicationNotesService = creditApplicationNotesService;
        _dateProvider = dateProvider;
        _keyVaultService = keyVaultService;
        _options = options.Value;
        _logger = logger;
    }

    public async Task Run(CancellationToken ctx, string? companyId = null)
    {
        _logger.LogInformation("Started authorization details refresh detector execution");

        var activeCompanies = companyId is not null
            ? await _companyService.GetCompanyById(companyId, ctx) is CompanyModel company ? [company] : []
            : await FilterCompanies(await _companyService.GetCompaniesByActiveAccounts(ctx) ?? []);

        var approvedCreditApplications = await GetApprovedCreditApplications(activeCompanies, ctx);
        var companiesForScheduledUpdates = GetCompaniesForScheduledUpdates(approvedCreditApplications, activeCompanies);
        var companyIds = companiesForScheduledUpdates.Select(x => x.Id).ToArray();
        var scheduledChecks = await _configurationService.GetRefreshChecks(ctx);
        var latestStepsDictionary = await _decisionEngineStepsService.GetLatestByCompanyIds(companyIds, ctx);

        var refreshServiceMessages = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>();
        foreach (var companyForUpdates in companiesForScheduledUpdates)
        {
            using (LogContext.PushProperty("CompanyId", companyForUpdates.Id))
            {
                var doesCompanyContainExecutions = latestStepsDictionary.TryGetValue(companyForUpdates.Id, out var lastCompanyExecutions);
                foreach (var scheduledCheck in scheduledChecks)
                {
                    var companyEvents = _eventsGenerator.GenerateScheduledUpdateEvents(companyForUpdates, scheduledCheck,
                        approvedCreditApplications, doesCompanyContainExecutions ? lastCompanyExecutions.ToList() : new List<DecisionEngineSteps>());
                    refreshServiceMessages.AddRange(companyEvents);
                }
            }
        }

        await SendEventsToRefreshService(refreshServiceMessages, ctx);

        _logger.LogInformation("Finished authorization details refresh detector execution");
    }

    private async Task<List<LightCreditApplicationDocument>> GetApprovedCreditApplications(List<CompanyModel> activeCompanies, CancellationToken ctx)
    {
        var companyIds = activeCompanies.Select(company => company.Id).ToArray();
        var approvedCreditApplications = (await _creditApplicationRepository.GetLightCreditApplications(companyIds,
            CreditApplicationStatus.Approved.ToString(),
            ctx)).ToList();

        return approvedCreditApplications;
    }

    private IEnumerable<CompanyModel> GetCompaniesForScheduledUpdates(List<LightCreditApplicationDocument> approvedCreditApplications, List<CompanyModel> activeCompanies)
    {
        var activeCompaniesWithApprovedCreditApplicationsIds = approvedCreditApplications
            .Select(x => x.CompanyId)
            .ToArray();
        var activeCompaniesWithApprovedCreditApplications = activeCompanies
            .Where(x => activeCompaniesWithApprovedCreditApplicationsIds.Contains(x.Id));

        // Check only companies created not later than year ago, in order to decrease messages amount and state machine executions in non-prod envs
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment) != EnvironmentConstants.Production)
        {
            activeCompaniesWithApprovedCreditApplications =
                activeCompaniesWithApprovedCreditApplications.Where(x => x.CreatedAt >= DateTime.UtcNow.AddYears(-1));
        }

        //Limit amount of companies to be refreshed to avoid overloading 
        return activeCompaniesWithApprovedCreditApplications.Take(_options.MaxCompaniesRefreshedCount);
    }

    private async Task SendEventsToRefreshService(IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> events, CancellationToken ct)
    {
        if (!events.Any()) return;
        var scheduledPeriodInMinutes = _options.ScheduledPeriodDurationBetweenMessagesInMinutes;
        var maxSimultaneouslySentMessagesCount = _options.MaxSimultaneouslySentMessagesCount;
        var currentDateTimeOffset = DateTimeOffset.UtcNow;
        for (var messagesSentCount = 0; messagesSentCount < events.Count; messagesSentCount++)
        {
            var currentEvent = events[messagesSentCount];
            var offsetInSeconds = (60 * (messagesSentCount / maxSimultaneouslySentMessagesCount) * scheduledPeriodInMinutes) + (messagesSentCount % 2) * 10;
            currentEvent.MessageAttributes!.ScheduledEnqueueTimeUtc = currentDateTimeOffset.AddSeconds(offsetInSeconds);
        }
        var eventsBatches = events.Select((x, i) => new { Index = i, Value = x })
            .GroupBy(x => x.Index / _options.MaxMessagesInBatchCount)
            .Select(x => x.Select(v => v.Value).ToList())
            .ToList();

        foreach (var eventsBatch in eventsBatches)
        {
            await _refreshServiceMessageSender.SendMessages(eventsBatch, ct);
        }
    }

    private async Task<List<CompanyModel>> FilterCompanies(List<CompanyModel> allCompanies)
    {
        var skipValue = "all";
        var secretValue = await _keyVaultService.GetSecret(AuthorizationDetailsRefreshDetectorConstants.RefreshDetectorCompaniesList);
        var companyIds = new List<string>(secretValue.Split(','));

        if (string.IsNullOrEmpty(secretValue))
            return allCompanies;

        if (companyIds.Contains(skipValue))
            return allCompanies;

        return allCompanies.Where(c => companyIds.Contains(c.Id)).ToList();
    }

    public async Task GenerateCreditApplicationNotes(CreditApplicationNoteDetails note, CancellationToken ctx)
    {
        var refreshCheck = await _configurationService
            .GetRefreshCheckByScheduledUpdateType(note.ScheduledUpdate, ctx);

        if (refreshCheck == null || refreshCheck.ScheduledUpdate == "inactivityChecks") return;

        var newNote = new CreateCreditApplicationNote()
        {
            Caption = AuthorizationDetailsRefreshDetectorConstants.OverridesClearedAutomaticallyCaption,
            CreditApplicationId = note.CreditApplicationId,
            CreatedAt = _dateProvider.CurrentDateTime,
            Note = CreateNote(refreshCheck),
            ExecutionId = note.ExecutionId
        };

        await _creditApplicationNotesService.Add(newNote, ctx);
    }

    private string CreateNote(RefreshCheckConfiguration check)
    {
        AuthorizationDetailsRefreshDetectorConstants.Checks.TryGetValue(check.ScheduledUpdate, out var checkName);

        return $"Due to every {check.FrequencyInDays} days {checkName}";
    }
}