﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class DrawApprovalDecisionEngineExecutionRequest
{
    public string? DrawApprovalId { get; set; } = string.Empty;
    public string CompanyId { get; set; } = string.Empty;
    public string? CreditId { get; set; } = string.Empty;
    public string? ArAdvanceCreditId { get; set; } = string.Empty;
    public string? InHouseCreditId { get; set; } = string.Empty;
    public string? MerchantId { get; set; } = string.Empty;
    public string PaymentPlanId { get; set; } = string.Empty;
    public string? ProjectId { get; set; } = string.Empty;
    public decimal DrawAmount { get; set; }
    public decimal? CreditHoldAmount { get; set; }
    public DateTime? ExpirationDate { get; set; }
    public LoanOrigin? LoanOrigin { get; set; }
    public IList<InputPayableItem> Payables { get; set; } = new List<InputPayableItem>();
    public NoSupplierDetailsModel? NoSupplierDetails { get; set; }
    public DownPaymentDetails? DownPaymentDetails { get; set; }
}
