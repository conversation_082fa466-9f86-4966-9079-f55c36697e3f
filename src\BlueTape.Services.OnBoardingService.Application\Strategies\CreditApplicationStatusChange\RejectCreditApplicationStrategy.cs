﻿using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;

public class RejectCreditApplicationStrategy : ChangeCreditApplicationStatusStrategy
{
    private readonly IAccountAuthorizationsService _accountAuthorizationsService;

    public RejectCreditApplicationStrategy(IDateProvider dateProvider,
        IMapper mapper,
        IAccountStatusService accountStatusService,
        ICreditApplicationRepository creditApplicationRepository,
        IAccountAuthorizationsService accountAuthorizationsService,
        ICreditApplicationSyncService creditApplicationSyncService,
        ICreditApplicationNotesService creditApplicationNotesService,
        ILogger<RejectCreditApplicationStrategy> logger)
        : base(dateProvider, mapper, accountStatusService, creditApplicationRepository, creditApplicationSyncService, creditApplicationNotesService, logger)
    {
        _accountAuthorizationsService = accountAuthorizationsService;
    }

    public override bool IsApplicable(string status) => CreditApplicationStatus.Rejected.IsEnum(status);

    public override async Task<CreditApplication> ChangeStatus(CreditApplicationDocument creditApplicationDocument,
        ReviewCreditApplicationDto reviewCreditApplication, string userId, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(reviewCreditApplication.Code))
            throw new ValidationException("Rejection code must be provided");

        creditApplicationDocument.StatusCode = reviewCreditApplication.Code;
        creditApplicationDocument.StatusNote = reviewCreditApplication.Note;
        creditApplicationDocument.Status = CreditApplicationStatus.Rejected.ToString().ToLower();

        Logger.LogInformation("Started update of credit application date {id}. New Status: {status}", creditApplicationDocument.Id, reviewCreditApplication.NewStatus);
        var currentDateTime = DateProvider.CurrentDateTime;
        creditApplicationDocument.RejectedAt = currentDateTime;
        creditApplicationDocument.RejectedBy = userId;

        await _accountAuthorizationsService.RejectOwners(creditApplicationDocument.CompanyId!, creditApplicationDocument.Id, userId, ct);
        var creditApplication = await CreditApplicationRepository.Update(creditApplicationDocument, ct);

        if (ShouldStatusNoteBeGenerated(creditApplicationDocument))
        {
            creditApplicationDocument.StatusNote = reviewCreditApplication.Note;
            await GenerateStatusNote(creditApplicationDocument, reviewCreditApplication.Note, userId, ct);
        }

        await CreditApplicationSyncService.SyncApplicableCreditApplication(creditApplicationDocument, ct);

        return Mapper.Map<CreditApplication>(creditApplication);
    }
}
