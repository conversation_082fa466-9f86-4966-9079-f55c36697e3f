﻿using System.Reflection.Emit;
using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Utilities.Providers;
using Serilog;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class CreditApplicationExecutionService : ICreditApplicationExecutionService
{
    private readonly ICreditApplicationService _creditApplicationService;
    private readonly IDateProvider _dateProvider;
    private readonly IMapper _mapper;
    private readonly IDecisionEngineExecutionService _decisionEngineExecutionService;
    private readonly IDraftRepository _draftRepository;

    public CreditApplicationExecutionService(
        ICreditApplicationService creditApplicationService,
        IDateProvider dateProvider,
        IMapper mapper,
        IDecisionEngineExecutionService decisionEngineExecutionService,
        IDraftRepository draftRepository)
    {
        _creditApplicationService = creditApplicationService;
        _dateProvider = dateProvider;
        _mapper = mapper;
        _decisionEngineExecutionService = decisionEngineExecutionService;
        _draftRepository = draftRepository;
    }

    public async Task<StepFunctionsExecutionResponse> RunDecisionEngineInitializationStepForCreditApplication(CreditApplicationInitializationStepStartRequest request, CancellationToken ct)
    {
        Log.Information("Starting execution of DE for credit application.");

        request.JobId = Guid.NewGuid();
        var result = await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(request, ct);

        return result;
    }

    public async Task<StepFunctionsExecutionResponse> RunDecisionEngineForCreditApplication(CreditApplicationDecisionEngineExecutionRequest request, CancellationToken ct)
    {
        Log.Information("Starting execution of DE for credit application. Getting draft {draftId}", request.DraftId);

        var draft = await _draftRepository.GetById(request.DraftId, ct) ?? throw new VariableNullException();
        var einHash = draft.GetEinHash();

        await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(request.Type, request.MerchantId, draft.CompanyId, einHash, ct);

        var jobId = Guid.NewGuid();
        var result = await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(
             new CreditApplicationInitializationStepStartRequest()
             {
                 DraftId = request.DraftId,
                 MerchantId = request.MerchantId,
                 Type = request.Type.GetShortType(),
                 JobId = jobId
             }, ct);

        Log.Information("Started execution of DE for credit application. Getting draft {draftId}", request.DraftId);

        return result;
    }

    public async Task<StepFunctionsExecutionResponse> RunDecisionEngineAsArAdvance(string getPaidApplicationId, CancellationToken ct)
    {
        Log.Information("Starting execution of DE as ArAdvance credit application. Getting getPaid credit application {creditApplicationId}", getPaidApplicationId);

        var getPaidCreditApplication = await _creditApplicationService.GetById(getPaidApplicationId, ct)
                                       ?? throw new VariableNullException($"GetPaid credit application {getPaidApplicationId} does not exist.");

        if (!CreditApplicationStatus.Approved.IsEnum(getPaidCreditApplication.Status))
        {
            throw new ValidationException(
                "GetPaid credit application is not approved, so it can't be run as ARAdvance application.");
        }

        var draft = await _draftRepository.GetById(getPaidCreditApplication.DraftId!, ct)
                    ?? throw new VariableNullException("GetPaid credit application does not have a draft.");
        var einHash = draft.GetEinHash();

        await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(CreditApplicationType.ARAdvance,
            draft.CompanyId, draft.CompanyId, einHash, ct);

        var jobId = Guid.NewGuid();
        var result = await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(
            new CreditApplicationInitializationStepStartRequest()
            {
                DraftId = draft.Id,
                MerchantId = draft.CompanyId,
                Type = CreditApplicationType.ARAdvance.GetShortType(),
                JobId = jobId
            }, ct);

        Log.Information("Started execution of DE for credit application by draft {draftId}", draft.Id);

        return result;
    }

    public async Task<CreditApplication> SubmitCreditApplication(SubmitCreditApplication submitCreditApplication, CancellationToken ct)
    {
        if (submitCreditApplication.Type == CreditApplicationType.InHouseCredit && string.IsNullOrEmpty(submitCreditApplication.DraftId))
        {
            return await SubmitIHCWithoutSubmittedCreateApplication(submitCreditApplication, ct);
        }

        if (string.IsNullOrEmpty(submitCreditApplication.DraftId))
            throw new ValidationException("Draft id cannot be null.");

        Log.Information("Started submission of credit application. Getting draft {draftId}", submitCreditApplication.DraftId);

        var draft = await _draftRepository.GetById(submitCreditApplication.DraftId, ct) ?? throw new VariableNullException();
        var createCreditApplication = _mapper.Map<CreateCreditApplication>(submitCreditApplication);
        createCreditApplication.CompanyId = draft.CompanyId;
        createCreditApplication.EinHash = draft.GetEinHash();
        createCreditApplication.CreatedBy = ApplicationConstants.OnBoardingService;
        createCreditApplication.ApplicationDate = _dateProvider.CurrentDateTime;
        var jobId = Guid.NewGuid();

        Log.Information("Got draft {draftId}. Company id: {companyId}, ein hash: {einHash}. Started checking sent back applications", submitCreditApplication.DraftId,
            draft.CompanyId, createCreditApplication.EinHash);

        var sentBackApplication = (await _creditApplicationService.GetAllByFilters(new GetCreditApplicationQuery()
        {
            EinHash = createCreditApplication.EinHash,
            CompanyId = createCreditApplication.CompanyId,
            MerchantId = createCreditApplication.MerchantId,
            Status = [CreditApplicationStatus.SentBack.ToString()]
        }, ct)).FirstOrDefault();

        if (sentBackApplication != null)
        {
            Log.Information("Found sentBack application {creditApplicationId}. Company id: {companyId}, ein hash: {einHash}. Started DE execution",
                sentBackApplication.Id, draft.CompanyId, createCreditApplication.EinHash);

            await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(
               new CreditApplicationInitializationStepStartRequest()
               {
                   DraftId = createCreditApplication.DraftId!,
                   MerchantId = createCreditApplication.MerchantId,
                   Type = createCreditApplication.Type.GetShortType(),
                   JobId = jobId
               }, ct);

            return sentBackApplication;
        }

        await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(createCreditApplication.Type,
            createCreditApplication.MerchantId, createCreditApplication.CompanyId, createCreditApplication.EinHash, ct);

        var creditApplication = await _creditApplicationService.Create(createCreditApplication, ct, true);

        await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(new CreditApplicationInitializationStepStartRequest()
        {
            CreditApplicationId = creditApplication.Id,
            MerchantId = creditApplication.MerchantId,
            Type = creditApplication.Type.GetShortType(),
            JobId = jobId
        }, ct).ConfigureAwait(false);

        return creditApplication;
    }

    private async Task<CreditApplication> SubmitIHCWithoutSubmittedCreateApplication(SubmitCreditApplication submitCreditApplication, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(submitCreditApplication.CompanyId))
            throw new ValidationException("CompanyId is required for IHC credit application without submitted application");

        Log.Information("Started submission of IHC without submitted credit application. Getting company {companyId}", submitCreditApplication.CompanyId);

        var jobId = Guid.NewGuid();

        await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(new CreditApplicationInitializationStepStartRequest()
        {
            CompanyId = submitCreditApplication.CompanyId,
            MerchantId = submitCreditApplication.MerchantId,
            Type = submitCreditApplication.Type.GetShortType(),
            JobId = jobId
        }, ct).ConfigureAwait(false);

        var creditApplication = (await _creditApplicationService.GetAllByFilters(new GetCreditApplicationQuery()
        {
            CompanyId = submitCreditApplication.CompanyId,
            MerchantId = submitCreditApplication.MerchantId,
            Type = [submitCreditApplication.Type.GetShortType()]
        }, ct)).FirstOrDefault();

        return creditApplication ??
            throw new VariableNullException($"No CreditApplication found for CompanyId: {submitCreditApplication.CompanyId}, MerchantId: {submitCreditApplication.MerchantId}");
    }
}
