﻿using AutoMapper;
using BlueTape.OBS.DTOs.Draft;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.Draft;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers
{
    [Route(ControllersConstants.Draft)]
    [ApiController]
    public class DraftController : ControllerBase
    {
        private readonly IDraftService _draftService;
        private readonly IMapper _mapper;

        public DraftController(
            IDraftService draftService,
            IMapper mapper)
        {
            _draftService = draftService;
            _mapper = mapper;
        }

        /// <summary>
        /// Array of drafts by different filters 
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /Drafts?Id=650b28825f07d3ca092f294a,
        ///     GET /Drafts?CompanyId=5fc7cb7fc5f00ee425c5d2d689c8383f
        ///     GET /Drafts?Id=650b28825f07d3ca092f294a&amp;CompanyId=5fc7cb7fc5f00ee425c5d2d689c8383f
        ///     
        /// </remarks>
        /// <returns>Array of drafts</returns>
        [HttpGet]
        public async Task<IEnumerable<DraftDto>> Get([FromQuery] DraftQuery draftQuery, CancellationToken ct)
        {
            if (draftQuery.Id is null && draftQuery.CompanyId is null && draftQuery.CreditApplicationId is null)
            {
                return _mapper.Map<IEnumerable<DraftDto>>(await _draftService.GetAll(ct));
            }

            var draftFilter = _mapper.Map<DraftFilter>(draftQuery);
            var draft = await _draftService.GetAllByFilters(draftFilter, ct);
            return _mapper.Map<IEnumerable<DraftDto>>(draft);
        }

        [HttpPost]
        public async Task<IEnumerable<DraftDto>> GetByCompanyIds([FromBody] string[] companyIds, CancellationToken ct)
        {
            var draft = await _draftService.GetByCompanyIds(companyIds, ct);
            return _mapper.Map<IEnumerable<DraftDto>>(draft);
        }

        /// <summary>
        /// Get draft by Id
        /// </summary>
        /// <param name="id">The draft id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /Drafts/650b28825f07d3ca092f294a
        /// 
        /// </remarks>
        [HttpGet(EndpointConstants.Id)]
        public async Task<DraftDto> GetById(string id, CancellationToken ct)
        {
            var draft = await _draftService.GetById(id, ct);

            return _mapper.Map<DraftDto>(draft);
        }
    }
}
