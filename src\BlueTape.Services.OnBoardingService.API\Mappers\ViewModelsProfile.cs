﻿using AutoMapper;
using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.OBS.DTOs.Cipher;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplication.Queries;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.CreditApplicationNotes;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.DTOs.Draft;
using BlueTape.OBS.DTOs.DrawApproval.Requests;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.OBS.DTOs.ParsedDraft;
using BlueTape.OBS.DTOs.Payables;
using BlueTape.OBS.DTOs.PaymentPlan;
using BlueTape.Services.OnBoardingService.API.ViewModels;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Cipher;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;
using BlueTape.Services.OnBoardingService.Application.Models.PaymentPlans;
using BlueTape.Services.OnBoardingService.Application.Models.User;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.Draft;
using System.Text.Json;
using Bank = BlueTape.Services.OnBoardingService.Application.Models.Draft.Bank;
using BusinessInfo = BlueTape.Services.OnBoardingService.Application.Models.Draft.BusinessInfo;
using BusinessOwner = BlueTape.Services.OnBoardingService.Application.Models.Draft.BusinessOwner;
using CoOwnerInfo = BlueTape.Services.OnBoardingService.Application.Models.Draft.CoOwnerInfo;
using CreditApplicationInitializationStepStartRequest = BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution.CreditApplicationInitializationStepStartRequest;
using Data = BlueTape.Services.OnBoardingService.Application.Models.Draft.Data;
using DrawApprovalInitializationStepStartRequest = BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution.DrawApprovalInitializationStepStartRequest;
using Finance = BlueTape.Services.OnBoardingService.Application.Models.Draft.Finance;
using Item = BlueTape.Services.OnBoardingService.Application.Models.Draft.Item;

namespace BlueTape.Services.OnBoardingService.API.Mappers
{
    public class ViewModelsProfile : Profile
    {
        public ViewModelsProfile()
        {
            CreateMap<DraftQuery, DraftFilter>();

            CreateMap<CreditApplicationQueryDto, GetCreditApplicationQuery>();
            CreateMap<CreditApplicationQueryPaginatedDto, GetCreditApplicationQueryWithPagination>();

            CreateMap<CreateCreditApplicationDto, CreateCreditApplication>().ReverseMap();
            CreateMap<UpdateCreditApplicationDto, UpdateCreditApplication>().ReverseMap();
            CreateMap<PatchCreditApplicationAdminDto, PatchCreditApplicationAdminModel>();

            CreateMap<CreateDecisionEngineStepsBVIResultsDto, CreateDecisionEngineStepsBviResultsModel>();
            CreateMap<DecisionEngineStepsBviResultsModel, DecisionEngineStepsBVIResultsDto>().ReverseMap();

            CreateMap<DraftDto, Draft>().ReverseMap();
            CreateMap<PersonalInfoDto, PersonalInfo>().ReverseMap();
            CreateMap<BankDto, Bank>().ReverseMap();
            CreateMap<BusinessInfoDto, BusinessInfo>().ReverseMap();
            CreateMap<BusinessOwnerDto, BusinessOwner>().ReverseMap();
            CreateMap<CoOwnerInfoDto, CoOwnerInfo>().ReverseMap();
            CreateMap<DataDto, Data>().ReverseMap();
            CreateMap<FinanceDto, Finance>().ReverseMap();
            CreateMap<ItemDto, Item>().ReverseMap();

            CreateMap<CreditApplicationDto, CreditApplication>().ReverseMap();
            CreateMap<UpsertAccountAuthDetailsSnapshotDto, UpsertAccountAuthDetailsSnapshotModel>();
            CreateMap<CreditDetailsDto, CreditApplicationDetailsDto>().ReverseMap();

            CreateMap<CreditApplicationNoteDto, CreditApplicationNote>().ReverseMap();
            CreateMap<CreditApplicationNoteCreateDto, CreateCreditApplicationNote>();
            CreateMap<PatchDrawApprovalNoteDto, PatchDrawApprovalNote>();

            CreateMap<DrawApprovalNoteDto, DrawApprovalNote>().ReverseMap();
            CreateMap<CreateDrawApprovalNoteDto, CreateDrawApprovalNote>();
            CreateMap<PatchCreditApplicationNoteDto, PatchCreditApplicationNote>();

            CreateMap<UpdateDecisionEngineSteps, UpdateDecisionEngineStepsDto>().ReverseMap();
            CreateMap<CreateDecisionEngineStepsDto, CreateDecisionEngineSteps>()
                .ForMember(x => x.CreatedBy, opt => opt.MapFrom(y => $"{y.ExecutionId}:{y.Step}"));
            CreateMap<DecisionEngineStepsDto, DecisionEngineSteps>().ReverseMap();
            CreateMap<DecisionEngineStepResultDto, DecisionEngineStepResult>()
                .ForMember(x => x.ComparisonValue,
                    opt => opt.MapFrom(x =>
                        ConvertValueFromJsonElement(x.ComparisonValue)))
                .ForMember(x => x.ThresholdValue,
                    opt => opt.MapFrom(x =>
                        ConvertValueFromJsonElement(x.ThresholdValue)));
            CreateMap<DecisionEngineStepQueryDto, DecisionEngineStepsQueryModel>();
            CreateMap<DecisionEngineStepThresholdDto, DecisionEngineStepThresholdModel>().ReverseMap();

            CreateMap<DecisionEngineStepResult, DecisionEngineStepResultDto>();

            CreateMap<CreateAccountAuthorizationDto, CreateAccountAuthorization>();
            CreateMap<UpdateAccountAuthorizationDto, UpdateAccountAuthorization>();
            CreateMap<PatchAccountAuthorizationsDto, PatchAccountAuthorization>()
                .ForAllMembers(opts => opts.Condition((_, _, srcMember) => srcMember != null));
            CreateMap<AccountAuthorization, AccountAuthorizationDto>();
            CreateMap<AccountAuthorizationFactoringOverallDetails, AccountAuthorizationFactoringOverallDetailsDto>().ReverseMap();
            CreateMap<AccountAuthorizationCreditDetails, AccountAuthorizationCreditDetailsDto>().ReverseMap();
            CreateMap<NullifyAccountAuthorizationDto, NullifyAccountAuthorization>();

            CreateMap<OwnersDetails, OwnersDetailsDto>();
            CreateMap<OwnersEntitiesDetails, OwnersEntitiesDetailsDto>();
            CreateMap<BusinessDetails, BusinessDetailsDto>();
            CreateMap<BankAccountDetails, BankAccountDetailsDto>();
            CreateMap<AddressModel, AddressDto>();
            CreateMap<MerchantSettings, MerchantSettingsDto>();

            CreateMap<BusinessDetailsUpdateDto, BusinessDetailsUpdate>();
            CreateMap<OwnersDetailsUpdateDto, OwnersDetailsUpdate>();
            CreateMap<OwnersEntitiesDetailsUpdateDto, OwnersEntitiesDetailsUpdate>();
            CreateMap<BankAccountDetailsUpdateDto, BankAccountDetailsUpdate>();
            CreateMap<AddressDto, AddressUpdate>();

            CreateMap<GetQueryWithPaginationResultDto<CreditApplicationDto>, GetQueryWithPaginationResultModel<CreditApplication>>().ReverseMap();
            CreateMap<GetQueryWithPaginationResultDto<DrawApprovalDto>, GetQueryWithPaginationResultModel<DrawApproval>>().ReverseMap();

            CreateMap<BusinessDetailsPatchDto, BusinessDetailsPatch>()
                .ForAllMembers(opts => opts.Condition((_, _, srcMember) => srcMember != null));
            CreateMap<OwnersDetailsPatchDto, OwnersDetailsPatch>()
                .ForAllMembers(opts => opts.Condition((_, _, srcMember) => srcMember != null));
            CreateMap<OwnersEntitiesDetailsPatchDto, OwnersEntitiesDetailsPatch>()
                .ForAllMembers(opts => opts.Condition((_, _, srcMember) => srcMember != null));
            CreateMap<BankAccountDetailsPatchDto, BankAccountDetailsPatch>()
                .ForAllMembers(opts => opts.Condition((_, _, srcMember) => srcMember != null));
            CreateMap<AddressDto, AddressPatch>()
                .ForAllMembers(opts => opts.Condition((_, _, srcMember) => srcMember != null));

            CreateMap<CreditApplicationAuthorizationDetails, CreditApplicationAuthorizationDetailsDto>();

            CreateMap<CreateDrawApprovalRequest, CreateDrawApproval>();
            CreateMap<PayableItemDto, PayableItem>();
            CreateMap<NoSupplierDetailsRequest, NoSupplierDetailsModel>();
            CreateMap<NoSupplierAddressRequest, NoSupplierAddressModel>();
            CreateMap<NoSupplierBankDetailsRequest, NoSupplierBankDetailsModel>();
            CreateMap<NoSupplierBankAccountNumberRequest, NoSupplierBankAccountNumber>();
            CreateMap<PatchDrawApprovalInternalRequest, PatchDrawApproval>();
            CreateMap<PatchDrawDetailsInternalRequest, PatchInternalDrawApproval>();
            CreateMap<PatchDrawDetailsRequest, PatchDrawDetails>();
            CreateMap<PatchFactoringDetailsRequest, PatchFactoringDetailsModel>();
            CreateMap<PatchFactoringOverallDetailsRequest, PatchFactoringOverallDetailsModel>();
            CreateMap<PatchPayNowDetailsRequest, PatchPayNowDetailsModel>();
            CreateMap<PatchAutomatedApprovalDetailsRequest, PatchAutomatedApprovalDetailsModel>();
            CreateMap<DownPaymentDetailsRequest, DownPaymentDetails>();
            CreateMap<DownPaymentDetails, DownPaymentDetailsDto>();
            CreateMap<DrawApproval, DrawApprovalDto>()
                .ForMember(x => x.ApplicationDate,
                    opt => opt.MapFrom(y => new DateOnly(y.ApplicationDate.Year, y.ApplicationDate.Month, y.ApplicationDate.Day)));
            CreateMap<DrawDetails, DrawDetailsDto>()
                .ForMember(x => x.LoansLastDefaultedDate,
                opt => opt.MapFrom(y => ConvertNullableDateTimeToDateOnly(y.LoansLastDefaultedDate)))
                .ForMember(x => x.ProjectEndDate,
                    opt => opt.MapFrom(y => ConvertNullableDateTimeToDateOnly(y.ProjectEndDate)));
            CreateMap<FactoringDetailsDto, FactoringDetailsModel>().ReverseMap();
            CreateMap<FactoringOverallDetailsDto, FactoringOverallDetailsModel>().ReverseMap();
            CreateMap<PayNowDetailsDto, PayNowDetailsModel>().ReverseMap();
            CreateMap<AutomatedApprovalDetailsDto, AutomatedApprovalDetailsModel>().ReverseMap();
            CreateMap<PayableItem, PayableItemDto>();
            CreateMap<NoSupplierDetails, NoSupplierDetailsDto>();
            CreateMap<NoSupplierBankDetails, NoSupplierBankDetailsDto>();
            CreateMap<NoSupplierAddress, NoSupplierAddressDto>();
            CreateMap<NoSupplierBankAccountNumber, NoSupplierBankAccountNumberDto>();

            CreateMap<OBS.DTOs.DrawApproval.Requests.DrawApprovalInitializationStepStartRequest, DrawApprovalInitializationStepStartRequest>();
            CreateMap<OBS.DTOs.CreditApplication.Requests.CreditApplicationInitializationStepStartRequest, CreditApplicationInitializationStepStartRequest>();
            CreateMap<InputPayableItemDto, InputPayableItem>();
            CreateMap<NoSupplierDetailsDto, NoSupplierDetailsModel>();
            CreateMap<NoSupplierAddressDto, NoSupplierAddressModel>();
            CreateMap<NoSupplierBankDetailsDto, NoSupplierBankDetailsModel>();
            CreateMap<NoSupplierBankAccountNumberDto, NoSupplierBankAccountNumber>();

            CreateMap<LoanPaymentPlan, LoanPaymentPlanDto>();

            CreateMap<PatchDrawStatusAdminRequest, ReviewDrawApprovalModel>();

            CreateMap<UpdateDrawApprovalRequest, UpdateDrawApproval>();
            CreateMap<UpdateDrawDetailsRequest, UpdateDrawDetails>();
            CreateMap<CipherModel, CipherDto>().ReverseMap();

            CreateMap<ParsedDraftModel, ParsedDraftDto>().ReverseMap();
            CreateMap<CoOwnerModel, CoOwnerDto>().ReverseMap();
            CreateMap<BankDetailsModel, BankDetailsDto>().ReverseMap();
            CreateMap<BusinessNameModel, BusinessNameDto>().ReverseMap();

            CreateMap<User, UserDto>();
            CreateMap<NoteDto, NoteModel>();

            CreateMap<(CompanyIdsDto companyIds, GetCreditApplicationsByCompanyIdsQueryDto query), GetCreditApplicationsByCompanyIdsQuery>()
                .ForMember(dest => dest.CompanyIds, opt => opt.MapFrom(src => src.companyIds.Ids))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.query.Status))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.query.Type))
                .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.query.Category));
        }

        private static DateOnly? ConvertNullableDateTimeToDateOnly(DateTime? dateTime)
        {
            return dateTime is null ? null : DateOnly.FromDateTime(dateTime.Value);
        }

        private static object? ConvertValueFromJsonElement(object? value)
        {
            if (value == null) return null;

            var jsonElement = (JsonElement)value;
            return jsonElement.ValueKind switch
            {
                JsonValueKind.Array => jsonElement.EnumerateArray().AsEnumerable().Select(ConvertValue).ToArray(),
                _ => ConvertValue(jsonElement)
            };
        }

        private static object? ConvertValue(JsonElement jsonElement)
        {
            return jsonElement.ValueKind switch
            {
                JsonValueKind.String => jsonElement.Deserialize<string>(),
                JsonValueKind.Number => jsonElement.Deserialize<decimal>(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                _ => null
            };
        }
    }
}
