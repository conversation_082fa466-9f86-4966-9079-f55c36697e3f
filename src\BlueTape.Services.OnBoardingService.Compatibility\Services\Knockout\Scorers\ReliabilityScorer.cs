﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class ReliabilityScorer : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        var result = new List<OwnerScore>();

        if (experian?.BusinessData != null && experian.BusinessData.Any())
        {
            foreach (var item in experian.BusinessData)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                ownerScore.Scores?.Add(Calculate("reliability", item.ReliabilityCode));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = Calculate("reliability", experian?.ReliabilityCode);
        return [new OwnerScore { Scores = [score] }];
    }

    private Score Calculate(string name, double? reliabilityCode)
    {
        return new Score()
        {
            Name = name,
            Value = reliabilityCode.HasValue ? reliabilityCode.ToString() : null,
            Pass = reliabilityCode.HasValue ? ScoringResult.Pass : ScoringResult.Reject,
        };
    }
}
