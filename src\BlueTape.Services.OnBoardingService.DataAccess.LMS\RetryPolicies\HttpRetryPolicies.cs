﻿using System.Net;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.HttpClients;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;

namespace BlueTape.Services.OnBoardingService.DataAccess.LMS.RetryPolicies;

public static class HttpRetryPolicies
{
    public static IAsyncPolicy<HttpResponseMessage> GetLoanServiceRetryPolicy(IServiceProvider serviceProvider)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => !msg.IsSuccessStatusCode &&
                             msg.StatusCode is not HttpStatusCode.NotFound and HttpStatusCode.BadRequest)
            .WaitAndRetryAsync(new[]
            {
                TimeSpan.FromMilliseconds(20),
                TimeSpan.FromMilliseconds(50),
                TimeSpan.FromMilliseconds(100),
                TimeSpan.FromMilliseconds(500),
                TimeSpan.FromMilliseconds(2000),
            }, (response, timespan, retryCount, context) =>
            {
                var logger = serviceProvider.GetRequiredService<ILogger<ILoanServiceHttpClient>>();
                logger?.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.",
                    timespan.Milliseconds, retryCount);
            });
    }
}