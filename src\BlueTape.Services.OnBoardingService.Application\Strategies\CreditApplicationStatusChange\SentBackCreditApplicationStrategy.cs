﻿using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;

public class SentBackCreditApplicationStrategy : ChangeCreditApplicationStatusStrategy
{
    public SentBackCreditApplicationStrategy(IDateProvider dateProvider,
        IMapper mapper,
        IAccountStatusService accountStatusService,
        ICreditApplicationRepository creditApplicationRepository,
        ICreditApplicationSyncService creditApplicationSyncService,
        ICreditApplicationNotesService creditApplicationNotesService,
        ILogger<SentBackCreditApplicationStrategy> logger)
        : base(dateProvider, mapper, accountStatusService, creditApplicationRepository, creditApplicationSyncService, creditApplicationNotesService, logger)
    {
    }

    public override bool IsApplicable(string status) => CreditApplicationStatus.SentBack.IsEnum(status);

    public override async Task<CreditApplication> ChangeStatus(CreditApplicationDocument creditApplicationDocument,
        ReviewCreditApplicationDto reviewCreditApplication, string userId, CancellationToken ct)
    {
        Logger.LogInformation("Started update of credit application date {id}. New Status: {status}", creditApplicationDocument.Id, reviewCreditApplication.NewStatus);

        creditApplicationDocument.Status = CreditApplicationStatus.SentBack.ToString().ToLower();

        var creditApplication = await CreditApplicationRepository.Update(creditApplicationDocument, ct);

        if (ShouldStatusNoteBeGenerated(creditApplicationDocument))
        {
            creditApplicationDocument.StatusNote = reviewCreditApplication.Note;
            await GenerateStatusNote(creditApplicationDocument, reviewCreditApplication.Note, userId, ct);
        }

        await CreditApplicationSyncService.SyncApplicableCreditApplication(creditApplicationDocument, ct);

        return Mapper.Map<CreditApplication>(creditApplication);
    }
}
