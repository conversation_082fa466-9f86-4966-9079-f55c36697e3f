﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;

public abstract class StepDataRetrievingStrategyBase
{
    private readonly IDecisionEngineStepsRepository _decisionEngineStepsRepository;
    private readonly IDecisionEngineStepsBviResultsService _decisionEngineStepsBviResultsService;
    private readonly ICreditApplicationAuthorizationDetailsService _creditApplicationAuthorizationDetailsService;

    protected StepDataRetrievingStrategyBase(IDecisionEngineStepsRepository decisionEngineStepsRepository, IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService)
    {
        _decisionEngineStepsRepository = decisionEngineStepsRepository;
        _decisionEngineStepsBviResultsService = decisionEngineStepsBviResultsService;
        _creditApplicationAuthorizationDetailsService = creditApplicationAuthorizationDetailsService;
    }

    protected abstract StepName StepName { get; }

    public bool IsApplicable(StepName stepName) => StepName == stepName;

    public virtual async Task<ReportStepDataModel?> CollectReportStepData(string creditApplicationId, string? companyId, CancellationToken ct)
    {
        var stepOutput = await GetStepOutput(creditApplicationId, StepName, ct);
        if (stepOutput is null) return null;

        var creditAppAccountAuthorization =
            await _creditApplicationAuthorizationDetailsService.GetByCreditApplicationId(creditApplicationId, ct);
        var accountAuthorizationSnapshot = creditAppAccountAuthorization?.AccountAuthorizationDetailsSnapshot;
        var accountAuthDetailsJson = accountAuthorizationSnapshot.ToFormattedJsonString();

        var outputJsonString = GetStepOutputJsonString(stepOutput);
        var inputJsonString = GetStepInputJsonString(stepOutput.ExecutionId, creditApplicationId);
        var bviResults = await GetBviResults(stepOutput.Id, companyId, accountAuthorizationSnapshot, ct);

        return new ReportStepDataModel()
        {
            CompanyId = companyId ?? string.Empty,
            CreditApplicationId = creditApplicationId,
            StepName = StepName,
            StepOutput = outputJsonString,
            StepInput = inputJsonString,
            BviResults = bviResults,
            AccountAuthorizationDetails = accountAuthDetailsJson
        };
    }

    protected virtual async Task<DecisionEngineStepsDocument?> GetStepOutput(string creditApplicationId, StepName stepName, CancellationToken ct)
    {
        var stepResults = await _decisionEngineStepsRepository
            .GetAll(x => stepName.ToString() == x.Step && x.CreditApplicationId == creditApplicationId, ct);
        var latestStepResult = stepResults.MaxBy(x => x.CreatedAt);

        return latestStepResult;
    }

    protected virtual string GetStepOutputJsonString(DecisionEngineStepsDocument? decisionEngineStepsDocument)
    {
        return decisionEngineStepsDocument.ToFormattedJsonString();
    }

    protected virtual string GetStepInputJsonString(string? executionId, string creditApplicationId)
    {
        var input = new
        {
            ExecutionId = executionId,
            CreditApplicationId = creditApplicationId,
        };
        var inputJsonString = input.ToFormattedJsonString();

        return inputJsonString;
    }

    protected async Task<BviResultModel[]> GetBviResultsFromIntegrationLogs(string? stepId, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(stepId)) throw new ValidationException("Step id is null");

        var bviResults = await _decisionEngineStepsBviResultsService.GetBviResponsesByStepId(stepId, ct);
        var bviResultsModel = bviResults.Where(x => x is not null).Select(x => new BviResultModel()
        {
            IntegrationSource = x?.RequestType ?? x?.RequestUrl ?? string.Empty,
            ResponseJsonString = x?.Response?.RawBody?.ToFormattedJsonString() ?? string.Empty,
        }).ToArray();

        return bviResultsModel;
    }

    protected abstract Task<BviResultModel[]> GetBviResults(string? stepId, string? companyId, AccountAuthorization? accountAuthorizationDetails, CancellationToken ct);
}