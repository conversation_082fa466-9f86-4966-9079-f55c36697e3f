﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization;

namespace BlueTape.Services.OnBoardingService.Domain.Serializers;
public class ObjectSerializer : IBsonSerializer
{
    public Type ValueType { get; } = typeof(object);

    public object? Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        switch (context.Reader.CurrentBsonType)
        {
            case BsonType.String:
                return context.Reader.ReadString();
            case BsonType.Array:
                var result = new List<object>();
                context.Reader.ReadStartArray();
                var bsonType = context.Reader.ReadBsonType();
                while (bsonType != BsonType.EndOfDocument)
                {
                    switch (bsonType)
                    {
                        case BsonType.Decimal128: result.Add((decimal)context.Reader.ReadDecimal128()); break;
                        case BsonType.String: result.Add(context.Reader.ReadString()); break;
                        case BsonType.Boolean: result.Add(context.Reader.ReadBoolean()); break;
                    }
                    bsonType = context.Reader.ReadBsonType();
                }
                context.Reader.ReadEndArray();
                return result;
            case BsonType.Decimal128:
                return (decimal)context.Reader.ReadDecimal128();
            case BsonType.Boolean:
                return context.Reader.ReadBoolean();
            case BsonType.Null:
                context.Reader.ReadNull();
                return null;
            default:
                throw new NotImplementedException($"No implementation to deserialize {context.Reader.CurrentBsonType}");
        }
    }

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object? value)
    {
        switch (value)
        {
            case null: context.Writer.WriteNull(); break;
            case string stringValue: context.Writer.WriteString(stringValue); break;
            case decimal number: context.Writer.WriteDecimal128(number); break;
            case bool booleanValue: context.Writer.WriteBoolean(booleanValue); break;
            case object[] array:
                {
                    context.Writer.WriteStartArray();
                    foreach (var item in array)
                    {
                        switch (item)
                        {
                            case string stringValue: context.Writer.WriteString(stringValue); break;
                            case decimal number: context.Writer.WriteDecimal128(number); break;
                            case bool booleanValue: context.Writer.WriteBoolean(booleanValue); break;
                        }
                    }
                    context.Writer.WriteEndArray();
                }
                break;
            case IEnumerable<object> collection:
                {
                    context.Writer.WriteStartArray();
                    foreach (var item in collection)
                    {
                        switch (item)
                        {
                            case string stringValue: context.Writer.WriteString(stringValue); break;
                            case decimal number: context.Writer.WriteDecimal128(number); break;
                            case bool booleanValue: context.Writer.WriteBoolean(booleanValue); break;
                        }
                    }
                    context.Writer.WriteEndArray();
                }
                break;
            default: throw new NotImplementedException("ObjectSerializer.Serialize");
        }
    }

}