﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using BlueTape.Utilities.Extensions;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService;

public class CompanyManualScheduledUpdateEventsGenerator : ICompanyManualScheduledUpdateEventsGenerator
{
    private readonly ITraceIdAccessor _traceIdAccessor;
    private readonly ILogger<CompanyManualScheduledUpdateEventsGenerator> _logger;

    public CompanyManualScheduledUpdateEventsGenerator(ITraceIdAccessor traceIdAccessor, ILogger<CompanyManualScheduledUpdateEventsGenerator> logger)
    {
        _traceIdAccessor = traceIdAccessor;
        _logger = logger;
    }

    public IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> GenerateScheduledUpdateEvents(CompanyModel company, RefreshCheckConfiguration scheduledCheck,
        IReadOnlyList<LightCreditApplicationDocument> approvedCreditApplications, ScheduleMode scheduleMode, string userId)
    {
        var events = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>();

        var creditApplicationsTypesToCheck = scheduledCheck.CreditApplicationTypes.ToList();
        var companyCreditApplications = approvedCreditApplications
            .Where(creditApp => ShouldCreditApplicationBeRerun(creditApp, creditApplicationsTypesToCheck));
        var companyCreditApplicationIds = companyCreditApplications
            .Select(x => x.Id)
            .Distinct()
            .ToArray();

        if (companyCreditApplicationIds.Length == 0)
        {
            _logger.LogInformation("No credit applications applicable for manual scheduled update {scheduledUpdateType} check", scheduledCheck.ScheduledUpdate);
            return events;
        }

        var correlationId = _traceIdAccessor.TraceId;

        _logger.LogInformation("Generating scheduled update events");
        foreach (var creditAppId in companyCreditApplicationIds)
        {
            var scheduledUpdateEvent = new ScheduledUpdateEvent()
            {
                CreatedBy = AuthorizationDetailsRefreshDetectorConstants.CreatedBy,
                BlueTapeCorrelationId = correlationId,
                EventType = $"{AuthorizationDetailsRefreshDetectorConstants.ScheduledUpdateEventType}.{scheduledCheck.ScheduledUpdate}",
                UserId = userId,
                Details = new ScheduledUpdateDetails()
                {
                    ApprovedCreditApplicationId = creditAppId,
                    ScheduleMode = scheduleMode
                }
            };
            events.Add(new ServiceBusMessageBt<ScheduledUpdateEvent>(scheduledUpdateEvent, new ServiceBusMessageAttributes()
            {
                CorrelationId = correlationId
            }));
        }

        return events;
    }

    private static bool ShouldCreditApplicationBeRerun(LightCreditApplicationDocument creditApplication,
        List<CreditApplicationType> creditApplicationsTypesToCheck)
    {
        var creditAppType = creditApplication.Type.ParseToEnum<CreditApplicationType>();
        return creditAppType != null && creditApplicationsTypesToCheck.Contains(creditAppType.Value);
    }
}
