﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.PaymentPlan;

[BsonIgnoreExtraElements]
[MongoCollection("loanpaymentplans")]
public class LoanPaymentPlanDocument : Document
{
    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("lmsTemplateId")]
    public string? LmsTemplateId { get; set; }

    [BsonElement("frequency")]
    public string? Frequency { get; set; }

    [BsonElement("type")]
    public string? Type { get; set; }

    [BsonElement("days")]
    public int Days { get; set; }

    [BsonElement("fee")]
    public double Fee { get; set; }

    [BsonElement("term")]
    public int Term { get; set; }

    [BsonElement("firstPaymentDelayDays")]
    public int FirstPaymentDelayDays { get; set; }
}
