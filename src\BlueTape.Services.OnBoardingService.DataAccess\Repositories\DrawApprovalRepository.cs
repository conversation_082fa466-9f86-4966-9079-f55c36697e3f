using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.Extensions;
using BlueTape.Services.OnBoardingService.DataAccess.Pipelines;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Text.RegularExpressions;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class DrawApprovalRepository(
    IObsMongoDBContext context,
    ILogger<DrawApprovalRepository> logger)
    : GenericRepository<DrawApprovalDocument>(context, logger), IDrawApprovalRepository
{
    public async Task<GetQueryWithPaginationResult<DrawApprovalDocument>> GetAllByFiltersWithPagination(GetDrawApprovalsQueryWithPagination query, CancellationToken ct)
    {
        Logger.LogInformation("Get draw approvals by filters: {@Query}", query);

        var skip = (query.PageNumber - 1) * query.PageSize;
        var limit = query.PageSize;
        var pageSize = query.PageSize <= 0 ? 1 : query.PageSize;
        var filterDefinition = BuildFilterDefinition(query);
        var sortDefinition = BuildSortDefinition(query);

        var pipeline = BuildFilteredLookupQuery(filterDefinition)
            .Sort(sortDefinition)
            .Skip(skip)
            .Limit(limit);

        var totalCount = await GetDocumentsCountV2Async(filterDefinition, ct);
        using var drawApprovalsQuery = await Collection.AggregateAsync(pipeline, cancellationToken: ct);
        return new GetQueryWithPaginationResult<DrawApprovalDocument>()
        {
            TotalCount = totalCount,
            Result = await drawApprovalsQuery.ToListAsync(ct),
            PageNumber = query.PageNumber,
            PagesCount = (long)Math.Round((double)totalCount / pageSize, 0, MidpointRounding.ToPositiveInfinity)
        };
    }

    public async Task<DrawApprovalDocument?> GetByInvoicesIds(string[] invoicesIds, CancellationToken ct)
    {
        var filter = Builders<DrawApprovalDocument>.Filter.ElemMatch(
            doc => doc.Payables,
            pay => invoicesIds.Contains(pay.Id)
        );

        var document = Collection.Find(filter);
        return await document.FirstOrDefaultAsync(ct);
    }

    public async Task<IEnumerable<DrawApprovalDocument?>> GetManyByInvoicesIds(string[] invoicesIds, CancellationToken ct)
    {
        var filter = Builders<DrawApprovalDocument>.Filter.AnyIn(
            x => x.Payables.Select(p => p.Id),
            invoicesIds
        );

        var document = Collection.Find(filter);
        return await document.ToListAsync(ct);
    }

    private static FilterDefinition<DrawApprovalDocument> BuildFilterDefinition(GetDrawApprovalsQueryWithPagination query)
    {
        var expression = Builders<DrawApprovalDocument>.Filter;
        var filter = expression.Empty;
        var companyNameRegex = query.CompanyName.BuildRegexDefinition();
        var searchRegex = query.Search.BuildRegexDefinition();
        var merchantNameRegex = query.MerchantName.BuildRegexDefinition();

        if (!string.IsNullOrEmpty(query.Id)) filter &= expression.Eq(x => x.Id, query.Id);
        if (!string.IsNullOrEmpty(query.CompanyId)) filter &= expression.Eq(x => x.CompanyId, query.CompanyId);
        if (!string.IsNullOrEmpty(query.EinHash)) filter &= expression.Eq(x => x.EinHash, query.EinHash);
        if (!string.IsNullOrEmpty(query.CreditId)) filter &= expression.Eq(x => x.CreditId, query.CreditId);
        if (!string.IsNullOrEmpty(query.ArAdvanceCreditId)) filter &= expression.Eq(x => x.ArAdvanceCreditId, query.ArAdvanceCreditId);
        if (!string.IsNullOrEmpty(query.InHouseCreditId)) filter &= expression.Eq(x => x.InHouseCreditId, query.InHouseCreditId);
        if (!string.IsNullOrEmpty(query.StatusCode)) filter &= expression.Eq(x => x.StatusCode, query.StatusCode);
        if (!string.IsNullOrEmpty(query.MerchantId)) filter &= expression.Eq(x => x.MerchantId, query.MerchantId);
        if (!string.IsNullOrEmpty(query.PaymentPlanId)) filter &= expression.Eq(x => x.PaymentPlanId, query.PaymentPlanId);
        if (!string.IsNullOrEmpty(query.CompanyDba)) filter &= expression.Eq(x => x.MerchantId, query.CompanyDba);
        if (!string.IsNullOrEmpty(query.InvoiceNumber)) filter &= expression.ElemMatch(x => x.Payables, Builders<PayableItemDocument>.Filter.Regex(p => p.InvoiceNumber, query.InvoiceNumber));
        if (!string.IsNullOrEmpty(query.VelocityCheckResult)) filter &= expression.Eq(x => x.AutomatedApprovalDetails.VelocityCheckResult, query.VelocityCheckResult);
        if (!string.IsNullOrEmpty(query.Term)) filter &= expression.Regex(x => x.LoanPricingPackageName,
            new BsonRegularExpression($"^{Regex.Escape($"{query.Term}")}$", "i"));
        if (!string.IsNullOrEmpty(query.Product))
        {
            var productRegex = GetProductRegexString(query.Product);
            filter &= expression.Regex(x => x.Type, new BsonRegularExpression(productRegex, "i"));
        }

        if (query.AppDateFrom.HasValue) filter &= expression.Gte(x => x.ApplicationDate, query.AppDateFrom.Value.ToDateTime(new TimeOnly()));
        if (query.AppDateTo.HasValue) filter &= expression.Lt(x => x.ApplicationDate, query.AppDateTo.Value.ToDateTime(new TimeOnly()));
        if (query.DecisionDateFrom.HasValue) filter &= expression.Gte(x => x.LastStatusChangedAt, query.DecisionDateFrom.Value.ToDateTime(new TimeOnly()));
        if (query.DecisionDateTo.HasValue) filter &= expression.Lt(x => x.LastStatusChangedAt, query.DecisionDateTo.Value.ToDateTime(new TimeOnly()));
        if (query.InvoiceDueFrom.HasValue)
        {
            filter &= Builders<DrawApprovalDocument>.Filter.ElemMatch(
                x => x.Payables,
                Builders<PayableItemDocument>.Filter.Gte(p => p.InvoiceDueDate, query.InvoiceDueFrom.Value.ToDateTime(new TimeOnly()))
            );
        }
        if (query.InvoiceDueTo.HasValue)
        {
            filter &= Builders<DrawApprovalDocument>.Filter.ElemMatch(
                x => x.Payables,
                Builders<PayableItemDocument>.Filter.Lt(p => p.InvoiceDueDate, query.InvoiceDueTo.Value.ToDateTime(new TimeOnly()))
            );
        }

        if (!string.IsNullOrEmpty(query.CompanyName))
            filter &= expression.Or(
                expression.Regex(x => x.CompanyName, companyNameRegex),
                expression.Regex(x => x.ApplicantName, companyNameRegex),
                expression.Regex(x => x.MerchantName, companyNameRegex));

        if (!string.IsNullOrEmpty(query.Search))
            filter &= expression.Or(
                expression.Regex(x => x.Type, searchRegex),
                expression.Regex(x => x.ApplicantName, searchRegex),
                expression.Regex(x => x.CompanyName, searchRegex),
                expression.Regex(x => x.MerchantName, searchRegex),
                expression.Regex(x => x.Term, searchRegex),
                expression.ElemMatch(x => x.Payables, Builders<PayableItemDocument>.Filter.Regex(p => p.InvoiceNumber, searchRegex))
                );

        if (!string.IsNullOrEmpty(query.MerchantName))
            filter &= expression.Regex(x => x.MerchantName, merchantNameRegex);

        //$or + $regex[] -> $in
        if (query.Status?.Length > 0) filter &= BuildStatusFilter(query, expression, filter);
        if (query.Type?.Length > 0) filter &= expression.Or(query.Type.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.Type));
        if (query.AutomatedDecisionResult?.Length > 0) filter &= expression.Or(query.AutomatedDecisionResult.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.AutomatedDecisionResult));
        if (query.AutomatedApprovalResult?.Length > 0) filter &= expression.Or(query.AutomatedApprovalResult.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.AutomatedApprovalResult));
        if (query.AccountStatus?.Length > 0) filter &= expression.Or(query.AccountStatus.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.DrawDetails.AccountStatus));
        if (query.PayableIds?.Length > 0) filter &= expression.ElemMatch(x => x.Payables, Builders<PayableItemDocument>.Filter.In(x => x.Id, query.PayableIds));
        if (query.IhcStatus?.Length > 0) filter &= expression.Or(query.IhcStatus.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.FactoringDetails.InHouseCreditStatus));
        if (query.InvoiceStatuses?.Length > 0) filter &= expression.ElemMatch(x => x.Payables, query.InvoiceStatuses.ToCaseInsensitiveRegexFilterForCollection<PayableItemDocument>(x => x.Status));

        return filter;
    }

    private static FilterDefinition<DrawApprovalDocument> BuildStatusFilter(GetDrawApprovalsQueryWithPagination query, FilterDefinitionBuilder<DrawApprovalDocument> expression,
        FilterDefinition<DrawApprovalDocument> filter)
    {
        if (query.Status!.Any(status => status.Equals(DrawApprovalConstants.Approved, StringComparison.OrdinalIgnoreCase) ||
                                              status.Equals(DrawApprovalConstants.AutoApproved, StringComparison.OrdinalIgnoreCase)))
        {
            var quoteType = Builders<DrawApprovalDocument>.Filter.Or(
                DrawApprovalConstants.Quote.ToCaseInsensitiveRegexFilter<DrawApprovalDocument>(x => x.Type),
                DrawApprovalConstants.Factoring.ToCaseInsensitiveRegexFilter<DrawApprovalDocument>(x => x.Type));
            var nonQuoteType = Builders<DrawApprovalDocument>.Filter.Not(
                DrawApprovalConstants.Quote.ToCaseInsensitiveRegexFilter<DrawApprovalDocument>(x => x.Type));

            var quoteFilter = Builders<DrawApprovalDocument>.Filter.And(
                    quoteType,
                DrawApprovalConstants.Approved.ToCaseInsensitiveRegexFilter<DrawApprovalDocument>(x => x.Status),
                query.PayableTypes?.Length > 0 ? BuildPayableTypeFilter(query.PayableTypes) : BuildPayableTypeFilter([DrawApprovalConstants.Quote, DrawApprovalConstants.Invoice])
            );

            var nonQuoteFilter = Builders<DrawApprovalDocument>.Filter.And(
                    nonQuoteType,
                DrawApprovalConstants.Approved.ToCaseInsensitiveRegexFilter<DrawApprovalDocument>(x => x.Status),
                BuildPayableTypeFilter([DrawApprovalConstants.Invoice])
            );

            var approvedFilter = Builders<DrawApprovalDocument>.Filter;

            if (query.Status!.Any(status => !status.Equals(DrawApprovalConstants.Approved, StringComparison.OrdinalIgnoreCase)))
            {
                query.Status = query.Status!.Where(x => !string.Equals(x, DrawApprovalConstants.Approved, StringComparison.OrdinalIgnoreCase)).ToArray();
                var otherStatusFilters = expression.Or(query.Status!.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.Status));
                filter &= approvedFilter.Or(quoteFilter, nonQuoteFilter, otherStatusFilters);
            }
            else
            {
                if (query.PayableTypes?.Length == 1 &&
                    query.PayableTypes.FirstOrDefault() == DrawApprovalConstants.Quote)
                    filter &= quoteFilter;
                else
                {
                    filter &= approvedFilter.Or(quoteFilter, nonQuoteFilter);
                }
            }

            if (query.Status!.Any(status => status.Equals(DrawApprovalConstants.Approved, StringComparison.OrdinalIgnoreCase))) return filter;

            filter &= approvedFilter.And(Builders<DrawApprovalDocument>.Filter.Eq(x => x.AutomatedApprovalResult, AutomatedApprovalResult.Passed.ToString()));
            filter &= approvedFilter.And(Builders<DrawApprovalDocument>.Filter.Eq(x => x.AutomatedDecisionResult, AutomatedDecisionResult.Pass.ToString()));


            return filter;
        }
        else
        {
            var otherStatusFilters = expression.Or(query.Status!.ConvertToCaseInsensitiveRegexFiltersList<DrawApprovalDocument>(x => x.Status));
            filter &= otherStatusFilters;
            return filter;
        }
    }

    private static SortDefinition<DrawApprovalDocument> BuildSortDefinition(GetDrawApprovalsQueryWithPagination query)
    {
        var builder = Builders<DrawApprovalDocument>.Sort;

        if (string.IsNullOrWhiteSpace(query.SortBy))
        {
            return builder.Descending(x => x.CreatedAt);
        }

        return string.Equals(query.SortOrder, SortConstants.Ascending, StringComparison.OrdinalIgnoreCase)
            ? builder.Ascending(query.SortBy)
            : builder.Descending(query.SortBy);
    }

    private static FilterDefinition<DrawApprovalDocument> BuildPayableTypeFilter(string[] payableTypes)
    {
        var inFilter = Builders<PayableItemDocument>.Filter.In(p => p.Type, payableTypes);
        var allMatchFilter = Builders<DrawApprovalDocument>.Filter.ElemMatch(
            d => d.Payables,
            inFilter
        );
        var ninFilter = Builders<PayableItemDocument>.Filter.Nin(p => p.Type, payableTypes);
        var noInvalidTypesFilter = Builders<DrawApprovalDocument>.Filter.Not(
            Builders<DrawApprovalDocument>.Filter.ElemMatch(d => d.Payables, ninFilter)
        );

        // Note: The size filter is commented out because it filtered out draws with multiple invoices that is incorrect.
        //var sizeFilter = Builders<DrawApprovalDocument>.Filter.Size(d => d.Payables, payableTypes.Length);
        var combinedFilter = Builders<DrawApprovalDocument>.Filter.And(
            allMatchFilter,
            noInvalidTypesFilter
        );

        return combinedFilter;
    }

    private static string GetProductRegexString(string product)
    {
        var factoringType = DrawApprovalType.Factoring.ToString();
        var isLineOfCreditProduct = product.Equals("loc", StringComparison.InvariantCultureIgnoreCase);

        return isLineOfCreditProduct ? $"^(?!{factoringType}$)" : factoringType;
    }

    private static PipelineDefinition<DrawApprovalDocument, DrawApprovalDocument> BuildFilteredLookupQuery(FilterDefinition<DrawApprovalDocument> filterDefinition)
    {
        var lookupLoanPricingPackagesPipeline = BuildLookupLoanPaymentPlansQuery();
        var lookupInvoicePipeline = BuildLookupInvoiceQuery(lookupLoanPricingPackagesPipeline);
        return lookupInvoicePipeline.Match(filterDefinition);
    }

    private static PipelineDefinition<DrawApprovalDocument, DrawApprovalDocument> BuildLookupLoanPaymentPlansQuery()
    {
        var pipeline = new EmptyPipelineDefinition<DrawApprovalDocument>()
            .AppendStage(LoanPricingPackagesPipelines.LookupLoanPricingPackages)
            .AppendStage(LoanPricingPackagesPipelines.UnwindLoanPricingPackages)
            .AppendStage(LoanPricingPackagesPipelines.AddLoanPricingPackages);
        return pipeline;
    }

    private static PipelineDefinition<DrawApprovalDocument, DrawApprovalDocument> BuildLookupInvoiceQuery(PipelineDefinition<DrawApprovalDocument, DrawApprovalDocument> pipeline)
    {
        var updatedPipeline = pipeline
            .AppendStage(InvoicePipelines.LookupInvoiceStatus)
            .AppendStage(InvoicePipelines.AddInvoiceStatusToPayables);

        return updatedPipeline;
    }

    private async Task<int> GetDocumentsCountV2Async(FilterDefinition<DrawApprovalDocument> filterDefinition, CancellationToken ct)
    {
        return (int)await Collection.CountDocumentsAsync(filterDefinition, cancellationToken: ct);
    }
}