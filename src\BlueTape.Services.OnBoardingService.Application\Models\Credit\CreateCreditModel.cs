﻿using BlueTape.LS.Domain.Enums;
using PurchaseTypeOption = BlueTape.OBS.Enums.PurchaseTypeOption;

namespace BlueTape.Services.OnBoardingService.Application.Models.Credit;

public class CreateCreditModel
{
    public string CompanyId { get; set; } = string.Empty;

    public string CreditApplicationId { get; set; } = string.Empty;

    public string? ProjectId { get; set; }

    public DateOnly StartDate { get; set; }

    public decimal CreditLimit { get; set; }

    public string? Currency { get; set; }

    public PurchaseTypeOption? PurchaseType { get; set; }

    public double RevenueFallPercentage { get; set; }

    public string? MerchantId { get; set; }

    public ProductType Product { get; set; }
}
