﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration
{
    public class RefreshCheckConfiguration
    {
        public string ScheduledUpdate { get; set; } = string.Empty;
        public int FrequencyInDays { get; set; }
        public IReadOnlyList<CreditApplicationType> CreditApplicationTypes { get; set; } = new List<CreditApplicationType>();
        public IReadOnlyList<string> StepsIncluded { get; set; } = new List<string>();
    }
}
