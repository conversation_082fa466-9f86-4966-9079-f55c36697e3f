﻿using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

namespace BlueTape.Services.OnBoardingService.Application.Tests.DTOs.AccountAuthorization;

public class ValidAccountAuthorizations
{
    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfAccountAuthorizationsForIdIsNullScenario =
        new()
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "e351e7f6-7824-4d2d-9ef5-ead2eff2c87d"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly Models.AccountAuthorization.AccountAuthorization AccountAuthorizationsDeepCopyObject =
        new()
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
            EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7",
            OwnersDetails = new List<OwnersDetails>()
            {
                new()
                {
                    Identifier = "Owner",
                    IsPrincipal = true,
                    SsnHash = "ssnHash",
                },
                new()
                {
                    Identifier = "CoOwner1",
                    IsPrincipal = false,
                    SsnHash = "ssnHash2",
                    Address = new()
                    {
                        State = "state"
                    }
                }
            },
            BusinessDetails = new()
            {
                AnnualRevenue = 10
            },
            BankAccountDetails = new List<BankAccountDetails>()
            {
                new()
                {
                    Identifier = "account1",
                    Id = "id"
                }
            }
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfExpectedAccountAuthorizationsForIdIsNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfAccountAuthorizationsForCompanyIdIsNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "55dc39fc-**************-926cb023a258",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "68b99a5a-b08c-47a4-aaf7-cf5ec4eb8689",
                CompanyId = "638265db-eddf-4c60-85d3-c9b903cd1d27",
                EinHash = "e351e7f6-7824-4d2d-9ef5-ead2eff2c87d"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "7c35fb2e-9226-40d8-a711-dc9c06313a56",
                CompanyId = "ae6ee9d4-ad2e-4a26-88ae-716c56362b9f",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfExpectedAccountAuthorizationsForCompanyIdIsNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfAccountAuthorizationsForEinHashIsNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "68b99a5a-b08c-47a4-aaf7-cf5ec4eb8689",
                CompanyId = "638265db-eddf-4c60-85d3-c9b903cd1d27",
                EinHash = "e351e7f6-7824-4d2d-9ef5-ead2eff2c87d"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "7c35fb2e-9226-40d8-a711-dc9c06313a56",
                CompanyId = "ae6ee9d4-ad2e-4a26-88ae-716c56362b9f",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfExpectedAccountAuthorizationsForEinHashIsNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfAccountAuthorizationsForIdAndCompanyIdAreNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "6114a57d-23a4-42fe-8087-7272f95b0c6a",
                CompanyId = "60981bb1-2d5d-4217-a4fc-3d000cda9fe1",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "68b99a5a-b08c-47a4-aaf7-cf5ec4eb8689",
                CompanyId = "638265db-eddf-4c60-85d3-c9b903cd1d27",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "7c35fb2e-9226-40d8-a711-dc9c06313a56",
                CompanyId = "ae6ee9d4-ad2e-4a26-88ae-716c56362b9f",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "7c35fb2e-9226-40d8-a711-dc9c06313a56",
                CompanyId = "ae6ee9d4-ad2e-4a26-88ae-716c56362b9f",
                EinHash = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfExpectedAccountAuthorizationsForIdAndCompanyIdAreNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "68b99a5a-b08c-47a4-aaf7-cf5ec4eb8689",
                CompanyId = "638265db-eddf-4c60-85d3-c9b903cd1d27",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfAccountAuthorizationsForIdAndCompanyIdAndEinHashAreNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "6114a57d-23a4-42fe-8087-7272f95b0c6a",
                CompanyId = "60981bb1-2d5d-4217-a4fc-3d000cda9fe1",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "68b99a5a-b08c-47a4-aaf7-cf5ec4eb8689",
                CompanyId = "638265db-eddf-4c60-85d3-c9b903cd1d27",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "7c35fb2e-9226-40d8-a711-dc9c06313a56",
                CompanyId = "ae6ee9d4-ad2e-4a26-88ae-716c56362b9f",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "7c35fb2e-9226-40d8-a711-dc9c06313a56",
                CompanyId = "ae6ee9d4-ad2e-4a26-88ae-716c56362b9f",
                EinHash = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb"
            },
        };

    public static readonly List<Models.AccountAuthorization.AccountAuthorization> ListOfExpectedAccountAuthorizationsForIdAndCompanyIdAndEinHashAreNullScenario =
        new()
        {
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "5caa21ae-dfbf-466a-a126-4d80eee620f5",
                CompanyId = "631b2ad4-098d-4382-af76-acc6ee5aa979",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "6114a57d-23a4-42fe-8087-7272f95b0c6a",
                CompanyId = "60981bb1-2d5d-4217-a4fc-3d000cda9fe1",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
            new Models.AccountAuthorization.AccountAuthorization()
            {
                Id = "68b99a5a-b08c-47a4-aaf7-cf5ec4eb8689",
                CompanyId = "638265db-eddf-4c60-85d3-c9b903cd1d27",
                EinHash = "226fd02a-e27d-4115-9a75-f086065cc0b7"
            },
        };
}