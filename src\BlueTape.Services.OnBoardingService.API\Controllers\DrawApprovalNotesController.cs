using System.ComponentModel.DataAnnotations;
using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplicationNotes;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.DrawApprovals)]
[ApiController]
public class DrawApprovalNotesController(
    IDrawApprovalNotesService noteService,
    IMapper mapper)
{
    [HttpGet($"{EndpointConstants.Id}/{EndpointConstants.Notes}")]
    public async Task<IEnumerable<DrawApprovalNoteDto>> GetNotes(string id, CancellationToken ct)
        => mapper.Map<IEnumerable<DrawApprovalNoteDto>>(await noteService.GetByDrawApprovalId(id, ct));

    [HttpPost($"{EndpointConstants.Id}/{EndpointConstants.Notes}")]
    public async Task<DrawApprovalNoteDto> AddNote(
        [FromRoute] string id,
        [FromBody] CreateDrawApprovalNoteDto noteDto,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var newNote = mapper.Map<CreateDrawApprovalNote>(noteDto);
        newNote.CreatedBy = userId;
        newNote.DrawApprovalId = id;

        return mapper.Map<DrawApprovalNoteDto>(await noteService.Add(newNote, ct));
    }

    [HttpPatch($"{EndpointConstants.Id}/{EndpointConstants.Notes}/{EndpointConstants.NoteId}")]
    public async Task<DrawApprovalNoteDto> Patch([FromRoute] string id,
        [FromRoute] string noteId,
        [FromBody] PatchDrawApprovalNoteDto noteDto,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var patchedNote = mapper.Map<PatchDrawApprovalNote>(noteDto);
        patchedNote.DrawApprovalId = id;
        patchedNote.UpdatedBy = userId;
        patchedNote.Id = noteId;

        return mapper.Map<DrawApprovalNoteDto>(await noteService.Patch(patchedNote, ct));
    }
    
    [HttpDelete($"{EndpointConstants.Id}/{EndpointConstants.Notes}/{EndpointConstants.NoteId}")]
    public async Task Delete([FromRoute] string id,
        [FromRoute] string noteId,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var deleteModel = new DeleteDrawApprovalNote
        {
            Id = noteId,
            DrawApprovalId = id,
            UserId = userId
        };
        await noteService.Delete(deleteModel, ct);
    }
}