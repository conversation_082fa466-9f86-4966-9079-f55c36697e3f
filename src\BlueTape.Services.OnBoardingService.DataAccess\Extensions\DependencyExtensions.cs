﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.RetryPolicies;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.OnBoardingService.DataAccess.Extensions
{
    public static class DependencyExtensions
    {
        public static void AddNodeBlueTapeServiceDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddHttpClient<INodeHttpClient, NodeHttpClient>(client =>
            {
                client.BaseAddress = GetBaseAddressUri(config, NodeServiceConstants.NodeServiceUrl);
            }).SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler((serviceProvider, request) => HttpRetryPolicies.GetNodeServiceRetryPolicy(serviceProvider));

            services.AddScoped<INodeExternalService, NodeExternalService>();
        }

        private static Uri GetBaseAddressUri(IConfiguration config, string configurationUrl) => new(config[configurationUrl]!);
    }
}
