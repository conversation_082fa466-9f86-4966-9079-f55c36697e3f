using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Company;
using Microsoft.Extensions.Logging;
using CompanyUpdateModelFromOnboarding = BlueTape.Services.OnBoardingService.Application.Models.Company.UpdateCompanyModel;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class CompanyService(ILogger<CompanyService> logger, ICompanyRepository companyRepository,
    ICompanyExternalService companyExternalService, IMapper mapper) : ICompanyService
{
    public async Task<List<CompanyModel>?> GetCompaniesByActiveAccounts(CancellationToken ct)
    {
        const int pageSize = 100;
        const int startingPage = 1;

        var initialPageResult = await FetchCompaniesPageAsync(startingPage, pageSize, ct);
        if (initialPageResult == null)
        {
            logger.LogWarning("Company service: Failed to fetch the initial page of active companies.");
            return null;
        }

        var total = initialPageResult.Total ?? 0;

        if (total <= pageSize)
        {
            return initialPageResult.Result;
        }

        var allActiveCompanies = new List<CompanyModel>(initialPageResult.Result);
        int totalPages = (int)Math.Ceiling((double)total / pageSize);

        for (int page = 2; page <= totalPages; page++)
        {
            var paginatedResult = await FetchCompaniesPageAsync(page, pageSize, ct);
            if (paginatedResult?.Result == null)
            {
                logger.LogWarning("Company service: Failed to fetch page {page} of active companies.", page);
                break;
            }
            allActiveCompanies.AddRange(paginatedResult.Result);
        }

        return allActiveCompanies;
    }


    public async Task<CompanyModel?> GetCompanyById(string id, CancellationToken ct) =>
        await companyExternalService.GetById(id, ct);


    public async Task PatchCompanyCredit(string companyId, CompanyUpdateModelFromOnboarding model, CancellationToken ct)
    {
        var allowedPurchaseTypes = new HashSet<string> { "inventory", "project", "projectOrInventory" };

        if (!string.IsNullOrEmpty(model.PurchaseType))
        {
            if (model.PurchaseType.Equals("both", StringComparison.CurrentCultureIgnoreCase))
                model.PurchaseType = "projectOrInventory";

            if (!allowedPurchaseTypes.Contains(model.PurchaseType!))
            {
                logger.LogWarning("Company service: Invalid purchase type: {purchaseType} for company: {id}", model.PurchaseType, companyId);
                model.PurchaseType = null;
            }
        }

        try
        {
            await companyRepository.UpdateManyByIds([companyId], mapper.Map<UpdateCompanyDocument>(model), ct);
        }
        catch
        {
            logger.LogError("Company service: Error during updating company credit: {id}", companyId);
        }
    }

    private Task<PaginatedCompanyResponse?> FetchCompaniesPageAsync(int pageNumber, int pageSize, CancellationToken ct)
    {
        return companyExternalService.GetCompaniesByQueryAsync(new CompanyQueryPaginated
        {
            ActiveAccountOnly = true,
            PageNumber = pageNumber,
            PageSize = pageSize
        }, ct);
    }
}