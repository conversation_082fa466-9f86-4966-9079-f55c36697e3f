﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.User;

[BsonIgnoreExtraElements]
[MongoCollection("users")]
public class UserDocument : Document
{
    [BsonElement("sub")]
    public string? Sub { get; set; }

    [BsonElement("firstName")]
    public string? FirstName { get; set; }

    [BsonElement("lastName")]
    public string? LastName { get; set; }

    [BsonElement("phone")]
    public string? Phone { get; set; }

    [BsonElement("email")]
    public string? Email { get; set; }

    [BsonElement("login")]
    public string? Login { get; set; }

    [BsonElement("settings")]
    public UserSettingsDocument? Settings { get; set; }
}