﻿using BlueTape.Services.OnBoardingService.Application.Abstractions.Mappers;
using BlueTape.Services.OnBoardingService.Application.Constants.DraftParser;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Cipher;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;
using BlueTape.Utilities.Security;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BlueTape.Services.OnBoardingService.Application.Mappers.DraftMapper;

public class DraftMapper : IDraftMapper
{
    private readonly IKmsEncryptionService _kmsEncryptionService;

    public DraftMapper(IKmsEncryptionService kmsEncryptionService)
    {
        _kmsEncryptionService = kmsEncryptionService;
    }

    public async Task<ParsedDraftModel> ParseDraft(Draft draft)
    {
        var parsedDraft = new ParsedDraftModel();

        ParseCoOwnerInfo(draft, parsedDraft);
        ParseBankInfo(draft, parsedDraft);
        ParseFinanceInfo(draft, parsedDraft);
        ParseOwnerInfo(draft, parsedDraft);
        await ParseBusinessInfo(draft, parsedDraft);

        parsedDraft.CompanyId = draft.CompanyId;
        parsedDraft.DraftId = draft.Id;

        return parsedDraft;
    }

    private async Task ParseBusinessInfo(Draft draft, ParsedDraftModel parsedDraft)
    {
        var businessInfoItems = draft.Data?.BusinessInfo?.Items?.ToList();
        if (businessInfoItems is null) return;

        var businessName = businessInfoItems.Find(x => x.Identifier == BusinessInfoConstants.BusinessName)
            ?.Content!;
        var businessNameJObject = JObject.FromObject(businessName);
        parsedDraft.BusinessName = JsonConvert.DeserializeObject<BusinessNameModel>(businessNameJObject.ToString());

        var businessEin = businessInfoItems.Find(x => x.Identifier == BusinessInfoConstants.Ein)?.Content!;
        var businessEinJObject = JObject.FromObject(businessEin);
        parsedDraft.Ein = JsonConvert.DeserializeObject<CipherModel>(businessEinJObject.ToString());

        var businessAddress = businessInfoItems
            .Find(x => x.Identifier == BusinessInfoConstants.BusinessAddress)?.Content!;
        var businessAddressJObject = JObject.FromObject(businessAddress);
        parsedDraft.BusinessAddress = JsonConvert.DeserializeObject<AddressModel>(businessAddressJObject.ToString());

        var businessStartDate =
            businessInfoItems.Find(x => x.Identifier == BusinessInfoConstants.StartDate)?.Content!.ToString();
        parsedDraft.BusinessStartDate = businessStartDate;

        var businessPhone =
            businessInfoItems.Find(x => x.Identifier == BusinessInfoConstants.BusinessPhone)?.Content!.ToString();
        parsedDraft.BusinessPhone = businessPhone;
    }

    private void ParseOwnerInfo(Draft draft, ParsedDraftModel parsedDraft)
    {
        var ownerInfo = draft.Data?.BusinessOwner?.Items?.ToList();
        if (ownerInfo is null) return;

        var firstName = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.FirstName)?.Content?.ToString()!;
        parsedDraft.OwnerFirstName = firstName;

        var lastName = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.LastName)?.Content?.ToString()!;
        parsedDraft.OwnerLastName = lastName;

        var ownerSsn = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.Ssn)?.Content;
        if (ownerSsn is not null)
        {
            var content = JObject.FromObject(ownerSsn);
            parsedDraft.OwnerSsn = JsonConvert.DeserializeObject<CipherModel>(content.ToString());
        }

        var ownerAddress =
            ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.OwnerAddress)?.Content;
        if (ownerAddress is not null)
        {
            var content = JObject.FromObject(ownerAddress);
            parsedDraft.OwnerAddress = JsonConvert.DeserializeObject<AddressModel>(content.ToString());
        }

        var ownerBirthDate = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.OwnerBirthdate)?.Content
            ?.ToString();
        parsedDraft.OwnerBirthDate = ownerBirthDate;

        var ownerPhone = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.Phone)?.Content?.ToString();
        parsedDraft.OwnerPhone = ownerPhone;

        var ownerEmail = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.Email)?.Content?.ToString();
        parsedDraft.OwnerEmail = ownerEmail;

        var ownerId = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.Id)?.Content?.ToString();
        parsedDraft.OwnerId = ownerId;

        parsedDraft.OwnerPercentOwned =
            int.TryParse(
                ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.OwnerPercentage)?.Content?.ToString(),
                out var ownerPercent)
                ? ownerPercent
                : 0;

        var isPrincipal = ownerInfo.Find(x => x.Identifier == BusinessOwnerConstants.IsOwner)?.Content?.ToString();
        parsedDraft.IsPrincipal = string.Equals(isPrincipal, BusinessOwnerConstants.IsOwnerPrincipal,
            StringComparison.InvariantCultureIgnoreCase);
    }

    private void ParseCoOwnerInfo(Draft draft, ParsedDraftModel parsedDraft)
    {
        var coOwnerInfoItems = draft.Data?.CoOwnerInfo?.Items?.ToList();

        var isCoOwnersExistContent =
            coOwnerInfoItems?.Find(x => x.Identifier == CoOwnersConstants.CoOwners)?.Content;
        if (isCoOwnersExistContent is null ||
            !bool.TryParse(isCoOwnersExistContent.ToString(), out var isCoOwnersExist) || !isCoOwnersExist)
            return;

        var coOwnersDetailsItems = coOwnerInfoItems!.Where(x => x.Identifier != CoOwnersConstants.CoOwners);

        var coOwnersDetails = coOwnersDetailsItems.Select(x =>
        {
            var coOwnerContent = JObject.FromObject(x.Content!);
            var einContent = coOwnerContent["ein"];
            var ssnContent = coOwnerContent["ssn"];

            var coOwner = JsonConvert.DeserializeObject<CoOwnerModel>(coOwnerContent.ToString())!;
            if (einContent is { Type: JTokenType.Object })
            {
                coOwner.Ein = JsonConvert.DeserializeObject<CipherModel>(einContent.ToString());
            }

            if (ssnContent is { Type: JTokenType.Object })
            {
                coOwner.Ssn = JsonConvert.DeserializeObject<CipherModel>(ssnContent.ToString());
            }

            coOwner.Identifier = x.Identifier!;
            return coOwner;
        });
        parsedDraft.CoOwnersInfo = coOwnersDetails.ToList()!;
    }

    private static void ParseBankInfo(Draft draft, ParsedDraftModel parsedDraft)
    {
        var bankInfoItems = draft.Data?.Bank?.Items?.ToList();

        var bankContent = bankInfoItems?.Find(x => x.Identifier == BankInfoConstants.DetailsIdentifier)?.Content;
        if (bankContent is null) return;
        var contentString = bankContent.ToString();
        if (string.IsNullOrEmpty(contentString)) return;

        parsedDraft.BankDetails =
            JsonConvert.DeserializeObject<BankDetailsModel>(JObject.FromObject(bankContent).ToString());
    }

    private static void ParseFinanceInfo(Draft draft, ParsedDraftModel parsedDraft)
    {
        var financeInfoItems = draft.Data?.Finance?.Items?.ToList();
        if (financeInfoItems is null) return;

        var revenueString = financeInfoItems?.Find(x => x.Identifier == FinanceConstants.Revenue)?.Content
            ?.ToString();
        var debtString = financeInfoItems?.Find(x => x.Identifier == FinanceConstants.Debt)?.Content?.ToString();
        var requestedAmountString = financeInfoItems?.Find(x => x.Identifier == FinanceConstants.RequestedAmount)
            ?.Content?.ToString();
        var arAdvanceRequestedString = financeInfoItems
            ?.Find(x => x.Identifier == FinanceConstants.ArAdvanceRequest)?.Content?.ToString();

        var isRevenueValid = decimal.TryParse(revenueString, out var revenue);
        var isDebtValid = decimal.TryParse(debtString, out var debt);
        var isRequestedAmountValid = decimal.TryParse(requestedAmountString, out var requestedAmount);
        var isArAdvanceRequestContentValid = bool.TryParse(arAdvanceRequestedString, out var arAdvanceRequested);

        parsedDraft.Revenue = isRevenueValid ? revenue : null;
        parsedDraft.Debt = isDebtValid ? debt : null;
        parsedDraft.RequestedAmount = isRequestedAmountValid ? requestedAmount : null;
        parsedDraft.ArAdvanceRequested = isArAdvanceRequestContentValid ? arAdvanceRequested : null;
    }
}
