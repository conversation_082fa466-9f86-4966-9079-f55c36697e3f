﻿using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

public interface IDrawApprovalRepository : IGenericRepository<DrawApprovalDocument>
{
    Task<GetQueryWithPaginationResult<DrawApprovalDocument>> GetAllByFiltersWithPagination(
        GetDrawApprovalsQueryWithPagination query, CancellationToken ct);

    Task<DrawApprovalDocument?> GetByInvoicesIds(string[] invoicesIds, CancellationToken ct);
    Task<IEnumerable<DrawApprovalDocument?>> GetManyByInvoicesIds(string[] invoicesIds, CancellationToken ct);
}
