﻿using Amazon;
using Amazon.Runtime;
using Amazon.SimpleNotificationService;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.LambdaBase;
using BlueTape.Services.OnBoardingService.Application.DI;
using BlueTape.Services.OnBoardingService.Compatibility.DI;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.DI;
using BlueTape.Services.Utilities.AWS;
using BlueTape.Services.Utilities.Options;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using EnvironmentConstants = BlueTape.Utilities.Constants.EnvironmentConstants;

namespace BlueTape.Functions.Hosting.Extensions;

public static class DependencyInjection
{
    private const string KeyVaultUri = "KEYVAULT_URI";
    public static IHostBuilder ConfigureBlueTapeFunctionAppHostConfiguration(this IHostBuilder hostBuilder)
    {
        return hostBuilder.ConfigureHostConfiguration(x =>
        {
            x.SetBasePath(Environment.CurrentDirectory);
            x.AddJsonFile("appsettings.json", optional: false);
            x.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment)}.json", optional: true);
            var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(KeyVaultUri) ??
                                      throw new ArgumentNullException(nameof(KeyVaultUri)));
            var azureCredentials = new DefaultAzureCredential();
            x.AddAzureKeyVault(keyVaultUri, azureCredentials, new AzureKeyVaultConfigurationOptions
            {
                ReloadInterval = TimeSpan.FromMinutes(EnvironmentConstants.MinutestKeyVaultReload)
            });
            x.AddEnvironmentVariables();
        });
    }

    public static IHostBuilder ConfigureBlueTapeServices(this IHostBuilder hostBuilder) =>
    hostBuilder.ConfigureBlueTapeServices(c => { });

    public static IHostBuilder ConfigureBlueTapeServices(
        this IHostBuilder hostBuilder,
        Action<IServiceCollection> configureDelegate)
    {
        return hostBuilder.ConfigureServices((hostBuilderContext, services) =>
        {
            var configuration = hostBuilderContext.Configuration;

            services.AddOptions();
            services.AddDefaultAWSOptions(configuration.GetAWSOptions());
            services.AddSingleton<IAmazonSimpleNotificationService>(sp =>
            {
                var awsCredentials = new BasicAWSCredentials(configuration["AWS-ACCESS-KEY-ID"], configuration["AWS-SECRET-ACCESS-KEY"]);
                var region = RegionEndpoint.GetBySystemName(configuration["AWS-DEFAULT-REGION"]);
                return new AmazonSimpleNotificationServiceClient(awsCredentials, region);
            });
            services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));
            services.AddSingleton<ISecretsManagerService, AwsSecretsManagerService>();
            services.AddSingleton<IKeyVaultService, KeyVaultService>();
            services.AddMemoryCache();
            services.AddApplicationDependencies(configuration);
            services.UseCompatibilityService(configuration);
            services.UseAuthorizationDetailsRefreshDetector(configuration);
            services.AddScoped<ITraceIdAccessor, LambdaTraceIdAccessor>();
            configureDelegate(services);
            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            if (!string.IsNullOrEmpty(env))
            {
                var serviceProvider = services.BuildServiceProvider();
                var keyVaultService = serviceProvider.GetRequiredService<IKeyVaultService>();
                var appInsightsConnectionString = keyVaultService.GetSecret("APP-INSIGHTS-CONNECTION").GetAwaiter().GetResult();

                services.AddApplicationInsightsTelemetryWorkerService(x => x.ConnectionString = appInsightsConnectionString);
                services.ConfigureFunctionsApplicationInsights();
            }

            services.Configure<LoggerFilterOptions>(options =>
            {
                var toRemove = options.Rules.FirstOrDefault(rule => rule.ProviderName
                                                                    == "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider");

                if (toRemove is not null)
                {
                    options.Rules.Remove(toRemove);
                }
            });
        });
    }

    public static IHostBuilder ConfigureBlueTapeSerilog(this IHostBuilder hostBuilder, string projectName)
    {
        return hostBuilder.UseSerilog((hostingContext, loggerConfiguration) =>
        {
            var config = hostingContext.Configuration;
            loggerConfiguration
                .ReadFrom.Configuration(hostingContext.Configuration)
                .Enrich.FromGlobalLogContext()
                .Enrich.WithProperty("ProjectName", projectName)
                .Enrich.WithProperty("EnvironmentName", hostingContext.HostingEnvironment.EnvironmentName)
                .Enrich.WithProperty("ContentRootPath", hostingContext.HostingEnvironment.ContentRootPath)
                .WriteTo.ApplicationInsights(config.GetSection("APP-INSIGHTS-CONNECTION").Value,
                    TelemetryConverter.Traces);

            loggerConfiguration.WriteTo.Console();
        });
    }

}
