﻿using BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;
public interface IParsedDraftService
{
    Task<ParsedDraftModel> MigrateDraft(string draftId, CancellationToken ct);
    Task<ParsedDraftModel> GetByDraftId(string draftId, CancellationToken ct);
    Task<ParsedDraftModel> GetByCompanyId(string draftId, CancellationToken ct);
    Task<ParsedDraftModel> GetById(string parsedDraftId, CancellationToken ct);
}
