﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class UpdateAccountAuthorization
{
    public string Id { get; set; } = string.Empty;

    public string CompanyId { get; set; } = string.Empty;

    public string EinHash { get; set; } = string.Empty;

    public BusinessDetailsUpdate BusinessDetails { get; set; } = new();

    public AccountAuthorizationCreditDetails CreditDetails { get; set; } = new();

    public IEnumerable<OwnersDetailsUpdate> OwnersDetails { get; set; } = Enumerable.Empty<OwnersDetailsUpdate>();

    public IEnumerable<BankAccountDetailsUpdate> BankAccountDetails { get; set; } = Enumerable.Empty<BankAccountDetailsUpdate>();

    public IEnumerable<OwnersEntitiesDetailsUpdate> OwnersEntitiesDetails { get; set; } = Enumerable.Empty<OwnersEntitiesDetailsUpdate>();

    public string UpdatedBy { get; set; } = string.Empty;

    public string CreditApplicationId { get; set; } = string.Empty;

}