﻿namespace BlueTape.Services.OnBoardingService.Application.Constants.DraftParser;

public static class BusinessOwnerConstants
{
    public const string OwnerAddress = "address";

    public const string OwnerBirthdate = "birthdate";

    public const string FirstName = "firstName";

    public const string Id = "id";

    public const string LastName = "lastName";

    public const string Email = "email";

    public const string Phone = "phone";

    public const string Ssn = "ssn";

    public const string OwnerIdentifier = "Owner";

    public const string OwnerId = "OwnerId";

    public const string OwnerPercentage = "ownershipPercentage";

    public const string IsOwner = "isOwner";

    public const string IsOwnerPrincipal = "Yes";
}
