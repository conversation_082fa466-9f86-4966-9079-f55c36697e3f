﻿using BlueTape.Services.OnBoardingService.Domain.Serializers;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class InvoiceDetails
{
    [BsonSerializer(typeof(ArrayAndStringSerializer))]
    [BsonElement("invoiceId")]
    public List<string>? InvoiceIds { get; set; }

    [BsonSerializer(typeof(StringSerializer))]
    [BsonElement("paymentPlan")]
    public string? PaymentPlan { get; set; }

    [BsonId]
    public ObjectId? Id { get; set; }
}
