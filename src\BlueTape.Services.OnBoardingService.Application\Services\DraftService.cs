﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Services
{
    public class DraftService : IDraftService
    {
        private readonly IDraftRepository _draftRepository;
        private readonly IMapper _mapper;

        public DraftService(IDraftRepository draftRepository, IMapper mapper)
        {
            _draftRepository = draftRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<Draft>> GetAll(CancellationToken ct)
        {
            var drafts = await _draftRepository.GetAll(ct);

            return _mapper.Map<IEnumerable<Draft>>(drafts);
        }

        public async Task<IEnumerable<Draft>> GetAllByFilters(DraftFilter filter, CancellationToken ct)
        {
            var drafts = await _draftRepository.GetAllByFilters(filter.Id, filter.CompanyId, filter.CreditApplicationId, ct);

            return _mapper.Map<IEnumerable<Draft>>(drafts);
        }

        public async Task<IEnumerable<Draft>> GetByCompanyIds(string[] companyIds, CancellationToken ct)
        {
            var drafts = await _draftRepository.GetByCompanyIds(companyIds, ct);

            return _mapper.Map<IEnumerable<Draft>>(drafts);
        }

        public async Task<Draft> GetById(string id, CancellationToken ct)
        {
            var draft = await _draftRepository.GetById(id, ct);

            return _mapper.Map<Draft>(draft);
        }
    }
}
