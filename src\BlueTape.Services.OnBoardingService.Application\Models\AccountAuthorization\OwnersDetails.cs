﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class OwnersDetails : OwnerDetailsBase
{
    public string? FraudpointScore { get; set; }

    public string? EmailRiskScore { get; set; }

    public DateOnly Birthday { get; set; }

    public string SsnHash { get; set; } = string.Empty;

    public string FirstName { get; set; } = string.Empty;

    public string LastName { get; set; } = string.Empty;

    public string? B2ELinkIndex { get; set; }

    public DateOnly? LastSSNRejectionDate { get; set; }

    public IEnumerable<string?> CRICodes { get; set; } = Enumerable.Empty<string?>();

    public string? CVI { get; set; }

    public string? FICOScore { get; set; }

    public DateOnly? LastPersonalBankruptcyDate { get; set; }

    public string? IPRiskLevel { get; set; }

    public string? DomainRiskLevel { get; set; }

    public DateOnly? LastEINRejectionDate { get; set; }
}
