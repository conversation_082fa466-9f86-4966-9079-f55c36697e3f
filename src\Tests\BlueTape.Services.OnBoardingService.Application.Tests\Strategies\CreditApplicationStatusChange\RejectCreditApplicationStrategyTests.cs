﻿using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Strategies.CreditApplicationStatusChange;

public class RejectCreditApplicationStrategyTests
{
    private readonly RejectCreditApplicationStrategy _strategy;

    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock;
    private readonly Mock<ICreditApplicationSyncService> _creditApplicationSyncServiceMock;
    private readonly Mock<IAccountAuthorizationsService> _accountAuthServiceMock;
    private readonly Mock<ICreditApplicationNotesService> _creditApplicationNotesServiceMock;
    public RejectCreditApplicationStrategyTests()
    {
        _dateProviderMock = new();
        _creditApplicationRepositoryMock = new();

        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        var mapper = new Mapper(mapperConfig);
        Mock<IAccountStatusService> accountStatusServiceMock = new();
        _creditApplicationRepositoryMock = new();
        _creditApplicationSyncServiceMock = new();
        _creditApplicationNotesServiceMock = new();
        _accountAuthServiceMock = new();
        Mock<ILogger<RejectCreditApplicationStrategy>> loggerMock = new();

        _strategy = new RejectCreditApplicationStrategy(
            _dateProviderMock.Object,
            mapper,
            accountStatusServiceMock.Object,
            _creditApplicationRepositoryMock.Object,
            _accountAuthServiceMock.Object,
            _creditApplicationSyncServiceMock.Object,
            _creditApplicationNotesServiceMock.Object,
            loggerMock.Object);
    }

    [Fact]
    public void IsApplicable_RejectedStatus_ReturnsTrue()
    {
        var result = _strategy.IsApplicable(CreditApplicationStatus.Rejected.ToString());

        result.ShouldBe(true);
    }

    [Fact]
    public void IsApplicable_NotRejectedStatus_ReturnsFalse()
    {
        var result = _strategy.IsApplicable(CreditApplicationStatus.Processed.ToString());

        result.ShouldBe(false);
    }

    [Theory, CustomAutoData]
    public async Task ChangeStatus_UpdatesCreditApplication(CreditApplicationDocument creditApplication, ReviewCreditApplicationDto reviewCreditApplication)
    {
        var userId = "userId";
        var now = DateTime.UtcNow;
        _creditApplicationRepositoryMock.Setup(x => x.Update(creditApplication, default)).ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);

        var result = await _strategy.ChangeStatus(creditApplication, reviewCreditApplication, userId, default);

        _creditApplicationSyncServiceMock.Verify(x => x.SyncApplicableCreditApplication(creditApplication, default), Times.Once);
        _accountAuthServiceMock.Verify(x => x.RejectOwners(creditApplication.CompanyId!, creditApplication.Id, userId, default));

        result.RejectedAt.ShouldBe(now);
        result.RejectedBy.ShouldBe(userId);
        result.StatusCode.ShouldBe(reviewCreditApplication.Code);
        result.StatusNote.ShouldBe(reviewCreditApplication.Note);
        result.Status.ShouldBe(CreditApplicationStatus.Rejected.ToString().ToLower());
    }

    [Theory, CustomAutoData]
    public async Task ChangeStatus_StatusNoteShouldBeGenerated_GeneratesStatusNote(CreditApplicationDocument creditApplication, ReviewCreditApplicationDto reviewCreditApplication)
    {
        var userId = "userId";
        var now = DateTime.UtcNow;
        creditApplication.Type = CreditApplicationType.ARAdvance.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.Update(creditApplication, default)).ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);

        await _strategy.ChangeStatus(creditApplication, reviewCreditApplication, userId, default);

        _creditApplicationNotesServiceMock.
            Verify(x => x.Add(It.Is<CreateCreditApplicationNote>(note => note.Note == reviewCreditApplication.Note), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task ChangeStatus_StatusNoteShouldNotBeGenerated_DoesNotGenerateStatusNote(CreditApplicationDocument creditApplication, ReviewCreditApplicationDto reviewCreditApplication)
    {
        var userId = "userId";
        var now = DateTime.UtcNow;
        creditApplication.Type = CreditApplicationType.LineOfCredit.ToString();
        _creditApplicationRepositoryMock.Setup(x => x.Update(creditApplication, default)).ReturnsAsync(creditApplication);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(now);

        await _strategy.ChangeStatus(creditApplication, reviewCreditApplication, userId, default);

        _creditApplicationNotesServiceMock.
            Verify(x => x.Add(It.Is<CreateCreditApplicationNote>(note => note.Note == reviewCreditApplication.Note), default), Times.Never);
    }

    [Theory, CustomAutoData]
    public Task ChangeStatus_RejectionCodeEmpty_ThrowsValidationException(CreditApplicationDocument creditApplication, ReviewCreditApplicationDto reviewCreditApplication)
    {
        reviewCreditApplication.Code = string.Empty;

        var act = async () => await _strategy.ChangeStatus(creditApplication, reviewCreditApplication, Guid.NewGuid().ToString(), default);

        return act.ShouldThrowAsync<ValidationException>();
    }
}
