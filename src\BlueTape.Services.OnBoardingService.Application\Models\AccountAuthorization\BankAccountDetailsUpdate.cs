﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
public class BankAccountDetailsUpdate
{
    public string Id { get; set; } = string.Empty;
    public string Identifier { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal CompanyNameScore { get; set; }
    public decimal PersonalNameScore { get; set; }
    public decimal CompanyAddressScore { get; set; }
    public decimal PersonalAddressScore { get; set; }
    public decimal NameScore { get; set; }
    public decimal AddressScore { get; set; }
}
