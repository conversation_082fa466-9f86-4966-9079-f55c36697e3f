﻿using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Compatibility.Models;

public class DrawRequest
{
    [BsonElement("creditId")]
    public string? CreditId { get; set; }

    [BsonElement("companyId")]
    public string? CompanyId { get; set; }

    [BsonElement("paymentPlanId")]
    public string PaymentPlanId { get; set; } = string.Empty;

    [BsonElement("projectId")]
    public string? ProjectId { get; set; }

    [BsonElement("drawAmount")]
    public double DrawAmount { get; set; }

    [BsonElement("payables")]
    public IList<PayableItem> Payables { get; set; } = new List<PayableItem>();
}
