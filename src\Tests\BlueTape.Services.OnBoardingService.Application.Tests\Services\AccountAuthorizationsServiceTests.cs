﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.Application.Tests.DTOs.AccountAuthorization;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class AccountAuthorizationsServiceTests
{
    private readonly IAccountAuthorizationsService _accountAuthorizationsService;

    private readonly Mock<IAccountAuthorizationsRepository> _accountAuthorizationsRepositoryMock = new();
    private readonly Mock<IAccountAuthorizationDetailsChangesService> _accountAuthorizationDetailsChangesServiceMock = new();
    private readonly Mock<DateProvider> _dateProviderMock = new();

    public AccountAuthorizationsServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        IMapper mapper = new Mapper(mapperConfig);

        _accountAuthorizationsService = new AccountAuthorizationsService(
            _accountAuthorizationsRepositoryMock.Object,
            _accountAuthorizationDetailsChangesServiceMock.Object,
            mapper, _dateProviderMock.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetById_AccountAuthorizationIsExist_ReturnsDraft(string id, AccountAuthorizationDocument accountAuthorization)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(accountAuthorization);

        var result = await _accountAuthorizationsService.GetById(id, default);

        result.CompanyId.ShouldBeEquivalentTo(accountAuthorization.CompanyId);
        result.Id.ShouldBeEquivalentTo(accountAuthorization.Id);
    }

    [Theory, CustomAutoData]
    public async Task GetById_AccountAuthorizationIsNull_ReturnsNull(string id)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetById(id, default));

        var result = await _accountAuthorizationsService.GetById(id, default);

        result.ShouldBeNull();
    }

    [Theory, CustomAutoData]
    public async Task GetAll_Valid_ReturnsAccountAuthorizations(List<AccountAuthorizationDocument> accountAuthorizations)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAll(default))
            .ReturnsAsync(accountAuthorizations);

        var result = await _accountAuthorizationsService.GetAll(default);

        result.Count().ShouldBe(accountAuthorizations.Count);
        result.First().CompanyId.ShouldBeEquivalentTo(accountAuthorizations.First().CompanyId);
    }

    [Fact]
    public async Task GetAll_AccountAuthorizationsIsNotExist_ReturnsEmptyList()
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAll(default))
            .ReturnsAsync(new List<AccountAuthorizationDocument>());

        var result = await _accountAuthorizationsService.GetAll(default);

        result.Count().ShouldBe(0);
    }

    [Theory, CustomAutoData]
    public async Task Create_Valid_ReturnsAccountAuthorizations(AccountAuthorizationDocument accountAuthorizationDocument,
        CreateAccountAuthorization createAccountAuthorization)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.Add(It.IsAny<AccountAuthorizationDocument>(), default))
            .ReturnsAsync(accountAuthorizationDocument);

        var result = await _accountAuthorizationsService.Create(createAccountAuthorization, default);

        result.CompanyId.ShouldBeEquivalentTo(accountAuthorizationDocument.CompanyId);
    }

    [Theory, CustomAutoData]
    public async Task Create_AccountAuthorizationsForCompanyExists_ReturnsAccountAuthorizations(AccountAuthorizationDocument accountAuthorizationDocument,
        CreateAccountAuthorization createAccountAuthorization)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(null, createAccountAuthorization.CompanyId, createAccountAuthorization.EinHash, null, default))
            .ReturnsAsync(new List<AccountAuthorizationDocument>()
            {
                accountAuthorizationDocument
            });
        _accountAuthorizationsRepositoryMock.Setup(x => x.Add(It.IsAny<AccountAuthorizationDocument>(), default))
            .ReturnsAsync(accountAuthorizationDocument);

        var result = await _accountAuthorizationsService.Create(createAccountAuthorization, default);

        result.CompanyId.ShouldBe(accountAuthorizationDocument.CompanyId);
        result.EinHash.ShouldBe(accountAuthorizationDocument.EinHash);
        result.BusinessDetails.BRICodes.ShouldBe(accountAuthorizationDocument.BusinessDetails.BRICodes);
    }

    [Theory, CustomAutoData]
    public async Task Update_ValidPutData_ReturnsAccountAuthorizations(AccountAuthorizationDocument accountAuthorizationDocument,
        UpdateAccountAuthorization updateAccountAuthorization)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocument);
        _accountAuthorizationsRepositoryMock.Setup(x => x.Update(It.IsAny<AccountAuthorizationDocument>(), default))
            .ReturnsAsync(accountAuthorizationDocument);
        _accountAuthorizationDetailsChangesServiceMock.Setup(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(),
                updateAccountAuthorization.CreditApplicationId,It.IsAny<ExecutionChangeType>(), CancellationToken.None));

        var result = await _accountAuthorizationsService.Update(updateAccountAuthorization, default);

        result.CompanyId.ShouldBeEquivalentTo(accountAuthorizationDocument.CompanyId);
        _accountAuthorizationDetailsChangesServiceMock.Verify(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(),
                updateAccountAuthorization.CreditApplicationId,It.IsAny<ExecutionChangeType>(), CancellationToken.None), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task Patch_ValidPatchData_ReturnsAccountAuthorizations(AccountAuthorizationDocument accountAuthorizationDocument,
        PatchAccountAuthorization patchAccountAuthorization)
    {
        accountAuthorizationDocument.IsSentBack = true;
        patchAccountAuthorization.IsSentBack = null;

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetById(It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocument);
        _accountAuthorizationDetailsChangesServiceMock.Setup(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(),
                patchAccountAuthorization.CreditApplicationId, It.IsAny<ExecutionChangeType>(), CancellationToken.None));

        _accountAuthorizationsRepositoryMock.Setup(x => x.Update(It.IsAny<AccountAuthorizationDocument>(), default))
            .ReturnsAsync(accountAuthorizationDocument);
        var result = await _accountAuthorizationsService.Patch(patchAccountAuthorization, default);

        result.CompanyId.ShouldBeEquivalentTo(accountAuthorizationDocument.CompanyId);
        result.IsSentBack.ShouldBe(true);
        result.BankAccountDetails.ToList()[3].Identifier.ShouldBe("account4");
        result.BankAccountDetails.ToList()[4].Identifier.ShouldBe("account5");
        _accountAuthorizationDetailsChangesServiceMock.Verify(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(),
                patchAccountAuthorization.CreditApplicationId, It.IsAny<ExecutionChangeType>(),  CancellationToken.None), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_IdIsNull_ReturnsAccountAuthorizations(List<AccountAuthorizationDocument> accountAuthorizationDocuments)
    {
        var accountAuthorization = ValidAccountAuthorizations.ListOfAccountAuthorizationsForIdIsNullScenario;
        var firstAccountAuthorization = accountAuthorization.First();

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocuments);

        var result = await _accountAuthorizationsService.GetAllByFilters(null, firstAccountAuthorization.CompanyId, firstAccountAuthorization.EinHash, null, default);

        result.Count().ShouldBe(accountAuthorizationDocuments.Count);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_CompanyIdIsNull_ReturnsAccountAuthorizations(List<AccountAuthorizationDocument> accountAuthorizationDocuments)
    {
        var accountAuthorization = ValidAccountAuthorizations.ListOfAccountAuthorizationsForCompanyIdIsNullScenario;
        var firstAccountAuthorization = accountAuthorization.First();

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocuments);

        var result = await _accountAuthorizationsService.GetAllByFilters(firstAccountAuthorization.Id, null, firstAccountAuthorization.EinHash, null, default);

        result.Count().ShouldBe(accountAuthorizationDocuments.Count);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_EinHashIsNull_ReturnsAccountAuthorizations(List<AccountAuthorizationDocument> accountAuthorizationDocuments)
    {
        var accountAuthorization = ValidAccountAuthorizations.ListOfAccountAuthorizationsForEinHashIsNullScenario;
        var firstAccountAuthorization = accountAuthorization.First();

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocuments);

        var result = await _accountAuthorizationsService.GetAllByFilters(firstAccountAuthorization.Id, firstAccountAuthorization.CompanyId, null, null, default);

        result.Count().ShouldBe(accountAuthorizationDocuments.Count);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_IdAndCompanyIdAreNull_ReturnsAccountAuthorizations(List<AccountAuthorizationDocument> accountAuthorizationDocuments)
    {
        var accountAuthorization = ValidAccountAuthorizations.ListOfAccountAuthorizationsForIdAndCompanyIdAreNullScenario;
        var firstAccountAuthorization = accountAuthorization.First();

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocuments);

        var result = await _accountAuthorizationsService.GetAllByFilters(null, null, firstAccountAuthorization.EinHash, null, default);

        result.Count().ShouldBe(accountAuthorizationDocuments.Count);
    }

    [Theory, CustomAutoData]
    public async Task GetAllByFilters_IdAndCompanyIdAndEinHashAreNull_ReturnsAccountAuthorizations(List<AccountAuthorizationDocument> accountAuthorizationDocuments)
    {

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default))
            .ReturnsAsync(accountAuthorizationDocuments);

        var result = await _accountAuthorizationsService.GetAllByFilters(null, null, null, null, default);

        result.Count().ShouldBe(accountAuthorizationDocuments.Count);
    }

    [Theory, CustomAutoData]
    public async Task RejectOwners_ValidCase_ShouldReturn(List<AccountAuthorizationDocument> accAuths, string companyId, string creditAppId, string userId)
    {
        var currentDateTime = DateTime.UtcNow;
        accAuths.First().CreatedAt = currentDateTime;
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(null, companyId, null, null, default)).ReturnsAsync(accAuths);

        _accountAuthorizationsRepositoryMock.Setup(x => x.Update(It.Is<AccountAuthorizationDocument>(x =>
            x.UpdatedBy == userId &&
            x.UpdatedAt == currentDateTime &&
            x.OwnersDetails.All(o => o.LastSSNRejectionDate == currentDateTime)), default));
        _accountAuthorizationDetailsChangesServiceMock.Setup(x
            => x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(It.IsAny<AccountAuthorizationDocument>(),
                It.IsAny<AccountAuthorizationDocument>(), creditAppId, It.IsAny<ExecutionChangeType>(), default));

        await _accountAuthorizationsService.RejectOwners(companyId, creditAppId, userId, default);

        _accountAuthorizationsRepositoryMock.Verify(x => x.GetAllByFilters(null, companyId, null, null, default), Times.Once);
        _accountAuthorizationsRepositoryMock.Verify(x => x.Update(It.IsAny<AccountAuthorizationDocument>(), default), Times.Once());
        _accountAuthorizationDetailsChangesServiceMock.Verify(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(It.IsAny<AccountAuthorizationDocument>(),
                It.IsAny<AccountAuthorizationDocument>(), creditAppId, It.IsAny<ExecutionChangeType>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task RejectOwners_OwnersNotFound_ShouldReturn(List<AccountAuthorizationDocument> accAuths, string companyId, string creditAppId, string userId)
    {
        var currentDateTime = DateTime.UtcNow;
        accAuths.First().CreatedAt = currentDateTime;
        accAuths.First().OwnersDetails = Enumerable.Empty<OwnersDetailsDocument>();
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetAllByFilters(null, companyId, null, null, default)).ReturnsAsync(accAuths);

        _accountAuthorizationsRepositoryMock.Setup(x => x.Update(It.Is<AccountAuthorizationDocument>(x =>
            x.UpdatedBy == userId &&
            x.UpdatedAt == currentDateTime &&
            x.OwnersDetails.All(o => o.LastSSNRejectionDate == currentDateTime)), default));
        _accountAuthorizationDetailsChangesServiceMock.Setup(x
            => x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(), creditAppId, It.IsAny<ExecutionChangeType>(), default));

        await _accountAuthorizationsService.RejectOwners(companyId, creditAppId, userId, default);

        _accountAuthorizationsRepositoryMock.Verify(x => x.GetAllByFilters(null, companyId, null, null, default), Times.Once);
        _accountAuthorizationsRepositoryMock.Verify(x => x.Update(It.IsAny<AccountAuthorizationDocument>(), default), Times.Never);
        _accountAuthorizationDetailsChangesServiceMock
            .Verify(x => x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(), creditAppId, It.IsAny<ExecutionChangeType>(), default), Times.Never);
    }

    [Theory, CustomAutoData]
    public async Task Nullify_ValidData_ReturnsAccountAuthorizations(string id, NullifyAccountAuthorization model, AccountAuthorizationDocument accAuth)
    {
        var expectedAccAuth = new AccountAuthorizationDocument
        {
            Id = accAuth.Id,
            CreatedAt = accAuth.CreatedAt,
            UpdatedAt = accAuth.UpdatedAt,
            CreatedBy = accAuth.CreatedBy,
            UpdatedBy = model.UpdatedBy,
            CompanyId = accAuth.CompanyId,
            EinHash = accAuth.EinHash,
            IsSentBack = accAuth.IsSentBack,
            BusinessDetails = new BusinessDetailsDocument()
            {
                LastEINRejectionDate = accAuth.BusinessDetails.LastEINRejectionDate
            },
            OwnersDetails = accAuth.OwnersDetails.Select(x => new OwnersDetailsDocument
            {
                Id = x.Id,
                Identifier = x.Identifier,
                PercentOwned = x.PercentOwned,
                SsnHash = x.SsnHash,
                IsPrincipal = x.IsPrincipal,
                LastSSNRejectionDate = x.LastSSNRejectionDate
            }),
            BankAccountDetails = []
        };

        _accountAuthorizationsRepositoryMock.Setup(x => x.GetById(id, default))
            .ReturnsAsync(accAuth);

        _accountAuthorizationDetailsChangesServiceMock.Setup(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(),
                model.CreditApplicationId, It.IsAny<ExecutionChangeType>(), CancellationToken.None));

        _accountAuthorizationsRepositoryMock.Setup(x => x.Update(It.IsAny<AccountAuthorizationDocument>(), default))
            .ReturnsAsync(expectedAccAuth);

        var result = await _accountAuthorizationsService.Nullify(id, model, default);
        result.BusinessDetails.LastEINRejectionDate.ShouldBeEquivalentTo(DateOnly.FromDateTime(expectedAccAuth.BusinessDetails.LastEINRejectionDate!.Value));
        result.BankAccountDetails.Count().ShouldBeEquivalentTo(expectedAccAuth.BankAccountDetails.Count());
        result.OwnersDetails.Count().ShouldBeEquivalentTo(expectedAccAuth.OwnersDetails.Count());
        result.OwnersDetails.First().Id.ShouldBeEquivalentTo(expectedAccAuth.OwnersDetails.First().Id);
        result.OwnersDetails.First().Identifier.ShouldBeEquivalentTo(expectedAccAuth.OwnersDetails.First().Identifier);
        result.OwnersDetails.First().PercentOwned.ShouldBeEquivalentTo(expectedAccAuth.OwnersDetails.First().PercentOwned);
        result.OwnersDetails.First().LastSSNRejectionDate.ShouldBeEquivalentTo(DateOnly.FromDateTime(expectedAccAuth.OwnersDetails.First().LastSSNRejectionDate!.Value));
        result.OwnersDetails.First().IsPrincipal.ShouldBeEquivalentTo(expectedAccAuth.OwnersDetails.First().IsPrincipal);

        _accountAuthorizationsRepositoryMock.Verify(x => x.GetById(id, default), Times.Once());

        _accountAuthorizationDetailsChangesServiceMock.Verify(x =>
            x.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
                It.IsAny<AccountAuthorizationDocument>(), It.IsAny<AccountAuthorizationDocument>(),
                model.CreditApplicationId, It.IsAny<ExecutionChangeType>(), CancellationToken.None), Times.Once());

        _accountAuthorizationsRepositoryMock.Verify(x => x.Update(It.IsAny<AccountAuthorizationDocument>(), default), Times.Once());
    }

    [Theory, CustomAutoData]
    public async Task GetByEinHashes_ValidRequest_ShouldReturnList(List<AccountAuthorizationDocument> docs, string[] einHashes)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetByEinHashes(einHashes, default)).ReturnsAsync(docs);
        var result = await _accountAuthorizationsService.GetByEinList(einHashes, default);

        result.Count().ShouldBe(docs.Count);
        _accountAuthorizationsRepositoryMock.Verify(x => x.GetByEinHashes(einHashes, default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task GetBySsnHashes_ValidRequest_ShouldReturnList(List<AccountAuthorizationDocument> docs, string[] ssnHashes)
    {
        _accountAuthorizationsRepositoryMock.Setup(x => x.GetBySsnHashes(ssnHashes, default)).ReturnsAsync(docs);
        var result = await _accountAuthorizationsService.GetBySsnList(ssnHashes, default);

        result.Count().ShouldBe(docs.Count);
        _accountAuthorizationsRepositoryMock.Verify(x => x.GetBySsnHashes(ssnHashes, default), Times.Once);
    }
}