﻿namespace BlueTape.Services.OnBoardingService.Application.Models.IntegrationLogs;

public class RequestResponseBaseModel
{

    public string? Id { get; set; }

    public bool Success { get; set; }

    public string? CachingParameter { get; set; }

    public string? RequestUrl { get; set; }

    public string? Request { get; set; }

    public ResponseModel? Response { get; set; }

    public string? RequestType { get; set; }

    public string? TraceId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string? Reference { get; set; }
}
