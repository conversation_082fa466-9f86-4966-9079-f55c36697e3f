using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;

public class PatchCreditApplicationAdminModel
{
    public string? Id { get; set; } = string.Empty;
    public decimal? ApprovedCreditLimit { get; set; }
    public string? PurchaseTypeOption { get; set; }
    public double? RevenueFallPercentage { get; set; }
    public bool? IsAchDelay { get; set; }
    public string? CardPricingPackageId { get; set; }
    public string? LoanPricingPackageId { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
    public string? Code { get; set; }
    public string? Note { get; set; }
    public DebtInvestorType? DebtInvestor { get; set; }
    public bool? IsSecured { get; set; }
    public decimal? DepositAmount { get; set; }
}