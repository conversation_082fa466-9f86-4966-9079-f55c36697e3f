﻿namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class CreditApplicationAuthorizationDetails
{
    public string? Id { get; set; }
    public string CreditApplicationId { get; set; } = string.Empty;
    public AccountAuthorization? AccountAuthorizationDetailsSnapshot { get; set; }
}