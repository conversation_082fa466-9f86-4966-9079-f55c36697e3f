﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="local.settings.json" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="1.18.1" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\BlueTape.Functions.Hosting\BlueTape.Functions.Hosting.csproj" />
		<ProjectReference Include="..\BlueTape.Functions.AuthorizationDetailsRefreshDetector\BlueTape.Functions.AuthorizationDetailsRefreshDetector.csproj" />
		<ProjectReference Include="..\BlueTape.Functions.AuthorizationDetailsRefreshService\BlueTape.Functions.AuthorizationDetailsRefreshService.csproj" />
	</ItemGroup>
</Project>