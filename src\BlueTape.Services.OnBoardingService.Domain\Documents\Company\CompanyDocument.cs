﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Company;

[BsonIgnoreExtraElements]
[MongoCollection("companies")]
public class CompanyDocument : Document
{
    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("email")]
    public string? Email { get; set; }

    [BsonElement("credit")]
    public Credit? Credit { get; set; }

    [BsonElement("settings")]
    public CompanySettingsDocument? Settings { get; set; }

    [BsonElement("bankAccounts")]
    public List<ObjectId> BankAccounts { get; set; } = null!;

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("legalName")]
    public string? LegalName { get; set; }
}
