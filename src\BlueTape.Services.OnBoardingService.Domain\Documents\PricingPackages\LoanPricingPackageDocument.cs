﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.PricingPackages;

[BsonIgnoreExtraElements]
[MongoCollection("loanpricingpackages")]
public class LoanPricingPackageDocument : Document
{
    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("title")]
    public string? Title { get; set; }

    [BsonElement("description")]
    public string? Description { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("product")]
    public string? Product { get; set; }
}
