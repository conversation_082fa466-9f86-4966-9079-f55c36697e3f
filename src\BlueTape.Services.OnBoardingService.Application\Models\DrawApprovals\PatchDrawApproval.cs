﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class PatchDrawApproval
{
    public string Id { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public string? CompanyId { get; set; }
    public string? EinHash { get; set; }
    public string? CreditId { get; set; }
    public string? ArAdvanceCreditId { get; set; }
    public string? InHouseCreditId { get; set; }
    public string? PaymentPlanId { get; set; }
    public string? CreditApplicationId { get; set; }
    public DrawApprovalType? Type { get; set; }
    public LoanOrigin? LoanOrigin { get; set; }
    public string? VirtualCardId { get; set; }
    public string? MerchantId { get; set; }
    public string? MerchantName { get; set; }
    public string? ExecutionId { get; set; }
    public decimal? DrawAmount { get; set; }
    public decimal? CreditHoldAmount { get; set; }
    public DateTime? ExpirationDate { get; set; }
    public string? AuthorizationPeriodId { get; set; }
    public string? ArAdvanceAuthorizationPeriodId { get; set; }
    public DrawAmountRiskLevel? DrawAmountRiskLevel { get; set; }
    public string? ProjectId { get; set; }
    public AutomatedDecisionResult? AutomatedDecisionResult { get; set; }
    public AutomatedApprovalResult? AutomatedApprovalResult { get; set; }
    public DrawApprovalStatus? Status { get; set; }
    public DebtInvestorType? DebtInvestor { get; set; }
    public DateTime? LastStatusChangedAt { get; set; }
    public string? LastStatusChangedBy { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? RejectedAt { get; set; }
    public string? RejectedBy { get; set; }
    public DateTime? LastRerunAt { get; set; }
    public string? LastRerunBy { get; set; }
    public PatchDrawDetails? DrawDetails { get; set; }
    public FactoringDetailsModel? FactoringDetails { get; set; }
    public FactoringOverallDetailsModel? FactoringOverallDetails { get; set; }
    public PayNowDetailsModel? PayNowDetails { get; set; }
    public AutomatedApprovalDetailsModel? AutomatedApprovalDetails { get; set; }
    public DownPaymentDetails? DownPaymentDetails { get; set; }
}
