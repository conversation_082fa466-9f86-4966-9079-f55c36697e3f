using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.User;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CustomerAccountsServiceTests
{
    private readonly CustomerAccountsService _customerAccountsService;
    private readonly Mock<IUserService> _userService = new();
    private readonly Mock<ICustomerAccountRepository> _repository = new();

    public CustomerAccountsServiceTests()
    {
        _customerAccountsService = new CustomerAccountsService(_repository.Object, _userService.Object);
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyId_UserIsFound_ShouldReturnId(User user, string companyId, string[] customerAccountId)
    {
        _userService.Setup(x => x.GetByCompanyId(companyId, default)).ReturnsAsync(user);
        _repository.Setup(x => x.GetIdsByLogin(user.Login, default)).ReturnsAsync(customerAccountId);

        var result = await _customerAccountsService.GetCustomerAccountIdsByCompanyId(companyId, default);
        result.ShouldBeEquivalentTo(customerAccountId);

        _userService.Verify(x => x.GetByCompanyId(companyId, default), Times.Once);
        _repository.Verify(x => x.GetIdsByLogin(user.Login, default), Times.Once); ;
    }

    [Theory, CustomAutoData]
    public async Task GetByCompanyId_UserNotFound_EmptyString(User user, string companyId)
    {
        _userService.Setup(x => x.GetByCompanyId(companyId, default)).ReturnsAsync((User?)null);

        var result = await _customerAccountsService.GetCustomerAccountIdsByCompanyId(companyId, default);
        result.ShouldBeEmpty();

        _userService.Verify(x => x.GetByCompanyId(companyId, default), Times.Once);
        _repository.Verify(x => x.GetIdsByLogin(user.Login, default), Times.Never);
    }
}