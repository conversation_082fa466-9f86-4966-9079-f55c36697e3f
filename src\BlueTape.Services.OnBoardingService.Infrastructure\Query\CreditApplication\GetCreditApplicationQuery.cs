﻿namespace BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;

public class GetCreditApplicationQuery
{
    public string? Id { get; set; }
    public string? CompanyId { get; set; }

    public string? MerchantId { get; set; }
    public string? EinHash { get; set; }
    public string? Name { get; set; }
    public string? MerchantName { get; set; }
    public DateTime? AppDateFrom { get; set; }
    public DateTime? AppDateTo { get; set; }

    public string[]? Status { get; set; }
    public string[]? AutomatedDecision { get; set; }
    public string[]? Category { get; set; }
    public string[]? Type { get; set; }
    public string[]? BankAccountType { get; set; }
    public string[]? SsnHashes { get; set; }
    public string? SortBy { get; set; }
    public string? SortOrder { get; set; }
    public bool? CreditDetails { get; set; }

    // For internal usage
    public string[]? ArAdvanceApplicationIds { get; set; }
    public string[]? CompanyIds { get; set; }
}
