﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorizationDetailsChanges;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class AccountAuthorizationsChangesRepository : IAccountAuthorizationsChangesRepository
{
    private readonly ILogger _logger;
    private readonly IMongoCollection<AccountAuthorizationDetailsChangesDocument> _collection;

    public AccountAuthorizationsChangesRepository(
        ILogger<AccountAuthorizationsRepository> logger,
        IObsMongoDBContext context)
    {
        _logger = logger;
        _collection = context.GetCollection<AccountAuthorizationDetailsChangesDocument>();
    }

    public async Task<IEnumerable<AccountAuthorizationDetailsChangesDocument>> AddRange(IEnumerable<AccountAuthorizationDetailsChangesDocument> entities,
        CancellationToken ct)
    {
        _logger.LogInformation("Add new account authorization changes document");
        await _collection.InsertManyAsync(entities, cancellationToken: ct);
        return entities;
    }
}
