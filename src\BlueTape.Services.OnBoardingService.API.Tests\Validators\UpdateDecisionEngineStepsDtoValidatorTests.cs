﻿using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.API.Validators.DecisionEngineSteps.UpdateDecisionEngineSteps;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.API.Tests.Validators;

public class UpdateDecisionEngineStepsDtoValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new UpdateDecisionEngineStepsDtoValidator();

        var model = new UpdateDecisionEngineStepsDto()
        {
            NewStatus = "str",
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDto>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_InvalidModelMissedNewStatus_ReturnsFalse()
    {
        var validator = new UpdateDecisionEngineStepsDtoValidator();

        var model = new UpdateDecisionEngineStepsDto()
        {
            UpdatedBy = "by me",
            Results = new List<DecisionEngineStepResultDto>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth"
                }
            }
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_InvalidModelMissedUpdatedBy_ReturnsFalse()
    {
        var validator = new UpdateDecisionEngineStepsDtoValidator();

        var model = new UpdateDecisionEngineStepsDto()
        {
            UpdatedBy = string.Empty,
            NewStatus = "str",
            Results = new List<DecisionEngineStepResultDto>()
            {
                new()
                {
                    Code = "code",
                    ComparisonJustification = "smth",
                }
            }
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_InvalidModelMissedResults_ReturnsFalse()
    {
        var validator = new UpdateDecisionEngineStepsDtoValidator();

        var model = new UpdateDecisionEngineStepsDto()
        {
            NewStatus = "str",
            UpdatedBy = "by me",
            Results = null
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }
}
