﻿using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions
{
    public interface ICreditApplicationService
    {
        Task<IEnumerable<CreditApplication>> GetAllByFilters(GetCreditApplicationQuery query, CancellationToken ct);

        Task<IEnumerable<CreditApplication>> CreateRange(IEnumerable<CreateCreditApplication> createCreditApplicationCollection, CancellationToken ct);
        Task<GetQueryWithPaginationResultModel<CreditApplication>> GetAllByFiltersWithPagination(
            GetCreditApplicationQueryWithPagination query, CancellationToken ct);
        Task<IEnumerable<CreditApplication>> GetAll(CancellationToken ct);
        Task<CreditApplication> GetById(string id, CancellationToken ct);
        Task<IEnumerable<CreditApplication>> GetByIds(string[] ids, CancellationToken ct);
        Task<CreditApplication> Create(CreateCreditApplication createCreditApplication, CancellationToken ct, bool shouldIgnoreSync = false);
        Task<CreditApplication> Update(UpdateCreditApplication updateCreditApplication, CancellationToken ct);
        Task<CreditApplication> PatchAdmin(PatchCreditApplicationAdminModel patchModel, CancellationToken ct);
        Task<CreditApplication> Review(string creditApplicationId, string userId, ReviewCreditApplicationDto reviewCreditApplication, CancellationToken ct);
        Task<IEnumerable<CreditApplication>> GetByEinHashes(string[] einHashes, CancellationToken ct);
        Task<IReadOnlyList<LightCreditApplication>> GetLightCreditApps(string[] companyIds, string status, CancellationToken ct);
        Task<IEnumerable<CreditApplication>> GetByCompanyIds(GetCreditApplicationsByCompanyIdsQuery query, CancellationToken ct);
    }
}
