using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.CreditApplications)]
[ApiController]
public class CreditApplicationNotesController : ControllerBase
{
    private readonly ICreditApplicationNotesService _notesService;
    private readonly IMapper _mapper;

    public CreditApplicationNotesController(ICreditApplicationNotesService notesService, IMapper mapper)
    {
        _notesService = notesService;
        _mapper = mapper;
    }

    /// <summary>
    /// List of notes for a credit application with the given id. 
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /CreditApplication/6577001b0952df74adb0e0e4/notes
    ///     
    /// </remarks>
    /// <returns>List of notes for a credit application with the given id..</returns>
    [HttpGet($"{EndpointConstants.Id}/{EndpointConstants.Notes}")]
    public async Task<IEnumerable<CreditApplicationNoteDto>> GetNotes([FromRoute] string id, CancellationToken ct)
    {
        var notes = await _notesService.GetByApplicationId(id, ct);

        return _mapper.Map<IEnumerable<CreditApplicationNoteDto>>(notes);
    }

    /// <summary>
    /// Creates a new note for a credit application with the given id  
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     POST /CreditApplication/6577001b0952df74adb0e0e4/notes
    ///     X-User-Id: '123456789abc'
    ///     {
    ///         note: "lorem ipsum"
    ///     }
    /// 
    /// </remarks>
    /// <returns>List of notes for a credit application with the given id..</returns>
    [HttpPost($"{EndpointConstants.Id}/{EndpointConstants.Notes}")]
    public async Task<CreditApplicationNoteDto> AddNote(
        [FromRoute] string id,
        [FromBody] CreditApplicationNoteCreateDto noteDto,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var newNote = _mapper.Map<CreateCreditApplicationNote>(noteDto);

        newNote.CreditApplicationId = id;
        newNote.CreatedBy = userId;

        var result = await _notesService.Add(newNote, ct);
        return _mapper.Map<CreditApplicationNoteDto>(result);
    }

    [HttpPatch($"{EndpointConstants.Id}/{EndpointConstants.Notes}/{EndpointConstants.NoteId}")]
    public async Task<CreditApplicationNoteDto> Patch([FromRoute] string id,
        [FromRoute] string noteId,
        [FromBody] PatchCreditApplicationNoteDto noteDto,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var patchedNote = _mapper.Map<PatchCreditApplicationNote>(noteDto);
        patchedNote.CreditApplicationId = id;
        patchedNote.UpdatedBy = userId;
        patchedNote.Id = noteId;

        return _mapper.Map<CreditApplicationNoteDto>(await _notesService.Patch(patchedNote, ct));
    }

    [HttpDelete($"{EndpointConstants.Id}/{EndpointConstants.Notes}/{EndpointConstants.NoteId}")]
    public Task Delete([FromRoute] string id,
        [FromRoute] string noteId,
        [FromHeader(Name = HttpHeaderConstants.UserId), Required] string userId,
        CancellationToken ct)
    {
        var deleteModel = new DeleteCreditApplicationNote()
        {
            Id = noteId,
            CreditApplicationId = id,
            UserId = userId
        };
        return _notesService.Delete(deleteModel, ct);
    }
}
