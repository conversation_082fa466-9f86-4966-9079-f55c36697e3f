﻿using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;

[BsonIgnoreExtraElements]
public class Score
{
    [BsonElement("name")]
    public string? Name { get; set; }
    [BsonElement("score")]
    public string? Value { get; set; }
    [BsonElement("pass")]
    public ScoringResult Pass { get; set; }
}
