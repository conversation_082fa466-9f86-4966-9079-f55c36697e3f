﻿using BlueTape.CompanyClient.DI;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.OnBoardingService.DataAccess.CompanyService.DI
{
    public static class CompanyExternalServiceExtensions
    {
        public static void AddCompanyExternalServiceDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddScoped<ICompanyExternalService, CompanyExternalService>();
            services.AddCompanyServiceClient(config);
        }
    }
}