﻿using AutoMapper;
using BlueTape.OBS.DTOs.PaymentPlan;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.PaymentPlans)]
[ApiController]
public class PaymentPlansController(ILoanPaymentPlanService paymentPlanService, IMapper mapper)
{
    /// <summary>
    /// Get array of loan payment plans
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /paymentPlans
    ///     
    /// </remarks>
    /// <returns>Array of payment plans</returns>
    [HttpGet]
    public async Task<List<LoanPaymentPlanDto>> Get(CancellationToken ctx)
    {
        var result = await paymentPlanService.Get(ctx);

        return mapper.Map<List<LoanPaymentPlanDto>>(result);
    }
}