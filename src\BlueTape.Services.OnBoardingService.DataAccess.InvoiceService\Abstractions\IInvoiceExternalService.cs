﻿using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Models.Invoice;

namespace BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Abstractions;

public interface IInvoiceExternalService
{
    Task<InvoiceModel?> GetById(string id, CancellationToken ctx);
    Task<IEnumerable<InvoiceModel>?> GetByIds(string[] ids, CancellationToken ct);
    Task<IEnumerable<InvoiceModel>> UpdateInvoicesStatus(IEnumerable<string> ids, InvoiceStatus invoiceStatus, CancellationToken ct);
}