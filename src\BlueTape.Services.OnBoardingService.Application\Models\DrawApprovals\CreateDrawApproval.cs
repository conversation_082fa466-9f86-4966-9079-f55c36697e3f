﻿using BlueTape.OBS.Enums;
namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class CreateDrawApproval
{
    public string CompanyId { get; set; } = string.Empty;
    public string EinHash { get; set; } = string.Empty;
    public string? CreditId { get; set; } = string.Empty;
    public string? ArAdvanceCreditId { get; set; } = string.Empty;
    public string? InHouseCreditId { get; set; } = string.Empty;
    public string PaymentPlanId { get; set; } = string.Empty;
    public string? CreditApplicationId { get; set; }
    public string ApplicantName { get; set; } = string.Empty;
    public string? ProjectId { get; set; }
    public decimal? CreditHoldAmount { get; set; }
    public string? MerchantId { get; set; }
    public string? ExecutionId { get; set; }

    public DateTime? ExpirationDate { get; set; }
    public LoanOrigin LoanOrigin { get; set; }
    public IList<PayableItem> Payables { get; set; } = new List<PayableItem>();
    public NoSupplierDetailsModel? NoSupplierDetails { get; set; }
    public DownPaymentDetails? DownPaymentDetails { get; set; }
}
