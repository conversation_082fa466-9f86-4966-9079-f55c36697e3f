﻿using BlueTape.LS.DTOs.AuthorizationPeriods;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Constants;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using System.Text;

namespace BlueTape.Services.OnBoardingService.DataAccess.LMS.ExternalServices;

public class LoanExternalService : ILoanExternalService
{
    private readonly ILoanServiceHttpClient _serviceHttpClient;
    private readonly ILogger<LoanExternalService> _logger;

    public LoanExternalService(ILoanServiceHttpClient serviceHttpClient,
        ILogger<LoanExternalService> logger)
    {
        _serviceHttpClient = serviceHttpClient;
        _logger = logger;
    }

    public async Task<CreditDto?> CreateCredit(CreateCreditDto requestDto, CancellationToken ct)
    {
        var json = JsonConvert.SerializeObject(requestDto);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await _serviceHttpClient.Client.PostAsync(LoanServiceConstants.CreditPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("CreateCredit: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<CreditDto>(responseString);
        }

        throw new HttpClientRequestException($"CreateCredit failed at path: {LoanServiceConstants.CreditPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task<LoanDto> GetById(Guid id, bool detailed, CancellationToken ct)
    {
        var response = await _serviceHttpClient.Client.GetAsync($"{LoanServiceConstants.LoanPath}/{id}?detailed={detailed}", ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetLoanById: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<LoanDto>(responseString)!;
        }

        throw new HttpClientRequestException($"GetLoanById failed at path: {LoanServiceConstants.LoanPath}/{id}?detailed={detailed}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task<AuthorizationPeriodDto> CreateCreditHold(CreateAuthorizationPeriodDto dto, CancellationToken ct)
    {
        var json = JsonConvert.SerializeObject(dto);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await _serviceHttpClient.Client.PostAsync($"{LoanServiceConstants.CreditHolds}", data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("Create credit hold: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<AuthorizationPeriodDto>(responseString)!;
        }

        throw new HttpClientRequestException($"Create CreditHold for credit with id {dto.CreditId} failed at path: {LoanServiceConstants.CreditHolds}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task<CreditDto> GetCreditById(Guid id, bool? detailed, CancellationToken ct)
    {
        var response = await _serviceHttpClient.Client.GetAsync($"{LoanServiceConstants.CreditPath}/{id}?detailed={detailed}", ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetCreditById: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<CreditDto>(responseString)!;
        }

        throw new HttpClientRequestException($"GetCreditById failed at path: {LoanServiceConstants.CreditPath}/{id}?detailed={detailed}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task<IList<CreditDto?>> GetCreditsByFilters(CreditFilterDto dto, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(dto);
        var url = QueryHelpers.AddQueryString(
            LoanServiceConstants.CreditPath,
            new Dictionary<string, StringValues>
            {
                { nameof(dto.CompanyId), dto.CompanyId },
                { nameof(dto.ProjectId), dto.ProjectId },
                { nameof(dto.MerchantId), dto.MerchantId },
                { nameof(dto.Detailed), dto.Detailed.ToString() },
                { nameof(dto.CreditApplicationId),  new StringValues(dto.CreditApplicationId?.ToArray()) },
                { nameof(dto.Status), dto.Status.ToString() },
                { nameof(dto.Product), dto.Product.ToString() },
            });

        var response = await _serviceHttpClient.Client.GetAsync(url, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("Get credits by filters: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<IList<CreditDto?>>(responseString)!;
        }

        throw new HttpClientRequestException($"Get credits by filters failed at path: {LoanServiceConstants.CreditPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task<List<LoanDto>> FindLoans(LoanQueryDto filter, CancellationToken ct)
    {
        var url = QueryHelpers.AddQueryString(
            LoanServiceConstants.LoanPath,
new Dictionary<string, string?>
        {
            { nameof(filter.FromDate), filter.FromDate?.ToString("yyyy-MM-dd") },
            { nameof(filter.ToDate), filter.ToDate?.ToString("yyyy-MM-dd") },
            { nameof(filter.EinHash), filter.EinHash },
            { nameof(filter.PayableId), filter.PayableId },
            { nameof(filter.ProjectId), filter.ProjectId },
            { nameof(filter.Product), filter.Product?.ToString() },
            { nameof(filter.LoanStatus), filter.LoanStatus?.ToString() },
            { nameof(filter.ShowLateOnly), filter.ShowLateOnly?.ToString() },
            { nameof(filter.DrawApprovalId), filter.DrawApprovalId?.ToString() },
            { nameof(filter.Detailed), filter.Detailed?.ToString() }
        });

        var response = await _serviceHttpClient.Client.GetAsync(url, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseBody = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("FindLoans: string response: {response}", responseBody);
            return JsonConvert.DeserializeObject<List<LoanDto>>(responseBody)!;
        }

        throw new HttpClientRequestException($"FindLoans failed at path: {url}" +
                                             $"Status code: {response.StatusCode}");
    }
}
