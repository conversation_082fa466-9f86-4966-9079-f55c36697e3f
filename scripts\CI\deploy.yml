.deploying:base:
  image: node:latest
  stage: deploy
  variables:
    LP_AWS_ACCOUNT: $LP_AWS_ACCOUNT
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEV
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEV
    API_LAMBDA_PACKAGE_LOCATION: ../../publish/api-$CI_PIPELINE_ID.zip
    AWS_REGION: us-west-1
    BRANCH: $CI_COMMIT_REF_NAME
  before_script:
    - cd scripts/deploy
  after_script:
    - cd ../../../../
  script:
    - yarn global add serverless --prefix /usr/local
    - serverless plugin install -n serverless-dotenv-plugin
    - serverless plugin install -n serverless-offline
    - serverless plugin install -n serverless-plugin-warmup
    - serverless plugin install -n serverless-prune-plugin
    - serverless plugin install -n serverless-domain-manager
    - serverless plugin install -n serverless-plugin-lambda-insights
    - serverless deploy --stage $STAGE --verbose
  when: manual

deploying:dev:
  extends: .deploying:base
  variables:
    STAGE: dev
    ASPNETCORE_ENVIRONMENT: dev
    LOGZIO_TOKEN: $LOGZIO_TOKEN_DEV
  needs: ["publishing:api"]
  only:
    !reference [ "publishing:api", only ]
  environment:
    name: dev
    deployment_tier: staging

deploying:beta:
  extends: .deploying:base
  variables:
    STAGE: beta
    ASPNETCORE_ENVIRONMENT: beta
    LOGZIO_TOKEN: $LOGZIO_TOKEN_DEV #needs to be replaces to beta when prepared
  needs: ["publishing:api"]
  only:
    !reference [ "publishing:api", only ]
  environment:
    name: beta
    deployment_tier: staging

deploying:qa:
  extends: .deploying:base
  variables:
    STAGE: qa
    ASPNETCORE_ENVIRONMENT: qa
    LOGZIO_TOKEN: $LOGZIO_TOKEN_DEV #needs to be replaces to qa when prepared
  needs: ["publishing:api"]
  only:
    !reference [ "publishing:api", only ]
  environment:
    name: qa
    deployment_tier: staging

deploying:prod:
  extends: .deploying:base
  variables:
    STAGE: prod
    ASPNETCORE_ENVIRONMENT: prod
    LOGZIO_TOKEN: $LOGZIO_TOKEN_PROD
  needs: ["publishing:prod"]
  only:
    !reference [ "publishing:prod", only ]
  environment:
    name: prod
    deployment_tier: production