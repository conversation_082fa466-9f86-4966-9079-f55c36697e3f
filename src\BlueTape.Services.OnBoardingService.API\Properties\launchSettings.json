﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:15379",
      "sslPort": 44322
    }
  },
  "profiles": {
    "DEV BlueTape.OBS.API": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://localhost:5066",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "dev",
        "AWS_REGION": "us-west-1",
        "LP_AWS_ACCOUNT": "************",
        "Branch": "Local",
        "KEYVAULT_URI": "https://keyvault-dev-17b195c92e.vault.azure.net/"
      },
      "dotnetRunMessages": true
    },
    "BETA BlueTape.OBS.API": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://localhost:5066",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "beta",
        "AWS_REGION": "us-west-1",
        "AWS_DEFAULT_REGION": "us-west-1",
        "LP_AWS_ACCOUNT": "************",
        "LOGZIO_TOKEN": "NCCCLcpNdOLBSMRoISTxozDzHoDwJjoR",
        "Branch": "Local",
        "KEYVAULT_URI": "https://keyvault-beta-10ea6a9eb6.vault.azure.net/"
      },
      "dotnetRunMessages": true
    },
    "QA BlueTape.OBS.API": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://localhost:5066",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "qa",
        "AWS_REGION": "us-west-1",
        "LP_AWS_ACCOUNT": "************",
        "LOGZIO_TOKEN": "NCCCLcpNdOLBSMRoISTxozDzHoDwJjoR",
        "Branch": "Local",
        "KEYVAULT_URI": "https://keyvault-qa-b2c7f314826.vault.azure.net/"
      },
      "dotnetRunMessages": true
    },
    "PROD BlueTape.OBS.API": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "http://localhost:5066",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "prod",
        "AWS_REGION": "us-west-1",
        "LP_AWS_ACCOUNT": "************",
        "LOGZIO_TOKEN": "PJiLirYZjrLhGExyBanGEwrtdaZBvUIx",
        "Branch": "Local",
        "KEYVAULT_URI": "https://keyvault-prod-c3b7f21532.vault.azure.net/"
      },
      "dotnetRunMessages": true
    }
  }
}
