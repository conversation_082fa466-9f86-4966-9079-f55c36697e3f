﻿using BlueTape.Services.OnBoardingService.DataAccess.LMS.Constants;

namespace BlueTape.Services.OnBoardingService.DataAccess.LMS.Extensions
{
    public static class EnvironmentExtensions
    {
        public static bool IsDevelopment()
        {
            var currentEnv = Environment.GetEnvironmentVariable(ApplicationConstants.AspNetCoreEnvironmentVariable);
            return currentEnv is null or ApplicationConstants.DevelopmentEnvironment;
        }
    }
}
