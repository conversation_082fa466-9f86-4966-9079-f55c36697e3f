﻿using BlueTape.OBS.DTOs.Linqpal;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;

public interface INodeExternalService
{
    Task NotifyOpsTeam(OpsTeamNotificationRequest request, CancellationToken ct);
    Task TriggerLoanCancellationCustomerNotification(LoanCancellationCustomerNotificationRequest request, CancellationToken ct);
    Task TriggerSentBackUserNotification(SendBackUserNotificationRequest request, CancellationToken ct);
    Task TriggerUserApprovalNotification(UserApprovalNotificationRequest request, CancellationToken ct);
    Task RejectLoan(UserRejectedNotificationRequest request, CancellationToken ct);
    Task HumanApproval(IssueLoanRequest request, CancellationToken ct);
    Task IssueFactoringLoan(IssueLoanRequest request, CancellationToken ct);
    Task IssueLoanPrequalified(IssueLoanPrequalifiedRequest request, CancellationToken ct);
    Task PostTransaction(PostTransactionRequest request, CancellationToken ct);
    Task UpdateAuthorization(UpdateAuthorizationRequest request, CancellationToken ct);
    Task TriggerCancelLoanApplication(CancelLoanApplicationRequest request, CancellationToken ct);
    Task TriggerIhcDrawApprovalCustomerNotification(DrawApprovalReviewUserNotification request, CancellationToken ct);
    Task<IEnumerable<InvoiceStatusResponse>?> GetInvoiceStatuses(InvoiceStatusesRequest request, CancellationToken ct);
}
