﻿using BlueTape.Services.OnBoardingService.Application.Models.Cipher;

namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class BusinessDetailsPatch
{
    public DateOnly? LastEINRejectionDate { get; set; }

    public DateOnly? LoansLastDefaultedDate { get; set; }

    public DateOnly? BusinessStartDate { get; set; }

    public IEnumerable<string?>? BRICodes { get; set; }

    public string? BVI { get; set; }

    public string? ReliabilityCode { get; set; }

    public DateOnly? FirstReportedTradeLineDate { get; set; }

    public bool? BankruptcyIndicator { get; set; }

    public DateOnly? LastBankruptcyDate { get; set; }

    public bool? JudgmentIndicator { get; set; }

    public DateOnly? LastJudgmentDate { get; set; }

    public decimal? JudgmentBalance { get; set; }

    public bool? LienIndicator { get; set; }

    public DateOnly? LastLienDate { get; set; }

    public decimal? LienBalance { get; set; }

    public decimal? DBT60PlusPercentage { get; set; }

    public decimal? LoanRevenue { get; set; }

    public decimal? LienRevenue { get; set; }

    public decimal? JudgmentRevenue { get; set; }

    public decimal? PastDueAmount { get; set; }

    public decimal? CompanyIncome { get; set; }

    public decimal? AnnualRevenue { get; set; }

    public decimal? InquiriesDuringLast6Months { get; set; }

    public decimal? TradeLinesPercentage { get; set; }

    public decimal? TotalTradeLines { get; set; }

    public decimal? CurrentDebt { get; set; }

    public decimal? LoanDebt { get; set; }

    public decimal? AvailableCreditLimit { get; set; }

    public decimal? DebtAdjustor { get; set; }

    public decimal? AcceptablePercentRevenue { get; set; }

    public decimal? RevenueByCustomer { get; set; }

    public decimal? DebtByCustomer { get; set; }

    public decimal? RevenueVariancePercentage { get; set; }

    public decimal? DTI2Value { get; set; }

    public decimal? BusinessOutstandingBalance { get; set; }

    public decimal? TotalAcceptableDebtAmount { get; set; }

    public decimal? DBT60PlusAmount { get; set; }

    public decimal? CreditUtilizationRatio { get; set; }

    public decimal? DBT60PlusAndRevenueRatio { get; set; }

    public decimal? JudgmentAndRevenueRatio { get; set; }

    public decimal? LienAndRevenueRatio { get; set; }

    public bool? IsEntityCoOwnersExist { get; set; }

    public CipherModel? Bin { get; set; }

}
