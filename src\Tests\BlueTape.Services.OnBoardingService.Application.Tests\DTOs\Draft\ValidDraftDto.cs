﻿using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;

namespace BlueTape.Services.OnBoardingService.Application.Tests.DTOs.Draft
{
    public static class ValidDraftDto
    {
        public static readonly List<DraftDocument> ListOfDraftsForIdIsNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "8538ed7e-9a1c-4c8a-8162-b672a415e6fa",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "e3061ad1-40b2-463c-8473-39dda8c37b79",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "a8fd952f-6943-4714-bfa9-4491a7bd0740",
            },
            new DraftDocument()
            {
                Id = "e9c8c354-ddd2-4c78-81ce-036cc0d05d1e",
                CompanyId = "86fc08f4-13ca-4bae-a3b2-f8c04af75b1a",
                CreditApplicationId = "fb09a7bd-9da4-404c-bfca-36cd84e3b481",
            },
        };

        public static readonly List<DraftDocument> ListOfExpectedDraftsForIdIsNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "8538ed7e-9a1c-4c8a-8162-b672a415e6fa",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
        };

        public static readonly List<DraftDocument> ListOfDraftsForCompanyIdIsNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb",
                CompanyId = "7cd65876-a478-4568-aaa0-68e628bedbc7",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "e3061ad1-40b2-463c-8473-39dda8c37b79",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "e9c8c354-ddd2-4c78-81ce-036cc0d05d1e",
                CompanyId = "86fc08f4-13ca-4bae-a3b2-f8c04af75b1a",
                CreditApplicationId = "fb09a7bd-9da4-404c-bfca-36cd84e3b481",
            },
        };

        public static readonly List<DraftDocument> ListOfExpectedDraftsForCompanyIdIsNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "9f17d414-1a94-4a5b-b9ef-8b0ccda7d1cb",
                CompanyId = "7cd65876-a478-4568-aaa0-68e628bedbc7",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
        };

        public static readonly List<DraftDocument> ListOfDraftsForCreditApplicationIdIsNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "e3061ad1-40b2-463c-8473-39dda8c37b79",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "e9c8c354-ddd2-4c78-81ce-036cc0d05d1e",
                CompanyId = "86fc08f4-13ca-4bae-a3b2-f8c04af75b1a",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
        };

        public static readonly List<DraftDocument> ListOfExpectedDraftsForCreditApplicationIdIsNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
        };

        public static readonly List<DraftDocument> ListOfDraftsForIdAndCompanyIdAreNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "8f2d5914-6067-44b0-a4f4-1d57a98b1580",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
            new DraftDocument()
            {
                Id = "85b7097f-0221-415c-ac55-03d1d9a9d56c",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
            new DraftDocument()
            {
                Id = "e3061ad1-40b2-463c-8473-39dda8c37b79",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
            new DraftDocument()
            {
                Id = "e9c8c354-ddd2-4c78-81ce-036cc0d05d1e",
                CompanyId = "86fc08f4-13ca-4bae-a3b2-f8c04af75b1a",
                CreditApplicationId = "f407c05e-3d05-4900-8ee8-d254c1574e21",
            },
        };

        public static readonly List<DraftDocument> ListOfExpectedDraftsForIdAndCompanyIdAreNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "8f2d5914-6067-44b0-a4f4-1d57a98b1580",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
            new DraftDocument()
            {
                Id = "85b7097f-0221-415c-ac55-03d1d9a9d56c",
                CompanyId = "73217f2a-a289-4f6d-ae63-70e70b825db0",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
        };

        public static readonly List<DraftDocument> ListOfDraftsForCompanyIdAndCreditApplicationIdAreNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "8f2d5914-6067-44b0-a4f4-1d57a98b1580",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
            new DraftDocument()
            {
                Id = "85b7097f-0221-415c-ac55-03d1d9a9d56c",
                CompanyId = "8f2d5914-6067-44b0-a4f4-1d57a98b1580",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
            new DraftDocument()
            {
                Id = "e3061ad1-40b2-463c-8473-39dda8c37b79",
                CompanyId = "5e9f8d56-5c15-4fc4-9d58-4f0021c7004c",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
            new DraftDocument()
            {
                Id = "e9c8c354-ddd2-4c78-81ce-036cc0d05d1e",
                CompanyId = "8f2d5914-6067-44b0-a4f4-1d57a98b1580",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
        };

        public static readonly List<DraftDocument> ListOfExpectedDraftsForCompanyIdAndCreditApplicationIdAreNullScenario = new List<DraftDocument>()
        {
            new DraftDocument()
            {
                Id = "217738ac-a5dc-41cd-9a0a-d548898d3359",
                CompanyId = "8f2d5914-6067-44b0-a4f4-1d57a98b1580",
                CreditApplicationId = "08e3b5e4-18b4-4c54-b6ed-6a192bdd0913",
            },
        };

        public static DraftDocument DraftToExecuteDecisionEngine = new DraftDocument()
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "companyId",
            SubmitDate = new DateTime(2024, 05, 05),
            Data = new DataDocument()
            {
                BusinessInfo = new()
                {
                    Items = new List<ItemDocument>()
                    {
                        new()
                        {
                            Identifier = "ein",
                            Content = new
                            {
                                hash = "hash"
                            }
                        }
                    }
                }
            }
        };
    }
}
