﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.PricingPackages;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class LoanPricingPackageRepository : GenericRepository<LoanPricingPackageDocument>, ILoanPricingPackageRepository
{
    public LoanPricingPackageRepository(IObsMongoDBContext context, ILogger<GenericRepository<LoanPricingPackageDocument>> logger) : base(context, logger)
    {
    }

    public async Task<IEnumerable<LoanPricingPackageDocument>> GetByTitles(IEnumerable<string> titles,
        CancellationToken ctx)
    {
        var expression = Builders<LoanPricingPackageDocument>.Filter;
        var filter = Builders<LoanPricingPackageDocument>.Filter.Empty;
        filter &= expression.In(x => x.Name, titles);

        var documents = Collection.Find(filter);
        return await documents.ToListAsync(ctx);
    }
}
