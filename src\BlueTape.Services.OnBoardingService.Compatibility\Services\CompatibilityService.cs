﻿using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Messages;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.Cipher;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Constants;
using BlueTape.Services.OnBoardingService.Compatibility.Models.ConnectorNotifications;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using BlueTape.Services.OnBoardingService.Domain.Documents.Company;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.LoanApplication;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Polly;
using System.Dynamic;
using Exception = System.Exception;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services;

public class CompatibilityService(
    IOutputsManagerService outputsManagerService,
    ILogger<CompatibilityService> logger,
    ILoanApplicationRepository loanApplicationRepository,
    ICreditApplicationService creditApplicationService,
    IDraftRepository draftRepository,
    IDrawApprovalRepository drawApprovalRepository,
    ICreditApplicationNotesRepository creditApplicationNotesRepository,
    ILinqPalInteractionService integrationService,
    IAccountAuthorizationsRepository accountAuthorizationsRepository,
    IInvoiceRepository invoiceRepository,
    ICustomerAccountRepository customerAccountRepository,
    IDateProvider dateProvider,
    IKeyVaultService keyVaultService,
    ICompanyRepository companyRepository,
    ILoanExternalService loanExternalService,
    IConnectorNotificationService connectorNotificationService,
    ICardPricingPackageRepository cardPricingPackageRepository,
    ILoanPricingPackageRepository loanPricingPackageRepository,
    IInvoiceSyncMessageSender invoiceSyncMessageSender) : ICompatibilityService
{
    public async Task SyncCreditApplication(string creditApplicationId, CancellationToken cancellationToken)
    {
        var creditApplication = await creditApplicationService.GetById(creditApplicationId, cancellationToken);

        if (creditApplication == null) throw new InvalidOperationException($"Credit application with ID {creditApplicationId} not found.");

        if (IsCreditApplicationCreatedByLms(creditApplication))
            return;

        var isExecutionPrevented = await IsExecutionPrevented(creditApplication.CompanyId);

        if (isExecutionPrevented)
            throw new InvalidOperationException("Compatibility execution is prevented by configuration settings." +
                        "Set the dedicated key vault storage value to false to run it again.");

        var loanApplications = await loanApplicationRepository.GetByCreditApplicationInfo(creditApplication.Id, creditApplication.EinHash!, cancellationToken);

        if (!Enum.TryParse(typeof(CreditApplicationStatus), creditApplication.Status, true, out var status))
            throw new InvalidOperationException($"Error parsing credit application status: {creditApplication.Status}.");

        if (!creditApplication.CreatedAt.HasValue)
            throw new InvalidOperationException("Credit application does not have a creation date.");

        // Get paid credit app does not have loan application
        if (creditApplication.Type != CreditApplicationType.GetPaid && loanApplications != null && loanApplications.Count != 0)
        {
            // Use latest LA there for sendback in reason that on the client latest LA used for status representation
            // Also we expect that before approval LoC no one DA can't be approved, so it can work
            // Added filter for DrawApprovalId to avoid synchronization for draw loan applications in case if credit application was sent back after some draw approval has been created
            // Also, we can check invoice details as well if the current filtration is not enough
            LoanApplicationDocument? prequalifiedLoanApplication = null;

            if (prequalifiedLoanApplication is null && loanApplications.Any(x =>
                    x.Status.ToString().Equals(LoanApplicationStatus.Closed.ToString(), StringComparison.OrdinalIgnoreCase) ||
                    x.Status.ToString().Equals(LoanApplicationStatus.Approved.ToString(), StringComparison.OrdinalIgnoreCase)))
                prequalifiedLoanApplication = loanApplications
                    .Where(x =>
                        !x.Status.ToString().Equals(LoanApplicationStatus.Rejected.ToString(), StringComparison.OrdinalIgnoreCase)
                        && x.CompanyId == creditApplication.CompanyId
                        && x.CreatedAt.HasValue)
                    .MinBy(x => x.CreatedAt);

            if (string.Equals(creditApplication.Status, CreditApplicationStatus.SentBack.ToString(),
                        StringComparison.CurrentCultureIgnoreCase))
                prequalifiedLoanApplication =
                    loanApplications.MinBy(x => x.CreatedAt);

            if (prequalifiedLoanApplication is null)
                prequalifiedLoanApplication = loanApplications
                .Where(x => string.IsNullOrEmpty(x.DrawApprovalId) && x.CreatedAt.HasValue).MaxBy(x => x.CreatedAt);

            if (prequalifiedLoanApplication is not null)
            {
                logger.LogInformation("Compatibility service starts synchronization, credit application id: {id}, status: {status}, loan application id: {loanAppId}.", creditApplicationId, status, prequalifiedLoanApplication.Id);

                var updateRequest = new UpdateLoanApplicationDocument()
                {
                    IsSentBack = CalculateIsSendBackStatus(creditApplication, [prequalifiedLoanApplication]),
                    CreditApplicationId = creditApplicationId,
                    UpdatedAtByCompatibility = dateProvider.CurrentDateTime,
                    SubmitDate = creditApplication.ApplicationDate,
                };

                switch (status)
                {
                    case CreditApplicationStatus.Canceled:
                        await LoanApplicationCancel(prequalifiedLoanApplication, updateRequest, cancellationToken);
                        break;
                    case CreditApplicationStatus.Rejected:
                        await LoanApplicationRejected(prequalifiedLoanApplication, updateRequest, creditApplication.StatusCode, cancellationToken);
                        break;
                    case CreditApplicationStatus.SentBack:
                        await LoanApplicationSentBack(prequalifiedLoanApplication, updateRequest, cancellationToken);
                        break;
                    case CreditApplicationStatus.Approved:
                        await LoanApplicationPrequalifiedApproved(prequalifiedLoanApplication, updateRequest, creditApplication, cancellationToken);
                        break;
                    case CreditApplicationStatus.Processing:
                        await LoanApplicationProcessing(prequalifiedLoanApplication, updateRequest, cancellationToken);
                        break;
                    case CreditApplicationStatus.Processed:
                        await LoanApplicationProcessed(prequalifiedLoanApplication, updateRequest, cancellationToken);
                        break;
                    case CreditApplicationStatus.New:
                        await integrationService.SendOpsTeamNotification(prequalifiedLoanApplication, cancellationToken);
                        await loanApplicationRepository.UpdateManyByIds([prequalifiedLoanApplication.Id], updateRequest, cancellationToken);
                        break;
                    default:
                        await loanApplicationRepository.UpdateManyByIds([prequalifiedLoanApplication.Id], updateRequest, cancellationToken);
                        break;
                }
            }
        }

        await UpdateCompanyData(creditApplication, (CreditApplicationStatus)status, cancellationToken);
    }

    public async Task SyncDrawApproval(string drawApprovalId, CancellationToken cancellationToken)
    {
        var drawApproval = await drawApprovalRepository.GetById(drawApprovalId, cancellationToken);

        if (drawApproval == null) throw new InvalidOperationException($"Draw approval with ID {drawApprovalId} not found.");

        var loans = await loanExternalService.FindLoans(new LoanQueryDto() { DrawApprovalId = drawApprovalId, Detailed = true },
            cancellationToken);
        var loan = loans.FirstOrDefault();

        if (drawApproval == null) throw new InvalidOperationException($"Draw approval with ID {drawApprovalId} not found.");

        var isExecutionPrevented = await IsExecutionPrevented(drawApproval.CompanyId);

        if (isExecutionPrevented)
            throw new InvalidOperationException("Compatibility execution is prevented by configuration settings." +
                        "Set the dedicated key vault storage value to false to run it again.");

        if (!Enum.TryParse(typeof(DrawApprovalStatus), drawApproval.Status, true, out var status))
            throw new InvalidOperationException($"Cannot parse draw approval status: {drawApproval.Status}.");

        if (drawApproval.IsFactoring())
        {
            await SendConnectorEvent(drawApproval, cancellationToken);

            if ((DrawApprovalStatus)status == DrawApprovalStatus.Approved)
                await SyncCustomerFees(drawApproval, cancellationToken);
            return;
        }

        var loanApplication = await loanApplicationRepository.GetByInvoicesIds(drawApproval.Payables.Select(x => x.Id), cancellationToken);
        if (loanApplication == null) throw new InvalidOperationException("No related loan application found for the provided invoice IDs.");

        var query = new GetCreditApplicationQuery { EinHash = drawApproval.EinHash };
        var creditApplication = (await creditApplicationService.GetAllByFilters(query, cancellationToken)).MaxBy(ca => ca.CreatedAt);

        if (creditApplication == null) throw new InvalidOperationException($"Credit application with EIN hash {drawApproval.EinHash} not found.");

        logger.LogInformation("Compatibility service starts synchronization, draw application id: {id}, status: {status}, loan application id: {loanAppId}.", drawApproval.Id, status, loanApplication.Id);

        var updateRequest = new UpdateLoanApplicationDocument()
        {
            DrawApprovalId = drawApproval!.Id,
            UpdatedAtByCompatibility = dateProvider.CurrentDateTime,
            SubmitDate = drawApproval.ApplicationDate,
            Notes = await creditApplicationNotesRepository.GetByApplicationId(creditApplication.Id, cancellationToken),
            CreditApplicationId = creditApplication.Id,
            DisableCancelNotification = IsDownPaymentAndNoSupplier(loan),
        };

        switch (status)
        {
            case DrawApprovalStatus.Approved:
                await LoanApplicationApproved(loanApplication, updateRequest, creditApplication, drawApproval, cancellationToken);
                break;
            case DrawApprovalStatus.Rejected:
                await LoanApplicationRejected(loanApplication, updateRequest, drawApproval.StatusCode, cancellationToken);
                break;
            case DrawApprovalStatus.Canceled:
                await LoanApplicationCancel(loanApplication, updateRequest, cancellationToken);
                break;
            case DrawApprovalStatus.Processing:
                await LoanApplicationProcessing(loanApplication, updateRequest, cancellationToken);
                break;
            case DrawApprovalStatus.Processed:
                await LoanApplicationProcessed(loanApplication, updateRequest, cancellationToken);
                break;
            case DrawApprovalStatus.New:
                await integrationService.SendOpsTeamNotification(loanApplication, cancellationToken);

                updateRequest.Status = LoanApplicationStatus.Processing.ToString().ToLower();
                await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, cancellationToken);
                break;
            default:
                await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, cancellationToken);
                break;
        }
    }

    private static bool IsDownPaymentAndNoSupplier(LoanDto? loan)
    {
        if (loan is null)
            return false;

        if (loan.DownPaymentStatus is DownPaymentStatus.NotRequired)
            return false;

        if (loan?.Payments is not null && loan.Payments.Any())
            return false;

        return true;
    }

    public async Task SyncLoanApplication(string loanApplicationId, CancellationToken cancellationToken)
    {
        var loanApplication = await loanApplicationRepository.GetById(loanApplicationId, cancellationToken);
        if (loanApplication is null)
            throw new InvalidOperationException($"Loan application is not exists with that id: {loanApplicationId}");

        var creditApplication = await creditApplicationService.GetById(loanApplication.CreditApplicationId!, cancellationToken);
        if (creditApplication is null)
            throw new InvalidOperationException($"Credit application is not exists with that id: {loanApplication.CreditApplicationId}");

        await SyncLoanApplication(loanApplication, creditApplication, cancellationToken);
    }

    private async Task SetCustomerTradeCreditStatus(LoanApplicationDocument loanApplication, CancellationToken cancellationToken)
    {
        if (loanApplication.InvoiceDetails == null || loanApplication.InvoiceDetails.InvoiceIds == null)
        {
            logger.LogInformation($"Invoice details or invoice IDs are missing for the loan application with ID: {loanApplication.Id}");
            return;
        }
        var invoice = await invoiceRepository.GetById(loanApplication.InvoiceDetails.InvoiceIds.First(), cancellationToken);

        await customerAccountRepository.UpdateCustomerStatus(invoice.CustomerAccountId!, CustomerStatus.TradeCredit,
            cancellationToken);
    }

    private string? GetCompanyPurchaseType(string? companyId, string? purchaseType, CancellationToken cancellationToken)
    {
        var allowedPurchaseTypes = new HashSet<string> { "inventory", "project", "projectOrInventory" };

        if (purchaseType.IsNullOrEmpty() || companyId.IsNullOrEmpty())
            return null;

        if (purchaseType == "both")
            purchaseType = "projectOrInventory";

        if (!allowedPurchaseTypes.Contains(purchaseType!))
        {
            logger.LogError("Compatibility service: Invalid purchase type: {purchaseType} for company: {id}", purchaseType, companyId);
            return null;
        }

        return purchaseType;
    }

    private async Task UpdateCompanyData(CreditApplication creditApplication, CreditApplicationStatus status, CancellationToken cancellationToken)
    {
        var companyId = creditApplication.CompanyId;
        logger.LogInformation("Compatibility service: Update data for company with id: {companyId}", companyId);

        var updateDocument = new UpdateCompanyDocument
        {
            PurchaseType = GetCompanyPurchaseType(creditApplication.CompanyId, creditApplication.PurchaseTypeOption, cancellationToken)
        };

        if (creditApplication.Type == CreditApplicationType.GetPaid)
        {
            var companyStatus = status switch
            {
                CreditApplicationStatus.Approved => CompanyStatusEnum.Approved.ToString().ToLower(),
                CreditApplicationStatus.Rejected => CompanyStatusEnum.Rejected.ToString().ToLower(),
                CreditApplicationStatus.New => CompanyStatusEnum.Applied.ToString().ToLower(),
                CreditApplicationStatus.SentBack => CompanyStatusEnum.New.ToString().ToLower(),
                _ => null
            };

            updateDocument.Status = companyStatus;

            if (status == CreditApplicationStatus.Approved && creditApplication.MerchantSettings is not null)
            {
                if (creditApplication.MerchantSettings.LoanPricingPackageId is not null)
                {
                    var loanPricingPackage = await loanPricingPackageRepository.GetById(creditApplication.MerchantSettings.LoanPricingPackageId, cancellationToken);
                    updateDocument.LoanPricingPackageId = loanPricingPackage.Name;
                }

                if (creditApplication.MerchantSettings.CardPricingPackageId is not null)
                {
                    var cardPricingPackage = await cardPricingPackageRepository.GetById(creditApplication.MerchantSettings.CardPricingPackageId, cancellationToken);
                    updateDocument.CardPricingPackageId = cardPricingPackage.Name;
                }

                updateDocument.AchDelayDisabled = !creditApplication.MerchantSettings.IsAchDelay;
            }
        }

        try
        {
            await companyRepository.UpdateManyByIds([companyId!], updateDocument, cancellationToken);
        }
        catch
        {
            logger.LogError("Compatibility service: Error during update company data: {id}", companyId);
        }
    }

    private async Task SyncLoanApplicationWithLegacyData(LoanApplicationDocument loanApplication, CancellationToken cancellationToken)
    {
        var approvedLegacyLoanApplication = await loanApplicationRepository.GetFirstApprovedLegacyLoanByCompany(loanApplication.CompanyId!, cancellationToken);

        if (approvedLegacyLoanApplication != null)
        {
            var updateRequest = new UpdateLoanApplicationDocument()
            {
                Outputs = approvedLegacyLoanApplication.Outputs,
            };

            await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, cancellationToken);
        }
    }

    private async Task SyncLoanApplication(LoanApplicationDocument? loanApplication, CreditApplication? creditApplication, CancellationToken cancellationToken)
    {
        if (loanApplication == null) throw new VariableNullException(nameof(loanApplication));
        if (creditApplication == null) throw new VariableNullException(nameof(creditApplication));
        if (loanApplication.CompanyId == null) throw new VariableNullException(nameof(loanApplication.CompanyId));

        var draft = await draftRepository.GetById(creditApplication.DraftId!, cancellationToken);
        var accountAuthorization =
            (await accountAuthorizationsRepository.GetAllByFilters(null, null, creditApplication.EinHash, null,
                cancellationToken)).MaxBy(x => x.UpdatedAt);

        var companyId = loanApplication.CompanyId;
        var loanApplicationId = loanApplication.Id;
        var firstLoanApplication = (await loanApplicationRepository.GetAllByFilters(
                new GetLoanApplicationQuery() { CompanyId = companyId },
                cancellationToken))
            .Where(x => x.Status is "approved" or "closed" && x.LmsId.IsNullOrEmpty())
            .MinBy(x => x.CreatedAt);

        await outputsManagerService.SyncValidation(loanApplicationId, cancellationToken);
        await outputsManagerService.SyncKyc(loanApplicationId, creditApplication.Id, loanApplication, firstLoanApplication, accountAuthorization, cancellationToken);
        await outputsManagerService.SyncKyb(loanApplicationId, creditApplication, loanApplication, firstLoanApplication, draft, accountAuthorization, cancellationToken);
        await outputsManagerService.SyncExperian(loanApplicationId, creditApplication.Id, loanApplication, firstLoanApplication, draft, accountAuthorization, cancellationToken);
        await outputsManagerService.SyncBluetape(loanApplication, companyId, cancellationToken);
        await outputsManagerService.SyncBankAccounts(loanApplicationId, companyId, cancellationToken);

        loanApplication = await loanApplicationRepository.GetById(loanApplication.Id, cancellationToken);
        await outputsManagerService.SyncKnockout(loanApplication, companyId, cancellationToken);
        await outputsManagerService.SyncProcessPlaidData(loanApplication, cancellationToken); //add logic for remove finicity data and manual
        await outputsManagerService.SyncProcessManualData(loanApplication, cancellationToken); // add logic for delete ProcessPlaidData and ProcessFinicityData

        loanApplication = await loanApplicationRepository.GetById(loanApplication.Id, cancellationToken);
        await outputsManagerService.SyncLoanDecision(loanApplication, cancellationToken);

        loanApplication = await loanApplicationRepository.GetById(loanApplication.Id, cancellationToken);
        await outputsManagerService.SyncKnockout(loanApplication, companyId, cancellationToken);
    }

    public async Task SyncLoanApplicationsByCompanyId(string companyId, CancellationToken cancellationToken)
    {
        var loanApplications = (await loanApplicationRepository.GetAllByFilters(
                new GetLoanApplicationQuery() { CompanyId = companyId },
                cancellationToken))
            .Where(x => !x.LmsId.IsNullOrEmpty() && !x.DrawApprovalId.IsNullOrEmpty() && x.Status is "approved" or "closed")
            .ToList();

        var creditApplication = await GetCreditApplication(companyId, cancellationToken);
        if (creditApplication == null) throw new InvalidOperationException($"Credit application for company ID {companyId} not found.");

        foreach (var loanApplication in loanApplications)
        {
            await SyncLoanApplication(loanApplication, creditApplication, cancellationToken);
        }
    }

    private async Task<CreditApplication?> GetCreditApplication(string companyId, CancellationToken cancellationToken)
    {
        var draft = (await draftRepository.GetAllByFilters(null, companyId, null, cancellationToken)).FirstOrDefault();

        if (draft?.Data == null)
        {
            throw new ArgumentNullException(nameof(draft), "Draft and draft data should not be null");
        }

        var businessInfoItems = draft.Data.BusinessInfo?.Items!.ToList();

        var businessInfoItem = businessInfoItems?.Find(x => x.Identifier == "ein")?.Content;

        JObject? businessEin = businessInfoItem as JObject;

        if (businessEin == null)
        {
            if (businessInfoItem is ExpandoObject expando)
            {
                var json = JsonConvert.SerializeObject(expando);
                businessEin = JObject.Parse(json);
            }
            else
            {
                throw new VariableNullException(nameof(businessEin));
            }
        }

        var ein = JsonConvert.DeserializeObject<CipherModel>(businessEin.ToString());

        if (ein == null)
        {
            logger.LogError("Ein should not be null");
            throw new ArgumentNullException(nameof(ein), "Ein should not be null");
        }
        var hash = ein.Hash;

        var query = new GetCreditApplicationQuery
        {
            EinHash = hash
        };

        var creditApplication = (await creditApplicationService.GetAllByFilters(query, cancellationToken)).MaxBy(ca => ca.CreatedAt);

        return creditApplication;
    }

    private bool? CalculateIsSendBackStatus(CreditApplication? creditApplication, IEnumerable<LoanApplicationDocument> loanApplications)
    {
        if (creditApplication != null)
        {
            var isSentBack = string.Equals(creditApplication.Status, CreditApplicationStatus.SentBack.ToString(),
                StringComparison.CurrentCultureIgnoreCase);

            if (isSentBack)
                return true;

            var isSentBackStatuses = loanApplications.Select(x => x.IsSentBack);

            if (isSentBackStatuses.Any(x => x.HasValue))
                return false;
        }

        return null;
    }

    private async Task LoanApplicationApproved(
        LoanApplicationDocument loanApplication,
        UpdateLoanApplicationDocument updateRequest,
        CreditApplication creditApplication,
        DrawApprovalDocument drawApproval,
        CancellationToken ct)
    {
        try
        {
            if (IsCreditApplicationCreatedByLms(creditApplication))
            {
                await SyncLoanApplicationWithLegacyData(loanApplication, ct);
            }
            else
            {
                await SyncLoanApplication(loanApplication, creditApplication, ct);
            }
        }
        catch (Exception)
        {
            logger.LogError("Compatibility service sync loan application outputs failed, loan application id: {id}", loanApplication.Id);
        }

        if (loanApplication.Status == LoanApplicationStatus.Approved.ToString().ToLower())
            return;

        updateRequest.ApprovedAmount = (double)drawApproval.DrawAmount;
        updateRequest.DecisionDate = drawApproval.ApprovedAt;
        updateRequest.ApprovedBy = drawApproval.ApprovedBy;

        await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, ct);

        if (drawApproval.Type.ToLower() != "nosupplier")    //No customers for no supplier draws
            await SetCustomerTradeCreditStatus(loanApplication, ct);

        await SendConnectorEvent(drawApproval, ct);
    }

    private async Task LoanApplicationPrequalifiedApproved(LoanApplicationDocument prequalifiedLoanApplication, UpdateLoanApplicationDocument updateRequest, CreditApplication creditApplication, CancellationToken ct)
    {
        try
        {
            await SyncLoanApplication(prequalifiedLoanApplication, creditApplication, ct);
        }
        catch (Exception)
        {
            logger.LogError("Compatibility service sync loan application outputs failed, loan application id: {id}", prequalifiedLoanApplication.Id);
        }

        if (prequalifiedLoanApplication.Status == LoanApplicationStatus.Approved.ToString().ToLower())
            return;

        await integrationService.StartIssueLoanPrequalifiedProcess(prequalifiedLoanApplication, creditApplication.ApprovedCreditLimit.GetValueOrDefault(), ct);

        await loanApplicationRepository.UpdateManyByIds([prequalifiedLoanApplication.Id], updateRequest, ct);
    }

    private async Task LoanApplicationRejected(LoanApplicationDocument loanApplication, UpdateLoanApplicationDocument updateRequest, string? reason, CancellationToken ct)
    {
        reason ??= string.Empty;

        await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, ct);
        await integrationService.StartRejectLoanProcess(loanApplication, null, reason, ct);
        if (loanApplication.InvoiceDetails?.InvoiceIds is not null)
        {
            foreach (var invoiceId in loanApplication.InvoiceDetails.InvoiceIds)
            {
                await connectorNotificationService.SendInvoiceMessageAsync(invoiceId, ConnectorOperationType.LoanApplicationRejected, ct);
            }
        }
    }

    private async Task LoanApplicationCancel(LoanApplicationDocument loanApplication, UpdateLoanApplicationDocument updateRequest, CancellationToken ct)
    {
        if (string.Equals(loanApplication.Status, LoanApplicationStatus.Canceled.ToString(), StringComparison.CurrentCultureIgnoreCase))
            return;

        await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, ct);

        try
        {
            await integrationService.StartCancelLoanApplicationProcess(loanApplication, null, ct,
                notify: updateRequest.DisableCancelNotification is null or false);

            if (loanApplication.InvoiceDetails?.InvoiceIds is not null)
            {
                foreach (var invoiceId in loanApplication.InvoiceDetails.InvoiceIds)
                {
                    await connectorNotificationService.SendInvoiceMessageAsync(invoiceId, ConnectorOperationType.LoanApplicationCanceled, ct);
                }
            }
        }
        catch (Exception ex)
        {
            //TODO there and for other notification need to add slack notifications
            logger.LogError("Compatibility service LoanApplicationCancel: SendLoanCancellationCustomerNotification failed with error: {ex}", ex.Message);
        }
    }

    private async Task LoanApplicationSentBack(LoanApplicationDocument loanApplication, UpdateLoanApplicationDocument updateRequest, CancellationToken ct)
    {
        if (string.Equals(loanApplication.Status, LoanApplicationStatus.New.ToString(), StringComparison.CurrentCultureIgnoreCase) && loanApplication.IsSentBack == true)
            return;

        updateRequest.IsSentBack = true;
        updateRequest.Status = LoanApplicationStatus.New.ToString().ToLower();

        await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, ct);

        try
        {
            await integrationService.SendSentBackUserNotification(loanApplication, ct);
        }
        catch (Exception ex)
        {
            //TODO there and for other notification need to add slack notifications
            logger.LogError("Compatibility service LoanApplicationSentBack: SendSentBackUserNotification failed with error: {ex}", ex.Message);
        }
    }

    private async Task LoanApplicationProcessing(LoanApplicationDocument loanApplication, UpdateLoanApplicationDocument updateRequest, CancellationToken ct)
    {
        if (string.Equals(loanApplication.Status, LoanApplicationStatus.Processing.ToString(), StringComparison.CurrentCultureIgnoreCase))
            return;

        if (string.Equals(loanApplication.Status, LoanApplicationStatus.Authorized.ToString(), StringComparison.CurrentCultureIgnoreCase))
        {
            logger.LogWarning("Attempt to override loan application status \"authorized\" to processing, loan application id: {loanApplicationId}", loanApplication.Id);
            return;
        }

        updateRequest.Status = LoanApplicationStatus.Processing.ToString().ToLower();

        await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, ct);
    }

    private async Task LoanApplicationProcessed(LoanApplicationDocument loanApplication, UpdateLoanApplicationDocument updateRequest, CancellationToken ct)
    {
        if (IsLoanApplicationQuote(loanApplication))
        {
            logger.LogWarning("Attempt to override quote loan application status to pending, loan application id: {loanApplicationId}", loanApplication.Id);
            return;
        }

        if (string.Equals(loanApplication.Status, LoanApplicationStatus.Pending.ToString(), StringComparison.CurrentCultureIgnoreCase))
            return;

        if (string.Equals(loanApplication.Status, LoanApplicationStatus.Authorized.ToString(), StringComparison.CurrentCultureIgnoreCase))
        {
            logger.LogWarning("Attempt to override loan application status \"authorized\" to pending, loan application id: {loanApplicationId}", loanApplication.Id);
            return;
        }

        updateRequest.Status = LoanApplicationStatus.Pending.ToString().ToLower();

        await loanApplicationRepository.UpdateManyByIds([loanApplication.Id], updateRequest, ct);
    }

    private async Task<bool> IsExecutionPrevented(string? companyId)
    {
        string skipCheckingValue = "all";
        bool isExecutionPrevented = true;
        var secretValue = await keyVaultService.GetSecret(CompatibilityConstants.CompatibilityCompaniesList);
        var companyIds = new List<string>(secretValue.Split(','));

        if (companyIds.Contains(skipCheckingValue))
            return false;

        if (secretValue is null || companyId is null)
            return isExecutionPrevented;

        isExecutionPrevented = !companyIds.Contains(companyId);

        return isExecutionPrevented;
    }

    public async Task SendConnectorEvent(string drawApprovalId, CancellationToken ct)
    {
        logger.LogInformation("Starting test of SendConnectorEvent for draw approval ID: {id}", drawApprovalId);

        var drawApproval = await drawApprovalRepository.GetById(drawApprovalId, ct);
        if (drawApproval == null)
        {
            logger.LogError("Draw approval with ID {id} not found for TestSendConnectorEvent", drawApprovalId);
            throw new InvalidOperationException($"Draw approval with ID {drawApprovalId} not found.");
        }

        logger.LogInformation("Found draw approval with status: {status}, payables count: {count}",
            drawApproval.Status, drawApproval.Payables?.Count() ?? 0);

        await SendConnectorEvent(drawApproval, ct);
        logger.LogInformation("TestSendConnectorEvent completed successfully for draw approval ID: {id}", drawApprovalId);
    }

    private async Task SendConnectorEvent(DrawApprovalDocument drawApproval, CancellationToken ct)
    {
        if (Enum.TryParse(typeof(DrawApprovalStatus), drawApproval.Status, true, out var drawApprovalStatusObj)
            && drawApprovalStatusObj is DrawApprovalStatus drawApprovalStatus)
        {

            var connectorStatus = drawApprovalStatus switch
            {
                DrawApprovalStatus.Approved => ConnectorOperationType.LoanApplicationApproved,
                DrawApprovalStatus.Rejected => ConnectorOperationType.LoanApplicationRejected,
                DrawApprovalStatus.Canceled => ConnectorOperationType.LoanApplicationCanceled,
                _ => ConnectorOperationType.None // Default status
            };

            if (connectorStatus != ConnectorOperationType.None)
            {
                if (drawApproval?.Payables is not null)
                {
                    foreach (var invoice in drawApproval.Payables)
                    {
                        await connectorNotificationService.SendInvoiceMessageAsync(invoice.Id, connectorStatus, ct);
                        logger.LogInformation("Connector event for invoice with id: {invoiceId} set with status: {connectorStatus}", invoice.Id, connectorStatus);
                    }
                }
            }
        }
    }

    private async Task SyncCustomerFees(DrawApprovalDocument drawApproval, CancellationToken ct)
    {
        if (drawApproval.IsFactoring())
        {
            var loans = await FindLoansWithPollyRetry(drawApproval.Id, ct);

            if (loans != null && loans.Any())
            {
                var loanFee = loans.FirstOrDefault()?.LoanReceivables?
                    .Where(x => x.Type is ReceivableType.LoanFee or ReceivableType.ExtensionFee)
                    .Sum(r => r.AdjustAmount + r.ExpectedAmount);

                var messages = drawApproval.Payables
                    .Select(payable =>
                        new ServiceBusMessageBt<SyncInvoiceMessagePayload>(
                            new SyncInvoiceMessagePayload
                            {
                                InvoiceId = payable.Id,
                                SyncType = InvoiceSyncType.SyncCustomerFees,
                                CustomerFees = (double?)loanFee,
                            }
                        )
                    )
                    .ToList();

                if (messages.Any())
                {
                    await invoiceSyncMessageSender.SendMessages(messages, ct);
                }
            }
        }
    }

    private async Task<List<LoanDto>> FindLoansWithPollyRetry(string drawApprovalId, CancellationToken ctx)
    {
        // Retry 4 times with increasing delays: 3s, 5s, 10s, 15s
        var retryPolicy = Policy
            .HandleResult<List<LoanDto>>(loans => loans == null || loans.Count == 0)
            .WaitAndRetryAsync(
                new[]
                {
                    TimeSpan.FromSeconds(3),
                    TimeSpan.FromSeconds(5),
                    TimeSpan.FromSeconds(10),
                    TimeSpan.FromSeconds(15)
                },
                (result, timeSpan, retryCount, context) =>
                {
                    logger.LogWarning("FindLoans attempt {RetryCount} failed, retrying in {Delay}s...", retryCount, timeSpan.TotalSeconds);
                });

        return await retryPolicy.ExecuteAsync(async () =>
            await loanExternalService.FindLoans(new LoanQueryDto() { DrawApprovalId = drawApprovalId }, ctx)
        );
    }

    private bool IsLoanApplicationQuote(LoanApplicationDocument loanApplication) =>
        string.Equals(loanApplication.Type, LoanApplicationConstant.QuoteType, StringComparison.OrdinalIgnoreCase);

    private bool IsCreditApplicationCreatedByLms(CreditApplication creditApplication) =>
    string.Equals(creditApplication.CreatedBy, "LMS", StringComparison.OrdinalIgnoreCase);
}

