﻿using BlueTape.MongoDB.DTO;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorizationDetailsChanges;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using BlueTape.Utilities.Providers;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class AccountAuthorizationDetailsChangesServiceTests
{
    private readonly IAccountAuthorizationDetailsChangesService _accountAuthorizationDetailsChangesService;

    private readonly Mock<IAccountAuthorizationsChangesRepository> _accountAuthorizationsChangesRepositoryMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();

    public AccountAuthorizationDetailsChangesServiceTests()
    {
        _accountAuthorizationDetailsChangesService =
            new AccountAuthorizationDetailsChangesService(_accountAuthorizationsChangesRepositoryMock.Object,
                _dateProviderMock.Object);
    }

    [Fact]
    public async Task
        CompareAccountAuthorizationsDetailsAndLogChangesAsync_BusinessDetailsFieldsDifferent_InsertsChangeLogEntries()
    {
        var creditAppId = Guid.NewGuid().ToString();
        ExecutionChangeType executionChangeType = ExecutionChangeType.CreditApplication;
        var initialObject = new AccountAuthorizationDocument()
        {
            BusinessDetails = new()
            {
                BVI = "152"
            }
        };

        var updatedObject = new AccountAuthorizationDocument()
        {
            BusinessDetails = new()
            {
                BVI = "190",
                DBT60PlusPercentage = 10
            }
        };
        _accountAuthorizationsChangesRepositoryMock.Setup(x =>
            x.AddRange(It.IsAny<IEnumerable<AccountAuthorizationDetailsChangesDocument>>(), default));
        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialObject, updatedObject, creditAppId, executionChangeType, default);

        _accountAuthorizationsChangesRepositoryMock.Verify(x =>
            x.AddRange(It.IsAny<IEnumerable<AccountAuthorizationDetailsChangesDocument>>(), default), Times.Once);
    }

    [Fact]
    public async Task
        CompareAccountAuthorizationsDetailsAndLogChangesAsync_NewOwnerHasBeenCreated_InsertsChangeLogEntries()
    {
        var creditAppId = Guid.NewGuid().ToString();
        ExecutionChangeType executionChangeType = ExecutionChangeType.CreditApplication;
        var initialObject = new AccountAuthorizationDocument
        {
            BusinessDetails = new()
            {
                BVI = "152",
                Bin = new ParsedBodyLoggingDocument
                {
                    Display = "Initial body content"
                }
            }
        };

        var updatedObject = new AccountAuthorizationDocument()
        {
            BusinessDetails = new()
            {
                BVI = "152",
                Bin = new ParsedBodyLoggingDocument
                {
                    Display = "Some body content"
                }
            },
            OwnersDetails =
            [
                new()
                {
                    Identifier = "Owner"
                }
            ],
            OwnersEntitiesDetails =
            [
                new()
                {
                    Identifier = "Owner"
                }
            ]
        };

        _accountAuthorizationsChangesRepositoryMock.Setup(x =>
            x.AddRange(It.IsAny<IEnumerable<AccountAuthorizationDetailsChangesDocument>>(), default));
        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialObject, updatedObject, creditAppId, executionChangeType, default);

        _accountAuthorizationsChangesRepositoryMock.Verify(x =>
            x.AddRange(It.Is<IEnumerable<AccountAuthorizationDetailsChangesDocument>>(
                documents => documents.Count() == 16
                             && documents.All(d => d.NewValue != null)
                             && documents.Any(y => y.OldValue == null && y.NewValue!.ToString() == "Owner")
                             && documents.Any(y =>
                                 y.Key == "businessDetails.Bin" && y.OldValue != null && y.NewValue != null
                                 && (y.NewValue as ParsedBodyLoggingDocument)!.Display == "Some body content")
            ), default), Times.Once);
    }

    [Fact]
    public async Task CompareAccountAuthorizationsDetailsAndLogChangesAsync_ObjectsDoNotDiffer_DoesNotInsertAnyEntries()
    {
        var creditAppId = Guid.NewGuid().ToString();
        ExecutionChangeType executionChangeType = ExecutionChangeType.CreditApplication;
        var initialObject = new AccountAuthorizationDocument()
        {
            BusinessDetails = new()
            {
                BVI = "152",
                Bin = new ParsedBodyLoggingDocument
                {
                    Display = "Some body content"
                }
            }
        };

        var updatedObject = new AccountAuthorizationDocument()
        {
            BusinessDetails = new()
            {
                BVI = "152",
                Bin = new ParsedBodyLoggingDocument
                {
                    Display = "Some body content"
                }
            }
        };
        _accountAuthorizationsChangesRepositoryMock.Setup(x =>
            x.AddRange(It.IsAny<IEnumerable<AccountAuthorizationDetailsChangesDocument>>(), default));
        await _accountAuthorizationDetailsChangesService.CompareAccountAuthorizationsDetailsAndLogChangesAsync(
            initialObject, updatedObject, creditAppId, executionChangeType, default);

        _accountAuthorizationsChangesRepositoryMock.Verify(x =>
            x.AddRange(It.IsAny<IEnumerable<AccountAuthorizationDetailsChangesDocument>>(), default), Times.Never);
    }
}