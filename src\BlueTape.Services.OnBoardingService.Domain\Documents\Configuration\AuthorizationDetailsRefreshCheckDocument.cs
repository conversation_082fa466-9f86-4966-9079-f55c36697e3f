﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Configuration
{
    [BsonIgnoreExtraElements]
    public class AuthorizationDetailsRefreshCheckDocument
    {
        [BsonElement("scheduledUpdate")]
        public string ScheduledUpdate { get; set; } = string.Empty;

        [BsonElement("frequencyInDays")]
        public int FrequencyInDays { get; set; }

        [BsonElement("creditApplicationTypes")]
        public string[] CreditApplicationTypes { get; set; } = [];

        [BsonElement("stepsIncluded")]
        public string[] StepsIncluded { get; set; } = [];
    }
}
