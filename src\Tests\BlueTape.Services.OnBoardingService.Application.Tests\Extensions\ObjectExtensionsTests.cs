﻿using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Extensions;

public class ObjectExtensionsTests
{
    [Fact]
    public void DeepCopyJson_ObjectIsNull_ReturnsNull()
    {
        var accountAuthDetailsObject = (AccountAuthorizationDocument?)null;
        var copy = ObjectExtensions.DeepCopyJson(accountAuthDetailsObject);

        copy.ShouldBeNull();
    }
}
