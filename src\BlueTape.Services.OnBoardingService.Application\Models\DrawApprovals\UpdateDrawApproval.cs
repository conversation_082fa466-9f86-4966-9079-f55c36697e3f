using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class UpdateDrawApproval
{
    public DateTime UpdatedAt { get; set; }

    public string UpdatedBy { get; set; } = string.Empty;

    public string CompanyId { get; set; } = string.Empty;

    public string EinHash { get; set; } = string.Empty;

    public string CreditId { get; set; } = string.Empty;

    public string PaymentPlanId { get; set; } = string.Empty;

    public decimal DrawAmount { get; set; }

    public decimal? CreditHoldAmount { get; set; }

    public DrawApprovalType Type { get; set; }

    public LoanOrigin LoanOrigin { get; set; }

    public string ProjectId { get; set; } = string.Empty;

    public DrawApprovalStatus Status { get; set; }

    public DebtInvestorType? DebtInvestor { get; set; }

    public DateTime? LastStatusChangedAt { get; set; }

    public string? LastStatusChangedBy { get; set; }

    public string? ExecutionId { get; set; }

    public DateTime? ApprovedAt { get; set; }

    public string? ApprovedBy { get; set; }

    public DateTime? RejectedAt { get; set; }

    public string? RejectedBy { get; set; }

    public UpdateDrawDetails? DrawDetails { get; set; }

    public DownPaymentDetails? DownPaymentDetails { get; set; }

    public List<string> InvoiceIds { get; set; } = new();
}