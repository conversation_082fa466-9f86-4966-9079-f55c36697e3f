﻿namespace BlueTape.Services.OnBoardingService.Domain.Documents.Company;

public class UpdateCompanyDocument
{
    public bool? ApproveRead { get; set; }
    public string? BankAccountStatus { get; set; }
    public string? Status { get; set; }
    public string? LegalName { get; set; }
    public string? Name { get; set; }

    public int? Limit { get; set; }

    public bool? AcceptAchPayment { get; set; }

    public string? PurchaseType { get; set; }

    public string? CardPricingPackageId { get; set; }
    public string? LoanPricingPackageId { get; set; }
    public bool? AchDelayDisabled { get; set; }
}
