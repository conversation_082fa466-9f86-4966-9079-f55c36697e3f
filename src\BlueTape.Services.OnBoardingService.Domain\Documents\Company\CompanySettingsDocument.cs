﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Company;

[BsonIgnoreExtraElements]
public class CompanySettingsDocument
{
    [BsonElement("cardPricingPackageId")]
    public string? CardPricingPackageId { get; set; }

    [BsonElement("loanPricingPackageId")]
    public string? LoanPricingPackageId { get; set; }

    [BsonElement("achDelayDisabled")]
    public bool? AchDelayDisabled { get; set; }

    [BsonElement("approveRead")]
    public bool? ApproveRead { get; set; }

    [BsonElement("acceptAchPayment")]
    public bool? AcceptAchPayment { get; set; }

    [BsonElement("onBoardingType")]
    public string[]? OnBoardingType { get; set; }
}
