﻿using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.API.Validators.DecisionEngineSteps.CreateDecisionEngineStepsBVIResults;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.API.Tests.Validators;

public class CreateDecisionEngineStepsBviResultsValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            DecisionEngineStepId = "id",
            IntegrationSource = "lexis",
            IntegrationLogId = "logId"
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_NotMandatoryFieldsNull_ReturnsTrue()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            DecisionEngineStepId = "id",
            IntegrationLogId = "logId"
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_NotMandatoryFieldsEmpty_ReturnsTrue()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            CreditApplicationId = "",
            ExecutionId = "",
            DecisionEngineStepId = "id",
            IntegrationSource = "",
            IntegrationLogId = "logId"
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_IntegrationLogIdNull_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            DecisionEngineStepId = "id",
            IntegrationSource = "lexis",
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_IntegrationLogIdEmpty_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            DecisionEngineStepId = "id",
            IntegrationSource = "lexis",
            IntegrationLogId = ""
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_DecisionEngineStepIdNull_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            IntegrationSource = "lexis",
            IntegrationLogId = "logId"
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_DecisionEngineStepIdEmpty_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsBviResultsValidator();

        var model = new CreateDecisionEngineStepsBVIResultsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            DecisionEngineStepId = "",
            IntegrationSource = "lexis",
            IntegrationLogId = "logId"
        };

        var result = validator.Validate(model);

        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_EmptyDecisionEngineSteps_ReturnsTrue()
    {
        var validator = new CreateDecisionEngineStepsBviResultsRangeValidator();
        var result = validator.Validate(new List<CreateDecisionEngineStepsBVIResultsDto>());

        result.IsValid.ShouldBeTrue();
    }
}
