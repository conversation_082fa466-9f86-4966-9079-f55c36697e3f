﻿using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface ICreditApplicationAuthorizationDetailsService
{
    Task<CreditApplicationAuthorizationDetails?> GetByCreditApplicationId(string creditApplicationId,
        CancellationToken ct);

    Task UpdateByCreditApplicationId(UpdateCreditApplicationAuthorizationDetails model, CancellationToken ct);

    Task<CreditApplicationAuthorizationDetailsDocument> Create(CreditApplicationAuthorizationDetails model, CancellationToken ct);

    public Task CreateOrUpdateAccountAuthDetailsSnapshot(UpsertAccountAuthDetailsSnapshotModel upsertAccountAuthDetailsSnapshot, CancellationToken ct);
}