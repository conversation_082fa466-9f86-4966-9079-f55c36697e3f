﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Models.IntegrationLogs;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IDecisionEngineStepsBviResultsService
{
    Task<IEnumerable<DecisionEngineStepsBviResultsModel>> AddRange(IEnumerable<CreateDecisionEngineStepsBviResultsModel> createModels, CancellationToken ct);
    Task<IEnumerable<DecisionEngineStepsBviResultsModel>> GetAllByFilters(string? id, string? executionId, string? integrationLogId, string? decisionEngineStepId, string? creditApplicationId, CancellationToken ct);
    Task<IEnumerable<RequestResponseBaseModel?>> GetBviResponsesByStepId(string decisionEngineStepId, CancellationToken ct);
    Task<RequestResponseBaseModel?> GetLexisNexisResponse(string creditApplicationId, LexisNexisSourceType type, string? reference, CancellationToken ct);
    Task<RequestResponseBaseModel?> GetExperianResponse(string creditApplicationId, ExperianSourceType type, CancellationToken ct);
    Task<RequestResponseBaseModel?> GetGiactResponse(string creditApplicationId, CancellationToken ct);
    Task<string> GetGiactRawData(string creditApplicationId, CancellationToken ct);
    Task<string> GetLexisNexisRawData(string creditApplicationId, LexisNexisSourceType? type, string? reference, CancellationToken ct);
    Task<string> GetExperianRawData(string creditApplicationId, ExperianSourceType? type, CancellationToken ct);
}