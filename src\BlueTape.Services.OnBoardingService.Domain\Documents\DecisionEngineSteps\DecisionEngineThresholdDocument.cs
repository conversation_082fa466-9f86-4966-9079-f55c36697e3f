﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;

public class DecisionEngineThresholdDocument
{
    [BsonElement("name")]
    public string Name { get; set; } = string.Empty;

    [BsonElement("pass")]
    public object? Pass { get; set; }

    [BsonElement("hardFail")]
    public object? HardFail { get; set; }

    [BsonElement("softFail")]
    public object? SoftFail { get; set; }
}