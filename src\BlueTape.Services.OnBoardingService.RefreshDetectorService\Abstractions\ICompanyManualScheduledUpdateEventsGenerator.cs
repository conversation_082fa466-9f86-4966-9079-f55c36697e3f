﻿using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;

public interface ICompanyManualScheduledUpdateEventsGenerator
{
    IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>> GenerateScheduledUpdateEvents(CompanyModel company,
        RefreshCheckConfiguration scheduledCheck,
        IReadOnlyList<LightCreditApplicationDocument> approvedCreditApplications,
        ScheduleMode scheduleMode, string userId);
}
