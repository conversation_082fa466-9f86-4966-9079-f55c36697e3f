﻿using BlueTape.MongoDB.Attributes;
using BlueTape.Services.OnBoardingService.Domain.Serializers;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
[MongoCollection("loanapplications")]
public class LoanApplicationDocument : Document
{
    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("invoiceDetails")]
    public InvoiceDetails? InvoiceDetails { get; set; }

    [BsonElement("outputs")]
    public List<OutputDocument>? Outputs { get; set; }

    [BsonElement("prevOutputs")]
    public List<OutputDocument>? PrevOutputs { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("type")]
    public string? Type { get; set; }

    [BsonElement("amountDue")]
    public double? AmountDue { get; set; }

    [BsonElement("draft")]
    public Dictionary<string, object>? Draft { get; set; }

    [BsonElement("progress")]
    public BsonDocument? Progress { get; set; }

    [BsonElement("executionArn")]
    public string? ExecutionArn { get; set; }

    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("drawApprovalId")]
    public string? DrawApprovalId { get; set; }

    [BsonElement("submitDate")]
    public DateTime? SubmitDate { get; set; }

    [BsonElement("decisionDate")]
    public DateTime? DecisionDate { get; set; }

    [BsonElement("approvedBy")]
    public string? ApprovedBy { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("approvedAmount")]
    public double? ApprovedAmount { get; set; }

    [BsonElement("isSentBack")]
    public bool? IsSentBack { get; set; }

    [BsonElement("lms_id")]
    public string? LmsId { get; set; }

    [BsonElement("notes")]
    public IEnumerable<object>? Notes { get; set; }

    [BsonElement("updatedAtByCompatibility")]
    public DateTime? UpdatedAtByCompatibility { get; set; }
}

public class EncryptedValue
{
    public string? Hash { get; set; }
}
