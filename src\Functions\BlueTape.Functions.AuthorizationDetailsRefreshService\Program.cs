using BlueTape.Functions.AuthorizationDetailsRefreshService;
using BlueTape.Functions.Hosting.Extensions;
using Microsoft.Extensions.Hosting;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureBlueTapeFunctionAppHostConfiguration()
    .ConfigureBlueTapeSerilog(nameof(AuthorizationDetailsRefreshService))
    .ConfigureBlueTapeServices()
    .Build();

await host.RunAsync();