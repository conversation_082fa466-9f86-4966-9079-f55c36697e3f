﻿using AutoMapper;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.Application.Tests.DTOs.Draft;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CreditApplicationExecutionServiceTests
{
    private readonly CreditApplicationExecutionService _creditApplicationExecutionService;

    private readonly Mock<ICreditApplicationService> _creditApplicationServiceMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();
    private readonly Mock<IDecisionEngineExecutionService> _decisionEngineExecutionService = new();
    private readonly Mock<IDraftRepository> _draftRepository = new();

    public CreditApplicationExecutionServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        var mapper = new Mapper(mapperConfig);

        _creditApplicationExecutionService = new CreditApplicationExecutionService(
            _creditApplicationServiceMock.Object,
            _dateProviderMock.Object,
            mapper,
            _decisionEngineExecutionService.Object,
            _draftRepository.Object);
    }

    [Theory, CustomAutoData]
    public Task SubmitCreditApplication_EinIsEmpty_ThrowsValidationError(SubmitCreditApplication submitCreditApplication, CreditApplication creditApplication, DraftDocument draft)
    {
        creditApplication.Type = CreditApplicationType.LineOfCredit;
        _creditApplicationServiceMock.Setup(app => app.Create(It.IsAny<CreateCreditApplication>(), default, false)).ReturnsAsync(creditApplication);
        _draftRepository.Setup(x => x.GetById(submitCreditApplication.DraftId, default)).ReturnsAsync(draft);
        var act = () => _creditApplicationExecutionService.SubmitCreditApplication(submitCreditApplication, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public Task RunDecisionEngineForCreditApplication_EinNull_ThrowsValidationException()
    {
        var draft = new DraftDocument() { };
        var request = new CreditApplicationDecisionEngineExecutionRequest()
        {
            DraftId = "draftId",
            MerchantId = "merchantId",
            Type = CreditApplicationType.InHouseCredit
        };

        _decisionEngineExecutionService.Setup(x => x.PreValidateCreditApplicationDecisionEngineExecution(It.IsAny<CreditApplicationType>(),
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default));
        _draftRepository.Setup(x => x.GetById(request.DraftId, default)).ReturnsAsync(draft);
        _decisionEngineExecutionService.Setup(x => x.StartCreditApplicationInitializationStep(It.IsAny<CreditApplicationInitializationStepStartRequest>(), default));

        var act = async () => await _creditApplicationExecutionService.RunDecisionEngineForCreditApplication(request, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task RunDecisionEngineForCreditApplication_ValidData_RunsDecisionEngine()
    {
        var companyId = Guid.NewGuid().ToString();
        var draft = ValidDraftDto.DraftToExecuteDecisionEngine;

        var request = new CreditApplicationDecisionEngineExecutionRequest()
        {
            DraftId = "draftId",
            MerchantId = "merchantId",
            Type = CreditApplicationType.InHouseCredit
        };

        _decisionEngineExecutionService.Setup(x => x.PreValidateCreditApplicationDecisionEngineExecution(request.Type, request.MerchantId, companyId, "hash", default));
        _draftRepository.Setup(x => x.GetById(request.DraftId, default)).ReturnsAsync(draft);
        _decisionEngineExecutionService.Setup(x => x.StartCreditApplicationInitializationStep(It.Is<CreditApplicationInitializationStepStartRequest>(x => x.DraftId == request.DraftId
            && x.Type == "ihc" && x.MerchantId == request.MerchantId), default));

        await _creditApplicationExecutionService.RunDecisionEngineForCreditApplication(request, default);

        _decisionEngineExecutionService.Verify(x => x.PreValidateCreditApplicationDecisionEngineExecution(It.IsAny<CreditApplicationType>(),
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default), Times.Once);
        _decisionEngineExecutionService.Verify(x => x.StartCreditApplicationInitializationStep(It.IsAny<CreditApplicationInitializationStepStartRequest>(), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task SubmitCreditApplication_ValidData_CreatesCreditApplication(SubmitCreditApplication submitCreditApplication, CreditApplication creditApplication)
    {
        creditApplication.Type = CreditApplicationType.LineOfCredit;
        var draft = ValidDraftDto.DraftToExecuteDecisionEngine;
        var currentDateTime = DateTime.UtcNow;

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDateTime);
        _creditApplicationServiceMock.Setup(x => x.Create(It.Is<CreateCreditApplication>(x => x.EinHash == "hash"
                && x.ApplicationDate == currentDateTime && x.CompanyId == draft.CompanyId), default, true)).ReturnsAsync(creditApplication);
        _draftRepository.Setup(x => x.GetById(submitCreditApplication.DraftId, default)).ReturnsAsync(draft);
        var result = await _creditApplicationExecutionService.SubmitCreditApplication(submitCreditApplication, default);

        _decisionEngineExecutionService.Verify(x => x.StartCreditApplicationInitializationStep(
            It.Is<CreditApplicationInitializationStepStartRequest>(r => r.MerchantId == result.MerchantId
                                                                        && r.Type == "loc"
                                                                        && r.CreditApplicationId == creditApplication.Id), default));
    }

    [Theory, CustomAutoData]
    public async Task SubmitCreditApplication_SentBackCreditApplicationExists_TriggersDecisionEngineRun(SubmitCreditApplication submitCreditApplication, CreditApplication sentBackCreditApplication)
    {
        var draft = ValidDraftDto.DraftToExecuteDecisionEngine;

        _creditApplicationServiceMock.Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(query =>
             query.Status![0] == CreditApplicationStatus.SentBack.ToString()), default))
            .ReturnsAsync(new List<CreditApplication>() { sentBackCreditApplication });
        _draftRepository.Setup(x => x.GetById(submitCreditApplication.DraftId, default)).ReturnsAsync(draft);
        _decisionEngineExecutionService.Setup(x =>
            x.StartCreditApplicationInitializationStep(It.Is<CreditApplicationInitializationStepStartRequest>(request => request.DraftId == draft.Id), default));

        await _creditApplicationExecutionService.SubmitCreditApplication(submitCreditApplication, default);

        _decisionEngineExecutionService.Verify(x => x.StartCreditApplicationInitializationStep(It.IsAny<CreditApplicationInitializationStepStartRequest>(), default), Times.Once);
    }

    [Fact]
    public Task RunDecisionEngineAsArAdvance_GetPaidDraftDoesNotExist_ThrowsVariableNullException()
    {
        var getPaidId = Guid.NewGuid().ToString();
        _creditApplicationServiceMock.Setup(x => x.GetById(getPaidId, default))!.ReturnsAsync((CreditApplication?)null);
        var act = () => _creditApplicationExecutionService.RunDecisionEngineAsArAdvance(getPaidId, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public Task RunDecisionEngineAsArAdvance_GetPaidApplicationIsNotApproved_ThrowsValidationException(CreditApplication creditApp)
    {
        var getPaidId = Guid.NewGuid().ToString();
        creditApp.Status = "processed";
        _creditApplicationServiceMock.Setup(x => x.GetById(getPaidId, default))!.ReturnsAsync(creditApp);
        var act = () => _creditApplicationExecutionService.RunDecisionEngineAsArAdvance(getPaidId, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task RunDecisionEngineAsArAdvance_DraftDoesNotExist_ThrowsVariableNullException(CreditApplication creditApp)
    {
        var getPaidId = Guid.NewGuid().ToString();
        creditApp.Status = "Approved";
        _creditApplicationServiceMock.Setup(x => x.GetById(getPaidId, default))!.ReturnsAsync(creditApp);
        _draftRepository.Setup(x => x.GetById(creditApp.DraftId!, default))!.ReturnsAsync((DraftDocument?)null);
        var act = () => _creditApplicationExecutionService.RunDecisionEngineAsArAdvance(getPaidId, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, CustomAutoData]
    public async Task RunDecisionEngineAsArAdvance_ValidData_RunsDecisionEngine(CreditApplication creditApp)
    {
        var draft = ValidDraftDto.DraftToExecuteDecisionEngine;
        var getPaidId = Guid.NewGuid().ToString();
        creditApp.Status = "Approved";
        var executionResult = new StepFunctionsExecutionResponse() { ExecutionArn = "arn" };
        _creditApplicationServiceMock.Setup(x => x.GetById(getPaidId, default))!.ReturnsAsync(creditApp);
        _draftRepository.Setup(x => x.GetById(creditApp.DraftId!, default))!.ReturnsAsync(draft);
        _decisionEngineExecutionService.Setup(x =>
            x.StartCreditApplicationInitializationStep(It.IsAny<CreditApplicationInitializationStepStartRequest>(), default))!.ReturnsAsync(executionResult);

        var result = await _creditApplicationExecutionService.RunDecisionEngineAsArAdvance(getPaidId, default);

        result.ShouldBe(executionResult);
        _decisionEngineExecutionService.Verify(x =>
            x.PreValidateCreditApplicationDecisionEngineExecution(CreditApplicationType.ARAdvance, draft.CompanyId,
                draft.CompanyId, "hash", default), Times.Once);
        _decisionEngineExecutionService.Verify(x =>
            x.StartCreditApplicationInitializationStep(It.IsAny<CreditApplicationInitializationStepStartRequest>(), default), Times.Once);
    }
}
