using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.SNS.SlackNotification;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services
{
    public class SlackNotificationServiceTests
    {
        private readonly SlackNotificationService _service;
        private readonly Mock<ILogger<SlackNotificationService>> _loggerMock;
        private readonly Mock<ISnsEndpoint> _snsEndpointMock;
        private readonly Mock<IOptions<SlackNotificationOptions>> _optionsMock;
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly SlackNotificationOptions _options;

        public SlackNotificationServiceTests()
        {
            _loggerMock = new Mock<ILogger<SlackNotificationService>>();
            _snsEndpointMock = new Mock<ISnsEndpoint>();
            _options = new SlackNotificationOptions { ErrorSnsTopicName = "TestTopic" };
            _optionsMock = new Mock<IOptions<SlackNotificationOptions>>();
            _optionsMock.Setup(o => o.Value).Returns(_options);
            _configurationMock = new Mock<IConfiguration>();
            _configurationMock.Setup(x => x["AWS-ACCOUNT-ID"]).Returns("id");
            _configurationMock.Setup(x => x["AWS-DEFAULT-REGION"]).Returns("region");

            _service = new SlackNotificationService(
                _loggerMock.Object,
                _snsEndpointMock.Object,
                _optionsMock.Object,
                _configurationMock.Object);
        }

        [Fact]
        public async Task Notify_WithValidMessageBody_CallsPublishSlackNotificationAsync()
        {
            // Arrange
            var message = new EventMessageBody
            {
                Message = "Test message",
                EventLevel = EventLevel.Error,
                EventName = "TestEvent",
                EventSource = "TestSource",
                ServiceName = "OnBoardingService",
                TimeStamp = "2021-01-01T00:00:00Z"
            };
            var traceId = "TestTraceId1";
            var cancellationToken = CancellationToken.None;
            
            // Act
            await _service.Notify(message, traceId, cancellationToken);

            // Assert
            _snsEndpointMock.Verify(s => s.PublishSlackNotificationAsync(
                    It.Is<EventMessageBody>(m => m.Message.Contains("BlueTapeCorrelationId: TestTraceId1")),
                    It.Is<string>(x => x.Contains("id") && x.Contains("region")),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task Notify_WithValidMessageBody_DevelopmentOrLocalEnvironment_DoesNotCallPublish()
        {
            // Arrange
            var message = new EventMessageBody
            {
                Message = "Test message",
                EventLevel = EventLevel.Error,
                EventName = "TestEvent",
                EventSource = "TestSource",
                ServiceName = "OnBoardingService",
                TimeStamp = "2021-01-01T00:00:00Z"
            };
            var traceId = "TestTraceId";
            var cancellationToken = CancellationToken.None;

            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            // Set environment variable to Development
            Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, EnvironmentConstants.Development);

            // Act
            await _service.Notify(message, traceId, cancellationToken);

            // restore env var
            Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

            // Assert
            _snsEndpointMock.Verify(
                s => s.PublishSlackNotificationAsync(It.IsAny<EventMessageBody>(), It.IsAny<string>(),
                    It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        }


        [Fact]
        public async Task Notify_WithMessageParameters_CallsInternalNotify()
        {
            // Arrange
            const string message = "Test message";
            const string eventName = "TestEvent";
            const EventLevel eventLevel = EventLevel.Error;
            const string traceId = "TestTraceId";
            var cancellationToken = CancellationToken.None;

            // Act
            await _service.Notify(message, eventName, eventLevel, traceId, cancellationToken);

            // Assert
            _snsEndpointMock.Verify(s => s.PublishSlackNotificationAsync(
                    It.Is<EventMessageBody>(m => m.Message.Contains("BlueTapeCorrelationId: TestTraceId")),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }
    }
}