﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using BlueTape.Services.OnBoardingService.Domain.Documents.CustomerAccount;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class CustomerAccountRepository(IObsMongoDBContext context) : ICustomerAccountRepository
{
    private readonly IMongoCollection<CustomerAccountDocument> _collection = context.GetCollection<CustomerAccountDocument>();

    public async Task UpdateCustomerStatus(string id, string status, CancellationToken cancellationToken)
    {
        if (!(status == CustomerStatus.Draft || status == CustomerStatus.Active || status == CustomerStatus.InActive ||
              status == CustomerStatus.Invited || status == CustomerStatus.Saved || status == CustomerStatus.Deleted ||
              status == CustomerStatus.New || status == CustomerStatus.ActiveCredit || status == CustomerStatus.TradeCredit))
        {
            throw new ArgumentException("Invalid status value", nameof(status));
        }

        if (string.IsNullOrWhiteSpace(id) || !ObjectId.TryParse(id, out var objectId))
        {
            throw new ArgumentException("Invalid or empty CustomerAccountId", nameof(id));
        }

        var filter = Builders<CustomerAccountDocument>.Filter.Where(doc => doc.Id == objectId.ToString());

        var document = await _collection.Find(filter).FirstOrDefaultAsync(cancellationToken);
        if (document == null)
        {
            throw new InvalidOperationException("Document not found.");
        }

        var update = Builders<CustomerAccountDocument>.Update.Set(doc => doc.Status, status);

        var updateResult = await _collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

        if (updateResult.MatchedCount == 0)
        {
            throw new InvalidOperationException("No document was updated, check the filter criteria.");
        }
    }

    public async Task<string[]> GetIdsByLogin(string? login, CancellationToken ct)
    {
        var expression = Builders<CustomerAccountDocument>.Filter;
        var filter = expression.Empty;

        if (!string.IsNullOrEmpty(login))
        {
            filter &= expression.Or(
                expression.Eq(x => x.Email, login),
                expression.Eq(x => x.Phone, login)
            );
        }

        var query = _collection.Find(filter);
        var result = await query.ToListAsync(ct);
        return result?.Where(x => !x.IsDeleted).Select(x => x.Id).ToArray() ?? [];
    }
}