﻿using BlueTape.Services.OnBoardingService.Application.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;


[Route("migration")]
[ApiController]
public class MigrationController(IMigrationService migrationService)
{
    /// <summary>
    /// Create getPaid Credit Applications + Authorization Details for suppliers
    /// </summary>
    [HttpPost("get-paid")]
    public Task MigrateGetPaidCreditApplications(CancellationToken ct)
    {
        return migrationService.MigrateGetPaidApplications(ct);
    }

    [HttpGet("decisionEngineSteps/ExecutionType")]
    public async Task MigrateDecisionEngineStepExecutionType(CancellationToken ct)
        => await migrationService.MigrateDecisionEngineExecutionType(ct);

    [HttpPost("CreditApplications/ExecutionId")]
    public async Task MigrateExecutionIdForCreditApplications(CancellationToken ct)
        => await migrationService.MigrateExecutionIdForCreditApplications(ct);
    
    [HttpPost("DrawApprovals/ExecutionId")]
    public async Task MigrateExecutionIdForDrawApprovals(CancellationToken ct)
        => await migrationService.MigrateExecutionIdForDrawApprovals(ct);
}
