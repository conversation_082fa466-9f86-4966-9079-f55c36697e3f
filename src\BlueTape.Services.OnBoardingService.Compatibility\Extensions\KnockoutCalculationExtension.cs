﻿using BlueTape.Services.OnBoardingService.Compatibility.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;

namespace BlueTape.Services.OnBoardingService.Compatibility.Extensions;
public static class KnockoutCalculationExtension
{
    public static Score Calculate(string name, double? fraudPointScore, int passThreshold)
    {
        var pass = ScoringResult.Review;
        if (fraudPointScore.HasValue)
            pass = fraudPointScore >= passThreshold ? ScoringResult.Pass : ScoringResult.Reject;

        return new Score()
        {
            Name = name,
            Value = fraudPointScore.HasValue ? fraudPointScore.ToString() : null,
            Pass = pass
        };
    }
}
