﻿using Newtonsoft.Json;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;
public static class ObjectExtensions
{
    public static T? DeepCopyJson<T>(T input)
    {
        var jsonString = JsonConvert.SerializeObject(input);
        return JsonConvert.DeserializeObject<T>(jsonString);
    }

    public static string ToFormattedJsonString<T>(this T input)
    {
        return JsonConvert.SerializeObject(input, Formatting.Indented);
    }
}
