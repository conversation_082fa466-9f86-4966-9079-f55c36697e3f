﻿using BlueTape.MongoDB.DTO.Base;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
public interface IDecisionEngineStepsBviResultsRepository : IGenericRepository<DecisionEngineStepsBviResultsDocument>
{
    Task<IEnumerable<DecisionEngineStepsBviResultsDocument>> GetAllByFilters(string? id, string? executionId,
        string? integrationLogId, string? decisionEngineStepId, string? creditApplicationId, CancellationToken ct);

    Task<IEnumerable<DecisionEngineStepsBviResultsDocument>> AddRange(
        IEnumerable<DecisionEngineStepsBviResultsDocument> documents, CancellationToken ct);

    Task<IEnumerable<RequestResponseBaseDocument?>> GetBviResponsesByStepId(string stepId,
        CancellationToken ct);

    Task<RequestResponseBaseDocument?> GetLexisNexisResponse(string creditApplicationId, LexisNexisSourceType type, string? reference,
        CancellationToken ct);

    Task<RequestResponseBaseDocument?> GetGiactResponse(string creditApplicationId, CancellationToken ct);

    Task<RequestResponseBaseDocument?> GetExperianResponse(string creditApplicationId, ExperianSourceType type, CancellationToken ct);
}
