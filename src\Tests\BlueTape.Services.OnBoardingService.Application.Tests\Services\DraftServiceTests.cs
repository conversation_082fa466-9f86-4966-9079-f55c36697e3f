﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.Application.Tests.DTOs.Draft;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services
{
    public class DraftServiceTests
    {
        private readonly IDraftService _draftService;
        private readonly Mock<IDraftRepository> _draftRepositoryMock = new();

        public DraftServiceTests()
        {
            var mapperConfig = new MapperConfiguration(
                cfg =>
                {
                    cfg.AddProfile(new ModelsProfile());
                });

            IMapper mapper = new Mapper(mapperConfig);
            _draftService = new DraftService(_draftRepositoryMock.Object, mapper);
        }

        [Theory, CustomAutoData]
        public async Task GetById_DraftIsExist_ReturnsDraft(string id, DraftDocument draft)
        {
            _draftRepositoryMock.Setup(x => x.GetById(id, default)).ReturnsAsync(draft);

            var result = await _draftService.GetById(id, default);

            result.CompanyId.ShouldBeEquivalentTo(draft.CompanyId);
        }

        [Theory, CustomAutoData]
        public async Task GetById_DraftIsNull_ReturnsNull(string id)
        {
            _draftRepositoryMock.Setup(x => x.GetById(id, default));

            var result = await _draftService.GetById(id, default);

            result.ShouldBeNull();
        }

        [Theory, CustomAutoData]
        public async Task GetAll_Valid_ReturnsDrafts(List<DraftDocument> drafts)
        {
            _draftRepositoryMock.Setup(x => x.GetAll(default)).ReturnsAsync(drafts);

            var result = await _draftService.GetAll(default);

            result.Count().ShouldBe(drafts.Count);
            result.First().CompanyId.ShouldBeEquivalentTo(drafts.First().CompanyId);
        }

        [Fact]
        public async Task GetAll_DraftsIsNotExist_ReturnsEmptyList()
        {
            _draftRepositoryMock.Setup(x => x.GetAll(default));

            var result = await _draftService.GetAll(default);

            result.Count().ShouldBe(0);
        }

        [Fact]
        public async Task GetAllByFilters_IdIsNull_ReturnsDrafts()
        {
            var drafts = ValidDraftDto.ListOfDraftsForIdIsNullScenario;
            var expectedDrafts =
                ValidDraftDto.ListOfExpectedDraftsForIdIsNullScenario;
            var filters = new List<string>();
            var firstDraft = drafts.First();
            filters.Add(firstDraft.CompanyId!);
            filters.Add(firstDraft.CreditApplicationId!);

            _draftRepositoryMock.Setup(x => x.GetAllByFilters(null, firstDraft.CompanyId, firstDraft.CreditApplicationId, default)).ReturnsAsync(expectedDrafts);

            var result = await _draftService.GetAllByFilters(new DraftFilter()
            {
                CompanyId = firstDraft.CompanyId,
                CreditApplicationId = firstDraft.CreditApplicationId,
            }, default);

            result.Count().ShouldBe(2);
        }

        [Fact]
        public async Task GetAllByFilters_CompanyIdIsNull_ReturnsDrafts()
        {
            var drafts = ValidDraftDto.ListOfDraftsForCompanyIdIsNullScenario;
            var expectedDrafts =
                ValidDraftDto.ListOfExpectedDraftsForCompanyIdIsNullScenario;
            var filters = new List<string>();
            var firstDraft = drafts.First();
            filters.Add(firstDraft.Id);
            filters.Add(firstDraft.CreditApplicationId!);

            _draftRepositoryMock.Setup(x => x.GetAllByFilters(It.Is<string>(draftId => draftId == firstDraft.Id), It.IsAny<string>(), It.IsAny<string>(), default)).ReturnsAsync(expectedDrafts);

            var result = await _draftService.GetAllByFilters(new DraftFilter()
            {
                Id = firstDraft.Id,
            }, default);

            result.Count().ShouldBe(2);
        }

        [Fact]
        public async Task GetAllByFilters_FilterIsEmpty_ReturnsDrafts()
        {
            var drafts = ValidDraftDto.ListOfDraftsForIdAndCompanyIdAreNullScenario;
            var expectedDrafts =
                ValidDraftDto.ListOfExpectedDraftsForIdAndCompanyIdAreNullScenario;
            var filters = new List<string>();
            var firstDraft = drafts.First();
            filters.Add(firstDraft.CreditApplicationId!);

            _draftRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), default)).ReturnsAsync(expectedDrafts);

            var result = await _draftService.GetAllByFilters(new DraftFilter(), default);

            result.Count().ShouldBe(2);
        }

        [Theory, CustomAutoData]
        public async Task GetByCompanyIds_ValidCompanyIds_ReturnsDrafts(string[] companyIds, List<DraftDocument> drafts)
        {
            _draftRepositoryMock.Setup(x => x.GetByCompanyIds(companyIds, default)).ReturnsAsync(drafts);

            var result = await _draftService.GetByCompanyIds(companyIds, default);

            result.Count().ShouldBe(drafts.Count);
            result.First().CompanyId.ShouldBeEquivalentTo(drafts.First().CompanyId);
        }

        [Fact]
        public async Task GetByCompanyIds_CompanyIdsIsEmpty_ReturnsEmptyList()
        {
            var companyIds = new string[] { };
            _draftRepositoryMock.Setup(x => x.GetByCompanyIds(companyIds, default)).ReturnsAsync(new List<DraftDocument>());

            var result = await _draftService.GetByCompanyIds(companyIds, default);

            result.Count().ShouldBe(0);
        }

        [Fact]
        public async Task GetByCompanyIds_CompanyIdsIsNull_ReturnsEmptyList()
        {
            string[] companyIds = null;
            _draftRepositoryMock.Setup(x => x.GetByCompanyIds(companyIds, default)).ReturnsAsync(new List<DraftDocument>());

            var result = await _draftService.GetByCompanyIds(companyIds, default);

            result.Count().ShouldBe(0);
        }

    }
}