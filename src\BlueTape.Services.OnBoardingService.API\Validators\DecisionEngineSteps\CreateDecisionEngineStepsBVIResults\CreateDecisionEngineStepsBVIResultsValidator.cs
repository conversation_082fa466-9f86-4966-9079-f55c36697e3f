﻿using BlueTape.OBS.DTOs.DecisionEngineSteps;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.DecisionEngineSteps.CreateDecisionEngineStepsBVIResults;

public class CreateDecisionEngineStepsBviResultsValidator : AbstractValidator<CreateDecisionEngineStepsBVIResultsDto>
{
    public CreateDecisionEngineStepsBviResultsValidator()
    {
        RuleFor(x => x.DecisionEngineStepId).NotNull().NotEmpty();
        RuleFor(x => x.IntegrationLogId).NotEmpty().NotNull();
    }
}

public class CreateDecisionEngineStepsBviResultsRangeValidator : AbstractValidator<IEnumerable<CreateDecisionEngineStepsBVIResultsDto>>
{
    public CreateDecisionEngineStepsBviResultsRangeValidator()
    {
        RuleForEach(x => x).SetValidator(new CreateDecisionEngineStepsBviResultsValidator()).When(x => x.Any());
    }
}