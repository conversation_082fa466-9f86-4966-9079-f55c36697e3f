﻿using BlueTape.LS.DTOs.Loan;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Logging;
using Polly;
using System.Text.RegularExpressions;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class LinqPalInteractionService(
    INodeExternalService nodeExternalService,
    ILoanExternalService loanExternalService,
    ILogger<LinqPalInteractionService> logger) : ILinqPalInteractionService
{
    public Task StartHumanApprovalProcess(LoanApplicationDocument? loanApplication, DrawApprovalDocument drawApproval, CancellationToken ctx)
    {
        var issueLoanRequest = new IssueLoanRequest()
        {
            ApplicationId = loanApplication?.Id,
            Decision =
            {
                ApprovedAmount = drawApproval.DrawAmount,
                Status = AdminDrawApprovalStatusUpdate.Approved
            },
            DrawApprovalType = drawApproval.Type,
            DrawApprovalId = drawApproval.Id
        };

        return nodeExternalService.HumanApproval(issueLoanRequest, ctx);
    }

    public Task StartIssueLoanProcess(DrawApprovalDocument drawApproval, CancellationToken ctx)
    {
        var issueLoanRequest = new IssueLoanRequest()
        {
            DrawApprovalId = drawApproval.Id,
            Decision =
            {
                ApprovedAmount = drawApproval.DrawAmount,
                Status = AdminDrawApprovalStatusUpdate.Approved
            }
        };

        return nodeExternalService.IssueFactoringLoan(issueLoanRequest, ctx);
    }

    public Task StartIssueLoanPrequalifiedProcess(LoanApplicationDocument loanApplication, decimal approvedAmount, CancellationToken ctx)
    {
        var issueLoanRequest = new IssueLoanPrequalifiedRequest()
        {
            ApplicationId = loanApplication.Id,
            ApprovedAmount = approvedAmount,
        };

        return nodeExternalService.IssueLoanPrequalified(issueLoanRequest, ctx);
    }

    public Task SendOpsTeamNotification(LoanApplicationDocument? loanApplication, CancellationToken ctx)
    {
        if (loanApplication == null)
        {
            logger.LogWarning("StartIssueLoanProcess: loan application is null");
            return Task.CompletedTask;
        }

        var issueLoanRequest = new OpsTeamNotificationRequest()
        {
            ApplicationId = loanApplication.Id
        };

        return nodeExternalService.NotifyOpsTeam(issueLoanRequest, ctx);
    }

    public async Task SendUserApprovalNotification(string loanApplicationId, string drawApprovalId, bool isQuote, CancellationToken ctx)
    {
        if (string.IsNullOrEmpty(drawApprovalId) || string.IsNullOrEmpty(loanApplicationId))
        {
            logger.LogWarning("SendUserApprovalNotification: draw Approval id or loan application id is null");
            return;
        }

        if (isQuote)
        {
            logger.LogWarning("SendUserApprovalNotification: draw Approval is quote - no notifications needs");
            return;
        }

        var loans = await FindLoansWithPollyRetry(drawApprovalId, ctx);
        var loan = loans.FirstOrDefault();
        var firstLoanReceivable = loan?.LoanReceivables?.MinBy(x => x.ExpectedDate);

        if (firstLoanReceivable == null) return;

        var userApprovalNotificationRequest = new UserApprovalNotificationRequest()
        {
            ApplicationId = loanApplicationId,
            FirstPaymentDate = firstLoanReceivable.ExpectedDate
        };

        await nodeExternalService.TriggerUserApprovalNotification(userApprovalNotificationRequest, ctx);
    }

    private async Task<List<LoanDto>> FindLoansWithPollyRetry(string drawApprovalId, CancellationToken ctx)
    {
        // Retry 4 times with increasing delays: 2s, 5s, 10s
        var retryPolicy = Policy
            .HandleResult<List<LoanDto>>(loans => loans == null || loans.Count == 0)
            .WaitAndRetryAsync(
                new[]
                {
                    TimeSpan.FromSeconds(2),
                    TimeSpan.FromSeconds(5),
                    TimeSpan.FromSeconds(10)
                },
                (result, timeSpan, retryCount, context) =>
                {
                    logger.LogWarning("FindLoans attempt {RetryCount} failed, retrying in {Delay}s...", retryCount, timeSpan.TotalSeconds);
                });

        return await retryPolicy.ExecuteAsync(async () =>
            await loanExternalService.FindLoans(new LoanQueryDto() { DrawApprovalId = drawApprovalId }, ctx)
        );
    }

    public Task SendLoanCancellationCustomerNotification(LoanApplicationDocument loanApplication, CancellationToken ctx)
    {
        var request = new LoanCancellationCustomerNotificationRequest
        {
            ApplicationId = loanApplication.Id
        };

        return nodeExternalService.TriggerLoanCancellationCustomerNotification(request, ctx);
    }

    public Task SendSentBackUserNotification(LoanApplicationDocument loanApplication, CancellationToken ctx)
    {
        var issueLoanRequest = new SendBackUserNotificationRequest()
        {
            ApplicationId = loanApplication.Id
        };

        return nodeExternalService.TriggerSentBackUserNotification(issueLoanRequest, ctx);
    }

    public Task StartRejectLoanProcess(LoanApplicationDocument? loanApplication, string? drawApprovalId, string reason, CancellationToken ctx)
    {
        var userRejectedNotificationRequest = new UserRejectedNotificationRequest()
        {
            ApplicationId = loanApplication?.Id ?? string.Empty,
            Data =
            {
                Reason = reason
            },
            DrawApprovalId = loanApplication?.DrawApprovalId ?? drawApprovalId ?? string.Empty
        };

        return nodeExternalService.RejectLoan(userRejectedNotificationRequest, ctx);
    }

    public Task StartCancelLoanApplicationProcess(LoanApplicationDocument? loanApplication, string? drawApprovalId, CancellationToken ctx, bool notify = false)
    {
        var cancelLoanApplicationRequest = new CancelLoanApplicationRequest()
        {
            ApplicationId = loanApplication?.Id ?? string.Empty,
            Notify = notify,
            DrawApprovalId = loanApplication?.DrawApprovalId ?? drawApprovalId ?? string.Empty
        };

        return nodeExternalService.TriggerCancelLoanApplication(cancelLoanApplicationRequest, ctx);
    }

    public Task SendIhcDrawApprovalRejectedOrApprovedNotification(DrawApprovalDocument document, CancellationToken ct)
    {
        if (!string.Equals(DrawApprovalType.Factoring.ToString(), document.Type, StringComparison.InvariantCultureIgnoreCase)
            || !string.Equals(DrawApprovalStatus.Approved.ToString(), document.Status, StringComparison.InvariantCultureIgnoreCase)
            && !string.Equals(DrawApprovalStatus.Rejected.ToString(), document.Status, StringComparison.InvariantCultureIgnoreCase))
            return Task.CompletedTask;

        var model = new DrawApprovalReviewUserNotification
        {
            InvoiceNumber = document.Payables.FirstOrDefault()!.InvoiceNumber!,
            CompanyName = Regex.Replace(document.CompanyName, @"\s*/\s*$", "", RegexOptions.None, TimeSpan.FromMilliseconds(100)),
            MerchantId = document.MerchantId,
            Decision = document.Status,
            InvoiceAmount = document.DrawAmount
        };

        return nodeExternalService.TriggerIhcDrawApprovalCustomerNotification(model, ct);
    }
}