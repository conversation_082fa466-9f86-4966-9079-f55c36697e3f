﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
[MongoCollection("drawapprovals")]
public class DrawApprovalDocument : Document
{
    [BsonElement("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    [BsonElement("updatedBy")]
    public string UpdatedBy { get; set; } = string.Empty;

    [BsonElement("companyId")]
    public string CompanyId { get; set; } = string.Empty;

    [BsonElement("companyName")]
    public string CompanyName { get; set; } = string.Empty;

    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("einHash")]
    public string EinHash { get; set; } = string.Empty;

    [BsonElement("creditId")]
    public string? CreditId { get; set; } = string.Empty;

    [BsonElement("arAdvanceCreditId")]
    public string? ArAdvanceCreditId { get; set; } = string.Empty;

    [BsonElement("inHouseCreditId")]
    public string? InHouseCreditId { get; set; } = string.Empty;

    [BsonElement("applicationDate")]
    public DateTime ApplicationDate { get; set; }

    [BsonElement("applicantName")]
    public string ApplicantName { get; set; } = string.Empty;

    [BsonElement("paymentPlanId")]
    public string PaymentPlanId { get; set; } = string.Empty;

    [BsonElement("type")]
    public string Type { get; set; } = string.Empty;

    [BsonIgnoreIfNull]
    [BsonElement("loanOrigin")]
    public string? LoanOrigin { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("virtualCardId")]
    public string? VirtualCardId { get; set; }

    [BsonElement("merchantId")]
    public string MerchantId { get; set; } = string.Empty;

    [BsonElement("merchantName")]
    public string MerchantName { get; set; } = string.Empty;

    [BsonElement("drawAmount")]
    public decimal DrawAmount { get; set; }

    [BsonElement("creditHoldAmount")]
    [BsonIgnoreIfNull]
    public decimal? CreditHoldAmount { get; set; }

    [BsonElement("expirationDate")]
    [BsonIgnoreIfNull]
    public DateTime? ExpirationDate { get; set; }

    [BsonElement("authorizationPeriodId")]
    public string? AuthorizationPeriodId { get; set; }

    [BsonElement("arAdvanceAuthorizationPeriodId ")]
    public string? ArAdvanceAuthorizationPeriodId { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("drawAmountRiskLevel")]
    public string? DrawAmountRiskLevel { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("projectId")]
    public string? ProjectId { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("automatedDecisionResult")]
    public string? AutomatedDecisionResult { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("automatedApprovalResult")]
    public string? AutomatedApprovalResult { get; set; }

    [BsonElement("status")]
    public string Status { get; set; } = string.Empty;

    [BsonElement("lastStatusChangedAt")]
    public DateTime LastStatusChangedAt { get; set; }

    [BsonElement("lastStatusChangedBy")]
    public string LastStatusChangedBy { get; set; } = string.Empty;

    [BsonIgnoreIfNull]
    [BsonElement("approvedAt")]
    public DateTime? ApprovedAt { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("approvedBy")]
    public string? ApprovedBy { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("rejectedAt")]
    public DateTime? RejectedAt { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("rejectedBy")]
    public string? RejectedBy { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("canceledAt")]
    public DateTime? CanceledAt { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("canceledBy")]
    public string? CanceledBy { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("lastRerunAt")]
    public DateTime? LastRerunAt { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("lastRerunBy")]
    public string? LastRerunBy { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("invoicedAt")]
    public DateTime? InvoicedAt { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("invoicedBy")]
    public string? InvoicedBy { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("statusCode")]
    public string? StatusCode { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("statusNote")]
    public string? StatusNote { get; set; }

    [BsonElement("executionId")]
    public string? ExecutionId { get; set; }

    [BsonElement("drawDetails")]
    public DrawDetailsDocument DrawDetails { get; set; } = new();

    [BsonElement("factoringOverallDetails")]
    public FactoringOverallDetailsDocument FactoringOverallDetails { get; set; } = new();

    [BsonElement("factoringDetails")]
    public FactoringDetailsDocument FactoringDetails { get; set; } = new();

    [BsonElement("payNowDetails")]
    public PayNowDetailsDocument PayNowDetails { get; set; } = new();

    [BsonElement("automatedApprovalDetails")]
    public AutomatedApprovalDetailsDocument AutomatedApprovalDetails { get; set; } = new();

    [BsonElement("payables")]
    public IEnumerable<PayableItemDocument> Payables { get; set; } = new List<PayableItemDocument>();

    [BsonElement("noSupplierDetails")]
    public NoSupplierDetailsDocument? NoSupplierDetails = new();

    [BsonIgnoreIfNull]
    [BsonElement("downPaymentDetails")]
    public DownPaymentDetailsDocument? DownPaymentDetails = new();

    [BsonIgnoreIfNull]
    [BsonElement("compatibilityDetails")]
    public CompatibilityDetailsDocument CompatibilityDetails = new();

    [BsonElement("approvedStatusSynchronizedAt")]
    public DateTime? ApprovedStatusSynchronizedAt { get; set; }

    [BsonElement("debtInvestor")]
    public string? DebtInvestor { get; set; }


    // Not stored in the database, will be evaluated during query execution
    [BsonElement("term")]
    [BsonIgnoreIfNull]
    public int? Term { get; set; }

    [BsonElement("name")]
    [BsonIgnoreIfNull]
    public string? LoanPricingPackageName { get; set; }
}
