﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.User;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class UserRepository(IObsMongoDBContext context, ILogger<GenericRepository<UserDocument>> logger)
    : GenericRepository<UserDocument>(context, logger), IUserRepository
{
    private readonly IMongoCollection<UserDocument> _collection = context.GetCollection<UserDocument>();

    public async Task<UserDocument?> GetBySub(string sub, CancellationToken ct)
    {
        var result = _collection.Find(x => x.Sub == sub);
        var userDocument = await result.FirstOrDefaultAsync(ct);

        return userDocument;
    }
}
