﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class DownPaymentDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("percentage")]
    public decimal? Percentage { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("amount")]
    public decimal? Amount { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("paymentMethod")]
    public string? PaymentMethod { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("accountId")]
    public string? AccountId { get; set; }
}

