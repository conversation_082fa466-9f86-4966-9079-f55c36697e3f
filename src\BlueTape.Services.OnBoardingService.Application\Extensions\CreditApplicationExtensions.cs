﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;

public static class CreditApplicationExtensions
{
    public static string GetShortType(this CreditApplicationType type)
    {
        return type switch
        {
            CreditApplicationType.LineOfCredit => "loc",
            CreditApplicationType.InHouseCredit => "ihc",
            CreditApplicationType.ARAdvance => "aradvance",
            CreditApplicationType.GetPaid => "getPaid",
            _ => "loc"
        };
    }

    public static bool IsInStatus(this CreditApplicationDocument creditApplication, CreditApplicationStatus status)
    {
        return string.Equals(creditApplication.Status, status.ToString(), StringComparison.InvariantCultureIgnoreCase);
    }

    public static string GetCreditApplicationsPreValidationFailureReason(string einHash,
        IEnumerable<CreditApplicationDocument> existingCompanyCreditApplications)
    {
        if (existingCompanyCreditApplications.Any(x => x.IsInStatus(CreditApplicationStatus.Approved)))
        {
            return $"Company with EIN hash:{einHash} already has approved credit applications";
        }

        if (existingCompanyCreditApplications.Any(x => x.IsInStatus(CreditApplicationStatus.Processing)))
        {
            return $"Company with EIN hash:{einHash} already has credit applications which are currently processing by Decision Engine";
        }

        return $"Company with EIN hash:{einHash} already has credit applications in review status";
    }
}
