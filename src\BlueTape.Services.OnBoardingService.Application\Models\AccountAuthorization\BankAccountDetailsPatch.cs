﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
public class BankAccountDetailsPatch
{
    public string? Id { get; set; }
    public string? Name { get; set; }
    public string? Type { get; set; }
    public decimal? CompanyNameScore { get; set; }
    public decimal? PersonalNameScore { get; set; }
    public decimal? CompanyAddressScore { get; set; }
    public decimal? PersonalAddressScore { get; set; }
    public decimal? NameScore { get; set; }
    public decimal? AddressScore { get; set; }
}
