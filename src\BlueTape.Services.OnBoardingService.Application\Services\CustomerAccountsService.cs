using System.Drawing;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class CustomerAccountsService(ICustomerAccountRepository customerAccountRepository, IUserService userService) : ICustomerAccountsService
{
    public async Task<string[]> GetCustomerAccountIdsByCompanyId(string companyId, CancellationToken ct)
    {
        var user = await userService.GetByCompanyId(companyId, ct);
        if (string.IsNullOrEmpty(user?.Login)) return [];
        return await customerAccountRepository.GetIdsByLogin(user.Login, ct);
    }
}