namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;

public class CreateCreditApplicationNote
{
    public string CreditApplicationId { get; set; } = string.Empty;
    public string Note { get; set; } = string.Empty;
    public string? Caption { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? ExecutionId { get; set; }
    public DateTime? CreatedAt { get; set; }
}
