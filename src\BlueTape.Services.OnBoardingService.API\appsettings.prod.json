{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "AllowedHosts": "*", "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_prod"}, "StepsReportOptions": {"BucketName": "prod.uw1.linqpal-temp-assets"}, "SlackNotification": {"ErrorSnsTopicName": "obs-notifications-prod"}, "ConnectorQueueOptions": {"TopicName": "netsuite-connector-prod.fifo"}, "InitializationStepOptions": {"CreditApplicationsStateMachineName": "dotnet-decision-engine-prod", "DrawApprovalsStateMachineName": "dotnet-draw-approval-prod"}}