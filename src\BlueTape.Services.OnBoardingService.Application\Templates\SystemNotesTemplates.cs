﻿using BlueTape.Services.OnBoardingService.Application.Models.Notes;
using BlueTape.Services.OnBoardingService.Domain.Enums;
using System.Collections.Frozen;

namespace BlueTape.Services.OnBoardingService.Application.Templates;

public static class SystemNotesTemplates
{
    public static readonly FrozenDictionary<CreditApplicationNotesTemplateType, Func<SystemNoteTemplateInfo, string>>
        CreditApplicationsNoteBuilders = new Dictionary<CreditApplicationNotesTemplateType, Func<SystemNoteTemplateInfo, string>>()
        {
            {CreditApplicationNotesTemplateType.CreditLimitManuallyChanged, BuildCreditLimitManuallyChangedNote}
        }.ToFrozenDictionary();

    private static string BuildCreditLimitManuallyChangedNote(SystemNoteTemplateInfo systemNoteTemplateInfo)
    {
        return $"<System> Credit Limit has been changed manually to ${systemNoteTemplateInfo.NewValue}. (Original: ${systemNoteTemplateInfo.OldValue})";
    }
}
