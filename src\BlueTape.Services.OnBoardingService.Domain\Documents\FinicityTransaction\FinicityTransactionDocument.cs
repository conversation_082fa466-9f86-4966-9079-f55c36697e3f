﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.FinicityTransaction;

[BsonIgnoreExtraElements]
[MongoCollection("finicitytransactions")]
public class FinicityTransactionDocument : Document
{
    [BsonElement("companyId")]
    public string CompanyId { get; set; }

    [BsonElement("transactionDate")]
    public int TransactionDate { get; set; }

    [BsonElement("amount")]
    public double Amount { get; set; }

    [BsonElement("balance")]
    public double Balance { get; set; }
}
