﻿using BlueTape.Services.OnBoardingService.Domain.Documents;
using System.Linq.Expressions;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions
{
    public interface IGenericRepository<TDocument> where TDocument : Document
    {
        Task<TDocument> Add(TDocument entity, CancellationToken ct);
        Task<IEnumerable<TDocument>> GetAll(CancellationToken ct);
        Task<IEnumerable<TDocument>> GetAll(Expression<Func<TDocument, bool>> predicate, CancellationToken ct);
        Task<TDocument> GetById(string id, CancellationToken ct);
        Task<TDocument> Update(TDocument entity, CancellationToken ct);
        Task<IEnumerable<TDocument>> AddRange(IEnumerable<TDocument> documents, CancellationToken ct);
    }
}
