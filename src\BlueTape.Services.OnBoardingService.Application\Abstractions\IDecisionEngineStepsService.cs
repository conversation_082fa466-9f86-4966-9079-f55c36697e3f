﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IDecisionEngineStepsService
{
    Task<IEnumerable<DecisionEngineSteps>> GetByCreditApplicationId(string creditApplicationId, CancellationToken ct);

    Task<IEnumerable<DecisionEngineSteps>> GetByDrawApprovalId(string drawApprovalId, CancellationToken ct);

    Task<DecisionEngineSteps> GetById(string id, CancellationToken ct);

    Task<Dictionary<string, IEnumerable<DecisionEngineSteps>>> GetLatestByCompanyIds(string[] companyIds,
        CancellationToken ct);
    Task<DecisionEngineSteps> Create(CreateDecisionEngineSteps createModel, CancellationToken ct);


    Task<DecisionEngineSteps> Update(UpdateDecisionEngineSteps updateModel, CancellationToken ct);

    Task<IEnumerable<DecisionEngineSteps>> GetByExecutionId(string executionId, CancellationToken ct);
    Task<IEnumerable<DecisionEngineSteps>> GetByCompanyId(string companyId, CreditApplicationType? creditApplicationType, CancellationToken ct);
    Task<IEnumerable<DecisionEngineSteps>> GetByQuery(DecisionEngineStepsQueryModel query, CancellationToken ct);
    Task ReinstateDecisionEngineRule(string id, string? identifier, string userId, ReinstateDecisionEngineRuleModel reinstateModel, CancellationToken ct);
    Task IgnoreDecisionEngineRule(string id, string? identifier,string userid,  IgnoreDecisionEngineRuleModel ignoreModel, CancellationToken ct);
}
