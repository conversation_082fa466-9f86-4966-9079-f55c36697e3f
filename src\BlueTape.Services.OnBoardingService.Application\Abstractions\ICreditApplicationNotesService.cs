using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface ICreditApplicationNotesService
{
    Task<IEnumerable<CreditApplicationNote>> GetByApplicationId(string id, CancellationToken ct);
    Task<CreditApplicationNote> Add(CreateCreditApplicationNote note, CancellationToken ct);
    Task<IEnumerable<CreditApplicationNote>> AddRange(IEnumerable<CreateCreditApplicationNote> notes, CancellationToken ct);
    Task<CreditApplicationNote> AddSystemNote(CreateSystemCreditApplicationNote note, CancellationToken ct);
    Task<CreditApplicationNote> Patch(PatchCreditApplicationNote patchModel, CancellationToken ct);
    Task Delete(DeleteCreditApplicationNote note, CancellationToken ct);
}
