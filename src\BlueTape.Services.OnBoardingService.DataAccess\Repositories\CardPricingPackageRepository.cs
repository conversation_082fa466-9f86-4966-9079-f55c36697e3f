﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.PricingPackages;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class CardPricingPackageRepository : GenericRepository<CardPricingPackageDocument>, ICardPricingPackageRepository
{
    public CardPricingPackageRepository(IObsMongoDBContext context, ILogger<GenericRepository<CardPricingPackageDocument>> logger) : base(context, logger)
    {
    }

    public async Task<IEnumerable<CardPricingPackageDocument>> GetByTitles(IEnumerable<string> titles,
        CancellationToken ctx)
    {
        var expression = Builders<CardPricingPackageDocument>.Filter;
        var filter = Builders<CardPricingPackageDocument>.Filter.Empty;
        filter &= expression.In(x => x.Name, titles);

        return await Collection.Find(filter).ToListAsync(ctx);
    }
}
