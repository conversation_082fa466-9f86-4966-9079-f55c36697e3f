﻿using BlueTape.InvoiceClient.DI;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.DI
{
    public static class InvoiceExternalServiceExtensions
    {
        public static void AddInvoiceExternalServiceDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddScoped<IInvoiceExternalService, InvoiceExternalService>();
            services.AddInvoiceServiceClient(config);
        }
    }
}
