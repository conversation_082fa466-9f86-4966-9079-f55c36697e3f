.publishing:base:
  stage: publish
  variables:
    GIT_DEPTH: "0"
  script:
    - apt-get update
    - apt-get -y install zip
    - dotnet tool restore
    - dotnet lambda package -pl src/BlueTape.Services.OnBoardingService.API/ -o publish/api-$CI_PIPELINE_ID.zip

  artifacts:
    name: $CI_JOB_NAME
    paths:
      - publish/api-$CI_PIPELINE_ID.zip
    expire_in: 1 week

publishing:api:
  extends: .publishing:base
  needs: ["building:api"]
  only:
    !reference [ "building:api", only ]
  when: manual

publishing:prod:
  extends: .publishing:base
  needs: ["building:prod"]
  only:
    !reference [ "building:prod", only ]
  when: manual
