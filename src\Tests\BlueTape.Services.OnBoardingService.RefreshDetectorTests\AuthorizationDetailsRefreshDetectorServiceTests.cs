﻿using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Configuration.Models;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorTests;

public class AuthorizationDetailsRefreshDetectorServiceTests
{
    private readonly Mock<IAuthorizationDetailsRefreshConfigurationService> _mockConfigService;
    private readonly Mock<ICreditApplicationRepository> _mockCreditApplicationRepo;
    private readonly Mock<ICompanyScheduledUpdateEventsGenerator> _mockEventsGenerator;
    private readonly Mock<IDecisionEngineStepsService> _mockStepsService;
    private readonly Mock<IRefreshServiceMessageSender> _mockMessageSender;
    private readonly Mock<ICreditApplicationNotesService> _creditApplicationNotesServiceMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<ICompanyService> _mockCompanyService;
    private readonly Mock<ILogger<AuthorizationDetailsRefreshDetectorService>> _mockLogger;
    private readonly AuthorizationDetailsRefreshDetectorService _service;
    private readonly AuthorizationDetailsDetectorMessagingOptions _messagingOptions;
    private readonly Mock<IKeyVaultService> _keyVaultService;
    private readonly Mock<IOptions<AuthorizationDetailsDetectorMessagingOptions>> _options;

    public AuthorizationDetailsRefreshDetectorServiceTests()
    {
        _mockConfigService = new Mock<IAuthorizationDetailsRefreshConfigurationService>();
        _mockCreditApplicationRepo = new Mock<ICreditApplicationRepository>();
        _mockEventsGenerator = new Mock<ICompanyScheduledUpdateEventsGenerator>();
        _mockStepsService = new Mock<IDecisionEngineStepsService>();
        _mockMessageSender = new Mock<IRefreshServiceMessageSender>();
        _dateProviderMock = new Mock<IDateProvider>();
        _creditApplicationNotesServiceMock = new Mock<ICreditApplicationNotesService>();
        _mockCompanyService = new Mock<ICompanyService>();
        _keyVaultService = new Mock<IKeyVaultService>();
        _mockLogger = new Mock<ILogger<AuthorizationDetailsRefreshDetectorService>>();
        _options = new();
        _messagingOptions = new()
        {
            ScheduledPeriodDurationBetweenMessagesInMinutes = 2,
            MaxSimultaneouslySentMessagesCount = 2,
            MaxCompaniesRefreshedCount = 150,
            MaxMessagesInBatchCount = 300

        };
        _options.Setup(x => x.Value).Returns(_messagingOptions);

        _service = new AuthorizationDetailsRefreshDetectorService(
            _mockConfigService.Object,
            _mockCreditApplicationRepo.Object,
            _mockEventsGenerator.Object,
            _mockStepsService.Object,
            _mockMessageSender.Object,
            _dateProviderMock.Object,
            _creditApplicationNotesServiceMock.Object,
            _mockCompanyService.Object,
            _keyVaultService.Object,
            _options.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task Run_ShouldNotSendEvents_WhenNoCompaniesForScheduledUpdates()
    {
        var activeCompanies = new List<CompanyModel> { new CompanyModel { Id = "Company1" } };
        _mockCompanyService.Setup(service => service.GetCompaniesByActiveAccounts(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeCompanies);

        _mockCreditApplicationRepo.Setup(repo => repo.GetLightCreditApplications(
            It.IsAny<string[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<LightCreditApplicationDocument>());

        _mockConfigService.Setup(manager => manager.GetRefreshChecks(default))
            .ReturnsAsync(new List<RefreshCheckConfiguration>());

        _keyVaultService.Setup(manager => manager.GetSecret(It.IsAny<string>())).ReturnsAsync(string.Empty);

        await _service.Run(default);

        _mockMessageSender.Verify(sender => sender.SendMessages(It.IsAny<IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>>>(), default), Times.Never);
    }

    [Fact]
    public async Task Run_ShouldSendEvents_WhenCompaniesAndApprovedCreditApplicationsExist()
    {
        // Arrange
        var activeCompanies = new List<CompanyModel> { new() { Id = "Company1", CreatedAt = DateTime.UtcNow } };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1" }
        };
        var scheduledChecks = new List<RefreshCheckConfiguration> { new() { ScheduledUpdate = "ScheduledUpdate" } };
        var lastCompanyExecutions = new List<DecisionEngineSteps>
        {
            new() { Step = "Step1", UpdatedAt = DateTime.UtcNow }
        };
        var generatedEvents = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>
        {
            new(new ScheduledUpdateEvent()
            {
                Details = new ScheduledUpdateDetails(),
                EventType= "ScheduledUpdate.ScheduledUpdate"
            }, new ServiceBusMessageAttributes()
            {
                CorrelationId = "id"
            })
        };

        _mockCompanyService.Setup(service => service.GetCompaniesByActiveAccounts(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeCompanies);

        _mockCreditApplicationRepo.Setup(repo => repo.GetLightCreditApplications(
            It.IsAny<string[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(approvedCreditApplications);

        _mockConfigService.Setup(manager => manager.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);

        _keyVaultService.Setup(manager => manager.GetSecret(It.IsAny<string>())).ReturnsAsync(string.Empty);

        _mockStepsService.Setup(service => service.GetLatestByCompanyIds(
            It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, IEnumerable<DecisionEngineSteps>>
            {
                { "Company1", lastCompanyExecutions }
            });

        _mockEventsGenerator.Setup(generator => generator.GenerateScheduledUpdateEvents(
            It.IsAny<CompanyModel>(), It.IsAny<RefreshCheckConfiguration>(), It.IsAny<IReadOnlyList<LightCreditApplicationDocument>>(), It.IsAny<IReadOnlyList<DecisionEngineSteps>>()))
            .Returns(generatedEvents);

        _mockMessageSender.Setup(sender => sender.SendMessages(It.IsAny<IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        await _service.Run(default);

        _mockMessageSender.Verify(sender => sender.SendMessages(generatedEvents, default), Times.Once);
    }

    [Fact]
    public async Task Run_ShouldHandleNoLatestStepsGracefully()
    {
        var activeCompanies = new List<CompanyModel> { new() { Id = "Company1" } };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1" }
        };
        var scheduledChecks = new List<RefreshCheckConfiguration> { new() };

        _mockCompanyService.Setup(service => service.GetCompaniesByActiveAccounts(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeCompanies);

        _mockCreditApplicationRepo.Setup(repo => repo.GetLightCreditApplications(
            It.IsAny<string[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(approvedCreditApplications);

        _mockConfigService.Setup(manager => manager.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);

        _keyVaultService.Setup(manager => manager.GetSecret(It.IsAny<string>())).ReturnsAsync(string.Empty);

        _mockStepsService.Setup(service => service.GetLatestByCompanyIds(
            It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, IEnumerable<DecisionEngineSteps>>() { { "Company1", new List<DecisionEngineSteps>() } });
        _mockEventsGenerator.Setup(x => x.GenerateScheduledUpdateEvents(It.IsAny<CompanyModel>(),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()))
            .Returns(new List<ServiceBusMessageBt<ScheduledUpdateEvent>>());

        await _service.Run(default);

        _mockMessageSender.Verify(sender => sender.SendMessages(It.IsAny<IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>>>(), default), Times.Never);
    }

    [Fact]
    public async Task Run_ShouldFilterCompanies_WhenConfigManagerReturnsIds()
    {
        var activeCompanies = new List<CompanyModel>
            {
                new CompanyModel { Id = "Company1", CreatedAt = DateTime.Now },
                new CompanyModel { Id = "Company2", CreatedAt = DateTime.Now }
            };

        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1" }
        };

        var secret = "Company1";

        var scheduledChecks = new List<RefreshCheckConfiguration> { new() };

        _mockCompanyService.Setup(service => service.GetCompaniesByActiveAccounts(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeCompanies);

        _mockCreditApplicationRepo.Setup(repo => repo.GetLightCreditApplications(
            It.IsAny<string[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(approvedCreditApplications);

        _mockConfigService.Setup(manager => manager.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);

        _keyVaultService.Setup(manager => manager.GetSecret(It.IsAny<string>())).ReturnsAsync(secret);

        _mockStepsService.Setup(service => service.GetLatestByCompanyIds(
            It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, IEnumerable<DecisionEngineSteps>>() { { "Company1", new List<DecisionEngineSteps>() } });
        _mockEventsGenerator.Setup(x => x.GenerateScheduledUpdateEvents(It.IsAny<CompanyModel>(),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()))
            .Returns(new List<ServiceBusMessageBt<ScheduledUpdateEvent>>());

        await _service.Run(default);

        _mockEventsGenerator.Verify(x => x.GenerateScheduledUpdateEvents(
            It.Is<CompanyModel>(c => c.Id == "Company1"),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()),
            Times.Once());

        _mockEventsGenerator.Verify(x => x.GenerateScheduledUpdateEvents(
            It.Is<CompanyModel>(c => c.Id == "Company2"),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()),
            Times.Never());
    }

    [Fact]
    public async Task Run_ShouldProcessAllCompanies_WhenNoFilterInConfig()
    {
        var activeCompanies = new List<CompanyModel>
            {
                new CompanyModel { Id = "Company1", CreatedAt = DateTime.Now },
                new CompanyModel { Id = "Company2", CreatedAt = DateTime.Now }
            };

        var approvedCreditApplications = new List<LightCreditApplicationDocument>
        {
            new() { Id = "CreditApp1", CompanyId = "Company1" },
            new() { Id = "CreditApp2", CompanyId = "Company2" }
        };

        var scheduledChecks = new List<RefreshCheckConfiguration> { new() };

        _mockCompanyService.Setup(service => service.GetCompaniesByActiveAccounts(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeCompanies);

        _mockCreditApplicationRepo.Setup(repo => repo.GetLightCreditApplications(
            It.IsAny<string[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(approvedCreditApplications);

        _mockConfigService.Setup(manager => manager.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);

        _keyVaultService.Setup(manager => manager.GetSecret(It.IsAny<string>())).ReturnsAsync(string.Empty);

        _mockStepsService.Setup(service => service.GetLatestByCompanyIds(
            It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, IEnumerable<DecisionEngineSteps>>() { { "Company1", new List<DecisionEngineSteps>() } });
        _mockEventsGenerator.Setup(x => x.GenerateScheduledUpdateEvents(It.IsAny<CompanyModel>(),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()))
            .Returns(new List<ServiceBusMessageBt<ScheduledUpdateEvent>>());

        await _service.Run(default);

        _mockEventsGenerator.Verify(x => x.GenerateScheduledUpdateEvents(
            It.Is<CompanyModel>(c => c.Id == "Company1"),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()),
            Times.Once());

        _mockEventsGenerator.Verify(x => x.GenerateScheduledUpdateEvents(
            It.Is<CompanyModel>(c => c.Id == "Company2"),
            It.IsAny<RefreshCheckConfiguration>(),
            It.IsAny<List<LightCreditApplicationDocument>>(),
            It.IsAny<IReadOnlyList<DecisionEngineSteps>>()),
            Times.Once());
    }
}
