﻿using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Abstractions;

namespace BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Services;

public class InvoiceExternalService(IInvoiceHttpClient invoiceHttpClient) : IInvoiceExternalService
{
    public async Task<InvoiceModel?> GetById(string id, CancellationToken ctx)
    {
        return (await invoiceHttpClient.GetInvoicesByIdsAsync([id], ctx))?.FirstOrDefault();
    }

    public async Task<IEnumerable<InvoiceModel>?> GetByIds(string[] ids, CancellationToken ct)
        => await invoiceHttpClient.GetInvoicesByIdsAsync(ids, ct);

    public async Task<IEnumerable<InvoiceModel>> UpdateInvoicesStatus(IEnumerable<string> ids, InvoiceStatus invoiceStatus, CancellationToken ct)
        => await invoiceHttpClient.PatchInvoicesStatusAsync(new PatchInvoicesStatusModel()
        {
            Ids = ids.ToArray(),
            Status = invoiceStatus
        }, ct);
}
