﻿using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using System.Linq.Expressions;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions
{
    public interface IAccountAuthorizationsRepository
    {
        Task<AccountAuthorizationDocument> Add(AccountAuthorizationDocument entity, CancellationToken ct);
        Task<IEnumerable<AccountAuthorizationDocument>> GetAll(CancellationToken ct);
        Task<IEnumerable<AccountAuthorizationDocument>> GetAll(Expression<Func<AccountAuthorizationDocument, bool>> predicate, CancellationToken ct);
        Task<IEnumerable<AccountAuthorizationDocument>> GetAllByFilters(string? id, string? companyId, string? einHash, string? ssnHash, CancellationToken ct);
        Task<AccountAuthorizationDocument> GetById(string id, CancellationToken ct);
        Task<AccountAuthorizationDocument> Update(AccountAuthorizationDocument entity, CancellationToken ct);
        Task<IEnumerable<AccountAuthorizationDocument>?> GetByEinHashes(string[] einHashes, CancellationToken ct);
        Task<IEnumerable<AccountAuthorizationDocument>> AddRange(IEnumerable<AccountAuthorizationDocument> documents, CancellationToken ct);
        Task<IEnumerable<AccountAuthorizationDocument>?> GetBySsnHashes(string[] ssnHashes, CancellationToken ct);
    }
}
