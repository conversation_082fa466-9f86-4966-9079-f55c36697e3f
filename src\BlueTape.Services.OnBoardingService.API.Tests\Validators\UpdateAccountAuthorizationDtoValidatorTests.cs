﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.UpdateAccountAuthorization;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.API.Tests.Validators;
public class UpdateAccountAuthorizationDtoValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new UpdateAccountAuthorizationDtoValidator();

        var model = new UpdateAccountAuthorizationDto()
        {
            CompanyId = "string",
            EinHash = "string"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }
    [Fact]
    public void Validate_InvalidModel_ReturnsFalse()
    {
        var validator = new UpdateAccountAuthorizationDtoValidator();

        var model = new UpdateAccountAuthorizationDto()
        {
            CompanyId = "string"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }
}
