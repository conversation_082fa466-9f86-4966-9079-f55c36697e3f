﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
public class OwnersEntitiesDetailsDocument : OwnerDetailsBaseDocument
{
    [BsonElement("authRepLastName")]
    public string AuthRepLastName = string.Empty;

    [BsonElement("authRepFirstName")]
    public string AuthRepFirstName = string.Empty;

    [BsonElement("einHash")]
    public string EinHash = string.Empty;

    [BsonElement("entityName")]
    public string EntityName = string.Empty;

    [BsonElement("LastEINRejectionDate")]
    public DateTime? LastEINRejectionDate { get; set; }

    [BsonElement("BRICodes")]
    public IEnumerable<string?> BRICodes { get; set; } = Enumerable.Empty<string?>();

    [BsonElement("BVI")]
    public string? BVI { get; set; }

    [BsonElement("businessOutstandingBalance")]
    public decimal? BusinessOutstandingBalance { get; set; }

    [BsonElement("DBT60PlusAmount")]
    public decimal? DBT60PlusAmount { get; set; }

    [BsonElement("DBT60PlusPercentage")]
    public decimal? DBT60PlusPercentage { get; set; }

    [BsonElement("FirstReportedTradeLineDate")]
    public DateTime? FirstReportedTradeLineDate { get; set; }

    [BsonElement("JudgmentIndicator")]
    public bool? JudgmentIndicator { get; set; }

    [BsonElement("LastJudgmentDate")]
    public DateTime? LastJudgmentDate { get; set; }

    [BsonElement("JudgmentBalance")]
    public decimal? JudgmentBalance { get; set; }

    [BsonElement("BankruptcyIndicator")]
    public bool? BankruptcyIndicator { get; set; }

    [BsonElement("LastBankruptcyDate")]
    public DateTime? LastBankruptcyDate { get; set; }

    [BsonElement("LienIndicator")]
    public bool? LienIndicator { get; set; }

    [BsonElement("LastLienDate")]
    public DateTime? LastLienDate { get; set; }

    [BsonElement("LienBalance")]
    public decimal? LienBalance { get; set; }

    [BsonElement("TradeLinesPercentage")]
    public decimal? TradeLinesPercentage { get; set; }

    [BsonElement("TotalTradeLines")]
    public decimal? TotalTradeLines { get; set; }

    [BsonElement("ReliabilityCode")]
    public string? ReliabilityCode { get; set; }

}