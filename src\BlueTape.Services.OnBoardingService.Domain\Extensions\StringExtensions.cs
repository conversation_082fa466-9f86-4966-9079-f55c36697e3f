﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Domain.Extensions;

public static class StringExtensions
{
    public static bool IsEnum<TEnum>(this TEnum enumValue, string? stringValue) where TEnum : struct, Enum
    {
        return string.Equals(enumValue.ToString(), stringValue, StringComparison.InvariantCultureIgnoreCase);
    }

    public static CreditApplicationType GetCreditApplicationTypeByShortDefinition(this string shortDefinition)
    {
        if (string.Equals(shortDefinition, "loc", StringComparison.InvariantCultureIgnoreCase))
            return CreditApplicationType.LineOfCredit;
        if (string.Equals(shortDefinition, "aradvance", StringComparison.InvariantCultureIgnoreCase))
            return CreditApplicationType.ARAdvance;
        if (string.Equals(shortDefinition, "ihc", StringComparison.InvariantCultureIgnoreCase))
            return CreditApplicationType.InHouseCredit;
        if (string.Equals(shortDefinition, "getPaid", StringComparison.InvariantCultureIgnoreCase))
            return CreditApplicationType.GetPaid;

        throw new ArgumentException(nameof(shortDefinition), $"Invalid short credit application type definition: {shortDefinition}");
    }
}
