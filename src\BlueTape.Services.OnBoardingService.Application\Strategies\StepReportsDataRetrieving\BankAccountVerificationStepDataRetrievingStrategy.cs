﻿using BlueTape.MongoDB.Constants;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;

public class BankAccountVerificationStepDataRetrievingStrategy : StepDataRetrievingStrategyBase
{
    public BankAccountVerificationStepDataRetrievingStrategy(IDecisionEngineStepsRepository decisionEngineStepsRepository,
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService)
        : base(decisionEngineStepsRepository, decisionEngineStepsBviResultsService, creditApplicationAuthorizationDetailsService)
    {
    }

    protected override StepName StepName => StepName.BankAccountVerification;

    protected override Task<BviResultModel[]> GetBviResults(string? stepId, string? companyId, AccountAuthorization? accountAuthorizationDetails, CancellationToken ct)
    {
        var accountAuthBanksDetails = accountAuthorizationDetails?.BankAccountDetails;

        var bviResult = new
        {
            BankAccountsAuthDetails = accountAuthBanksDetails?.Select(bankAccount =>
            new
            {
                bankAccount.Id,
                bankAccount.PersonalNameScore,
                bankAccount.PersonalAddressScore,
                bankAccount.CompanyNameScore,
                bankAccount.CompanyAddressScore,
                bankAccount.NameScore,
                bankAccount.AddressScore,
                bankAccount.Type
            })
        };

        return Task.FromResult(new BviResultModel[]
        {
            new()
            {
                IntegrationSource = IntegrationServicesNamesConstants.Plaid,
                ResponseJsonString = bviResult.ToFormattedJsonString()
    }
        });
    }
}
