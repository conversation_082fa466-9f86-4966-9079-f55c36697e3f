﻿using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Process;
public class ManualDataService : IManualDataService
{
    private readonly ICompanyExternalService _companyExternalService;

    public ManualDataService(ICompanyExternalService companyExternalService, IDraftRepository draftRepository)
    {
        _companyExternalService = companyExternalService;
    }

    public async Task<IEnumerable<CashFlowItemResponse>> LoanApplicationManualDataStep(LoanApplicationDocument application, CancellationToken cancellationToken)
    {
        if (application == null)
            throw new Exception("Loan Application is not found");

        var companyId = application.CompanyId;

        var query = new AssetReportQuery()
        {
            From = DateTime.Today.AddYears(-2),
            To = DateTime.Today,
            Grouping = CashFlowGrouping.Monthly
        };

        return await _companyExternalService.GetPlaidAssetReport(companyId, query, cancellationToken);
    }
}
