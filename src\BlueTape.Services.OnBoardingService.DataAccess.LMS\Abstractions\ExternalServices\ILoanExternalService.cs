﻿using BlueTape.LS.DTOs.AuthorizationPeriods;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Loan;

namespace BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;

public interface ILoanExternalService
{
    Task<CreditDto?> CreateCredit(CreateCreditDto requestDto, CancellationToken ct);
    Task<LoanDto> GetById(Guid id, bool detailed, CancellationToken ct);
    Task<AuthorizationPeriodDto> CreateCreditHold(CreateAuthorizationPeriodDto dto, CancellationToken ct);
    Task<IList<CreditDto?>> GetCreditsByFilters(CreditFilterDto dto, CancellationToken ct);
    Task<CreditDto> GetCreditById(Guid id, bool? detailed, CancellationToken ct);
    Task<List<LoanDto>> FindLoans(LoanQueryDto filter, CancellationToken ct);
}
