﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Models;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using MongoDB.Bson;
using Newtonsoft.Json;
using System.Dynamic;
using System.Globalization;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Decision;
public class DecisionService : IDecisionService
{
    private readonly ISettingsRepository _settingsRepository;
    private readonly IInvoiceExternalService _invoiceExternalService;
    public DecisionService(ISettingsRepository settingsRepository, IInvoiceExternalService invoiceExternalService)
    {
        _settingsRepository = settingsRepository;
        _invoiceExternalService = invoiceExternalService;
    }

    public async Task<LoanDecisionResult> LoanDecision(LoanApplicationDocument application, CancellationToken cancellationToken)
    {
        var data = application.Outputs.FirstOrDefault(o =>
            new[] { "ProcessManualData", "ProcessFinicityData", "ProcessPlaidData" }.Contains(o.Step));

        var cashFlowList = new List<Dictionary<string, object>>();

        if (data?.Data is IDictionary<string, object> dataDictionary)
        {
            if (dataDictionary.TryGetValue("cashFlow", out var cashFlowObject))
            {

                string json = JsonConvert.SerializeObject(cashFlowObject);
                cashFlowList = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(json);
            }
        }

        var minAmountSetting = await _settingsRepository.GetSetting("de_min_approval_amount", cancellationToken);
        var minAmount = minAmountSetting?.Value ?? 0;

        var debtAdjustorSetting = await _settingsRepository.GetSetting("de_debt_adjustor", cancellationToken);
        var debtAdjustor = debtAdjustorSetting?.Value ?? 1.2;

        var acceptableDebtOfRevenueSetting = await _settingsRepository.GetSetting("de_acceptable_debt_of_revenue", cancellationToken);
        var acceptableDebtOfRevenue = acceptableDebtOfRevenueSetting?.Value ?? 15;

        object? businessInfoStartDate = application.Draft?.GetValueOrDefault("businessInfo_startDate") ?? null;

        double annualRevenue = 0;

        if (cashFlowList != null && cashFlowList.Count > 1)
        {
            var lastEntry = cashFlowList.Last();
            var firstEntry = cashFlowList.First();

            if (lastEntry != null && firstEntry != null)
            {
                var startDate = DateTime.ParseExact(lastEntry.GetValueOrDefault("date").ToString(), "yyyy-MM-dd", CultureInfo.InvariantCulture);
                var endDate = DateTime.ParseExact(firstEntry.GetValueOrDefault("date").ToString(), "yyyy-MM-dd", CultureInfo.InvariantCulture).AddMonths(1);

                double totalCredits = cashFlowList.Sum(cf => cf.TryGetValue("credit", out var creditObj) ? Convert.ToDouble(creditObj) : 0);

                int monthsDifference = (endDate.Year - startDate.Year) * 12 + endDate.Month - startDate.Month;
                annualRevenue = Math.Round(12 / (double)monthsDifference * totalCredits, 2);
            }
        }

        object? financeRevenue = null;
        application.Draft?.TryGetValue("finance_revenue", out financeRevenue);

        object? financeDebt = null;
        application.Draft?.TryGetValue("finance_debt", out financeDebt);

        var revenueEstProvidedInApp = double.TryParse(financeRevenue?.ToString(), out var revenue) ? revenue : 0;
        var debtEstimateProvidedInApp = double.TryParse(financeDebt?.ToString(), out var debt) ? debt : 0;

        var creditStatus = application.Outputs.FirstOrDefault(o => o.Step == "creditStatus");

        object? businessDataObj = null;
        var creditStatusData = creditStatus?.Data as ExpandoObject;

        if (creditStatusData != null)
        {
            var creditStatusDataDict = (IDictionary<string, object>)creditStatusData;

            if (creditStatusDataDict.ContainsKey("businessData"))
            {
                var businessData = creditStatusDataDict["businessData"] as List<object>;

                if (businessData != null && businessData.Count > 0)
                {
                    businessDataObj = businessData[0] as IDictionary<string, object>;
                }
            }
        }

        var debtExperian = 0;
        if (businessDataObj is ExpandoObject expandoData)
        {
            var businessDataDict = (IDictionary<string, object>)expandoData;

            if (businessDataDict.TryGetValue("owner", out var ownerObj))
            {
                var businessDataOwner = ownerObj as IDictionary<string, object>;

                businessDataOwner.TryGetValue("accountBalanceDebt", out var debtExperianObj);

                if (debtExperianObj != null)
                {
                    debtExperian = Convert.ToInt32(debtExperianObj);
                }
            }
        }

        var loanRevenue = Math.Min(annualRevenue, revenueEstProvidedInApp);
        var debtAmount = Math.Round(debtEstimateProvidedInApp * Convert.ToDouble(debtAdjustor), 2);
        var loanDebt = Math.Max(debtAmount, debtExperian);

        var totalAcceptableDebtAmount = Math.Round(loanRevenue * Convert.ToDouble(acceptableDebtOfRevenue) / 100, 2);

        var loanInfo = application.Outputs.FirstOrDefault(i => i.Step == "BlueTape");


        object? outstandingBalance = null;

        if (loanInfo?.Data is BsonDocument bsonData)
        {
            var infoBson = bsonData.GetElement("info").Value.AsBsonDocument;
            if (infoBson.TryGetValue("BusinessOutstandingBalance", out var businessOutstandingBalance))
            {
                if (double.TryParse(businessOutstandingBalance.ToString(), out var decimalValue))
                {
                    outstandingBalance = decimalValue;
                }
            }
        }

        var availableCreditLimit = Math.Floor(Math.Floor((totalAcceptableDebtAmount - loanDebt) / 1000) * 1000) - Convert.ToDouble(outstandingBalance);

        double approvedAmount = availableCreditLimit;

        var loanApproved = approvedAmount >= Convert.ToDouble(minAmount);

        object? invoiceId = null;

        if (application.InvoiceDetails is IDictionary<string, object> invoiceDetails)
        {
            if (invoiceDetails.TryGetValue("invoiceId", out var invoiceIdObject) && invoiceIdObject is IEnumerable<object> invoiceIdList)
            {
                invoiceId = invoiceIdList.Select(id => id.ToString()).ToList();
            }
        }

        if (invoiceId != null)
        {
            IEnumerable<string> invoiceIds;

            if (invoiceId is IEnumerable<string> ids)
            {
                invoiceIds = ids;
            }
            else
            {
                invoiceIds = new List<string> { invoiceId.ToString() };
            }

            decimal invoiceTotalAmount = 0;

            foreach (var id in invoiceIds)
            {
                var invoice = await _invoiceExternalService.GetById(id, cancellationToken);
                if (invoice != null)
                {
                    invoiceTotalAmount += invoice.TotalAmount;
                }
            }

            var invoiceAmount = Convert.ToDecimal(availableCreditLimit);

            if (invoiceTotalAmount > 0) invoiceAmount = invoiceTotalAmount;
            approvedAmount = Math.Min(Convert.ToDouble(invoiceAmount), availableCreditLimit);
            loanApproved = Convert.ToDouble(invoiceAmount) <= availableCreditLimit && approvedAmount >= Convert.ToDouble(minAmount);
        }

        var decisions = new Dictionary<string, object>
        {
            { "business_outstanding_balance", outstandingBalance ?? 0},
            { "min_amount", Convert.ToDouble(minAmount)},
            { "approved_amount", Math.Round(approvedAmount, 2) },
            { "loan_revenue", loanRevenue },
            { "annual_revenue", annualRevenue },
            { "loan_debt", loanDebt },
            { "debt_amount", debtAmount },
            { "total_acceptable_debt_amount", totalAcceptableDebtAmount },
            { "available_credit_limit", availableCreditLimit },
            { "debt_adjustor", debtAdjustor },
            { "acceptable_debt_of_revenue", Convert.ToDouble(acceptableDebtOfRevenue) },
            { "revenue_est_provided_in_app", revenueEstProvidedInApp },
            { "debt_estimate_provided_in_app", debtEstimateProvidedInApp },
            { "debt_experian", Convert.ToDouble(debtExperian) },
            { "loanApproved", loanApproved },
            { "businessAge", businessInfoStartDate ?? "null" }
        };

        return new LoanDecisionResult
        {
            LoanApproved = loanApproved,
            Decisions = decisions
        };
    }
}
