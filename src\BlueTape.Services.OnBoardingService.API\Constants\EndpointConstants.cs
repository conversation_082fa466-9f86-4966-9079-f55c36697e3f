﻿namespace BlueTape.Services.OnBoardingService.API.Constants
{
    internal static class EndpointConstants
    {
        public const string Id = "{id}";
        public const string LastExecutions = "LastExecutions";
        public const string CreditApplicationId = "{creditApplicationId}";
        public const string CreditApplication = "CreditApplication";
        public const string Execution = "Execution";
        public const string DrawApproval = "DrawApproval";
        public const string DrawApprovals = "DrawApprovals";
        public const string Report = "Report";
        public const string Notes = "Notes";
        public const string Submit = "Submit";
        public const string Execute = "Execute";
        public const string InitializationStep = "InitializationStep";
        public const string ReviewCreditApplication = "creditApplications/{creditApplicationId}";
        public const string UpdateFinalDrawApprovalStatus = "drawapprovals/{id}/status";
        public const string Bvi = "BVI";
        public const string LexisNexis = "LexisNexis";
        public const string Experian = "Experian";
        public const string Giact = "Giact";
        public const string Invoices = "Invoices";
        public const string InvoicesIds = "Invoices/ids";
        public const string PaymentPlan = "paymentplan";
        public const string QuoteExpiration = "quoteexpiration";
        public const string PostTransaction = "posttransaction";
        public const string Invoice = "invoice";
        public const string NoteId = "{noteId}";
        public const string LocalHost = "localhost";
        public const string Suffix = "/onBoarding";
        public const string GetByEinList = "getByEinList";
        public const string GetBySsnList = "getBySsnList";
        public const string AuthorizationPeriod = "AuthorizationPeriod";
        public const string Ids = "ids";
        public const string Company = "Company";
        public const string Companies = "Companies";
        public const string Draft = "Draft";
    }
}