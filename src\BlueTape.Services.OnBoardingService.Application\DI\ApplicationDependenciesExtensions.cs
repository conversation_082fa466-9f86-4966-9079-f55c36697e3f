﻿using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.Runtime;
using Amazon.StepFunctions;
using BlueTape.AWSS3.Extensions;
using BlueTape.AWSStepFunction.Configuration;
using BlueTape.AWSStepFunction.Extensions;
using BlueTape.CompanyService.Common.Senders;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Reports;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Senders;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Services.Reports;
using BlueTape.Services.OnBoardingService.Application.Services.Senders;
using BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.DI;
using BlueTape.Services.OnBoardingService.DataAccess.DI;
using BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.DI;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Extensions;
using BlueTape.Services.OnBoardingService.Infrastructure.Options;
using BlueTape.SNS.SlackNotification.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace BlueTape.Services.OnBoardingService.Application.DI
{
    public static class ApplicationDependenciesExtensions
    {
        public static void AddApplicationDependencies(this IServiceCollection services, IConfiguration configurationManager)
        {
            AddBusinessLogicServices(services);
            AddReportsDataRetrievingStrategies(services);

            ConfigureAutoMapper(services);
            ConfigureOptions(services, configurationManager);
            AddReportServices(services);
            AddExternalServices(services, configurationManager);
            AddStepFunctions(services, configurationManager);
            AddChangeCreditApplicationStatusStrategies(services);

            services.AddSnsSlackNotifications();
            services.AddTransient<ISlackNotificationService, SlackNotificationService>();
            services.Configure<SlackNotificationOptions>(configurationManager.GetSection(SlackNotificationOptions.SectionName));
            services.AddDataAccessDependencies(configurationManager);
        }
        private static void AddBusinessLogicServices(IServiceCollection services)
        {
            services.AddScoped<IDraftService, DraftService>();
            services.AddTransient<IDateProvider, DateProvider>();
            services.AddScoped<ICreditApplicationService, CreditApplicationService>();
            services.AddScoped<IAccountAuthorizationsService, AccountAuthorizationsService>();
            services.AddScoped<IDecisionEngineStepsService, DecisionEngineStepsService>();
            services.AddScoped<IAccountAuthorizationDetailsChangesService, AccountAuthorizationDetailsChangesService>();
            services.AddScoped<IDecisionEngineStepsBviResultsService, DecisionEngineStepsBviResultsService>();
            services.AddScoped<ICreditApplicationNotesService, CreditApplicationNotesService>();
            services.AddScoped<ICreditApplicationAuthorizationDetailsService, CreditApplicationAuthorizationDetailsService>();
            services.AddScoped<ILoanService, LoanService>();
            services.AddScoped<IDrawApprovalService, DrawApprovalService>();
            services.AddScoped<ICompanyService, Services.CompanyService>();
            services.AddScoped<IDrawApprovalNotesService, DrawApprovalNotesService>();
            services.AddScoped<ILinqPalInteractionService, LinqPalInteractionService>();
            services.AddScoped<IDecisionEngineExecutionService, DecisionEngineExecutionService>();
            services.AddScoped<ILoanPaymentPlanService, LoanPaymentPlanService>();
            services.AddScoped<IAccountStatusService, AccountStatusService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ICustomerAccountsService, CustomerAccountsService>();
            services.AddScoped<IAccountStatusChangeQueueSender, AccountStatusChangeQueueSender>();
            services.AddScoped<ILoanApplicationSyncMessageSender, LoanApplicationSyncMessageSender>();
            services.AddScoped<IInvoiceSyncMessageSender, InvoiceSyncMessageSender>();
            services.AddScoped<IMigrationService, MigrationService>();
            services.AddScoped<ICreditApplicationExecutionService, CreditApplicationExecutionService>();
            services.AddScoped<ICreditApplicationSyncService, CreditApplicationSyncService>();
            services.AddScoped<IParsedDraftService, ParsedDraftService>();
            services.AddScoped<IAuthorizationDetailsRefreshConfigurationService, AuthorizationDetailsRefreshConfigurationService>();
        }

        private static void ConfigureOptions(IServiceCollection services, IConfiguration configurationManager)
        {
            services.Configure<StepsReportOptions>(configurationManager.GetSection(nameof(StepsReportOptions)));
        }

        private static void AddExternalServices(IServiceCollection services, IConfiguration configurationManager)
        {
            services.AddLMSExternalServiceDependencies(configurationManager);
            services.AddInvoiceExternalServiceDependencies(configurationManager);
            services.AddCompanyExternalServiceDependencies(configurationManager);
        }

        public static void AddStepFunctions(this IServiceCollection services, IConfiguration configuration)
        {
            ArgumentNullException.ThrowIfNull(configuration);

            var section = configuration.GetSection(nameof(InitializationStepOptions));
            var accessKeyId = configuration[ConfigurationConstants.AWSAccessKey] ?? throw new ArgumentException("AWSAccessKey not configured");
            var secretKey = configuration[ConfigurationConstants.AWSSecretKey] ?? throw new ArgumentException("AWSSecretKey not configured");
            var defaultRegion = configuration[ConfigurationConstants.AWSDefaultRegion] ?? throw new ArgumentException("AWSDefaultRegion not configured");

            var awsOptions = new AWSOptions
            {
                Region = RegionEndpoint.GetBySystemName(defaultRegion),
                Credentials = new BasicAWSCredentials(accessKeyId, secretKey),
            };
            services.AddAWSService<IAmazonStepFunctions>(awsOptions);

            var accountId = configuration[ConfigurationConstants.AWSAccountId] ?? throw new ArgumentException("AWSAccountId not configured");

            services.Configure<InitializationStepOptions>(section);

            services.AddStepFunctionsClient<CreditApplicationInitializationStepStartRequest, InitializationStepOptions>
            (options => new AwsStepFunctionClientConfiguration()
            {
                StepMachineName = options.CreditApplicationsStateMachineName,
                AwsAccountId = accountId,
                AwsRegion = defaultRegion
            });

            services.AddStepFunctionsClient<DrawApprovalInitializationStepStartRequest, InitializationStepOptions>
            (options => new AwsStepFunctionClientConfiguration()
            {
                StepMachineName = options.DrawApprovalsStateMachineName,
                AwsAccountId = accountId,
                AwsRegion = defaultRegion
            });
        }

        private static void AddReportServices(IServiceCollection services)
        {
            services.AddScoped<IReportsService, ReportsService>();
            services.AddScoped<IExcelService, ExcelService>();
            services.AddS3Client();
        }
        private static void ConfigureAutoMapper(IServiceCollection services)
        {
            services.AddAutoMapper(typeof(ModelsProfile).GetTypeInfo().Assembly);
        }

        private static void AddReportsDataRetrievingStrategies(IServiceCollection services)
        {
            services.AddScoped<StepDataRetrievingStrategyBase, InitializationStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, PreliminaryStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, KybStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, KycStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, CreditRatingBusinessStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, CreditRatingCoOwnersStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, BankAccountVerificationStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, BankStatementValidationStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, CashFlowStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, AffordabilityAssessmentStepDataRetrievingStrategy>();
            services.AddScoped<StepDataRetrievingStrategyBase, BlueTapeStepDataRetrievingStrategy>();
        }

        private static void AddChangeCreditApplicationStatusStrategies(IServiceCollection services)
        {
            services.AddScoped<ChangeCreditApplicationStatusStrategy, ApproveCreditApplicationStrategy>();
            services.AddScoped<ChangeCreditApplicationStatusStrategy, CancelCreditApplicationStrategy>();
            services.AddScoped<ChangeCreditApplicationStatusStrategy, RejectCreditApplicationStrategy>();
            services.AddScoped<ChangeCreditApplicationStatusStrategy, SentBackCreditApplicationStrategy>();
        }
    }
}
