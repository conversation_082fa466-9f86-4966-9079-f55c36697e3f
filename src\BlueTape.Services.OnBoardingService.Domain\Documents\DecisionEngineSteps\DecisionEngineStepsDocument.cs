﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;

[BsonIgnoreExtraElements]
[MongoCollection("decisionEngineSteps")]
public class DecisionEngineStepsDocument : Document
{
    [BsonElement("executionId")]
    public string? ExecutionId { get; set; }

    [BsonElement("executionType")]
    public string? ExecutionType { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("accountAuthorizationDetailsId")]
    public string? AccountAuthorizationDetailsId { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("drawApprovalId")]
    public string? DrawApprovalId { get; set; }

    [BsonElement("step")]
    public string? Step { get; set; }

    [BsonElement("previousStep")]
    public string? PreviousStep { get; set; }

    [BsonElement("policyVersion")]
    public string? PolicyVersion { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("results")]
    public IEnumerable<DecisionEngineStepResultDocument>? Results { get; set; }

    [BsonElement("thresholds")]
    public IEnumerable<DecisionEngineThresholdDocument>? Thresholds { get; set; }

    [BsonElement("updatedBy")]
    public string? UpdatedBy { get; set; }

    [BsonElement("createdBy")]
    public string? CreatedBy { get; set; }

    [BsonElement("manualStatus")]
    public string? ManualStatus { get; set; }

    [BsonElement("manuallyOverridedBy")]
    public string? ManuallyOverridedBy { get; set; }

    [BsonElement("manuallyOverridedAt")]
    public DateTime? ManuallyOverridedAt { get; set; }

    [BsonElement("manuallyOverridedReason")]
    public string? ManuallyOverridedReason { get; set; }

}
