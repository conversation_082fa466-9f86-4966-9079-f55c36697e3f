﻿using Newtonsoft.Json;

namespace BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;

public class BankDetailsModel
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("accountholderName")]
    public string? AccountHolderName { get; set; }

    [JsonProperty("routingNumber")]
    public string? RoutingNumber { get; set; }

    [JsonProperty("accountNumber")]
    public string? AccountNumber { get; set; }

    [JsonProperty("accountName")]
    public string? AccountName { get; set; }

    [JsonProperty("paymentMethodType")]
    public string? PaymentMethodType { get; set; }

    [JsonProperty("isPrimary")]
    public bool IsPrimary { get; set; } = false;

    [JsonProperty("accountType")]
    public string? AccountType { get; set; }
}
