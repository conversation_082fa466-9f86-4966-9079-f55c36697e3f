using Azure.Messaging.ServiceBus;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Functions.AuthorizationDetailsRefreshService;

[ExcludeFromCodeCoverage]
public class AuthorizationDetailsRefreshService(
    ILogger<AuthorizationDetailsRefreshService> logger,
    IAuthorizationDetailsRefreshService refreshService,
    ISlackNotificationService notificationService,
    ITraceIdAccessor traceIdAccessor)
{
    [Function(nameof(AuthorizationDetailsRefreshService))]
    public async Task Run(
        [ServiceBusTrigger(
            $"%{InfrastructureConstants.RefreshServiceQueueName}%",
            Connection = $"{InfrastructureConstants.RefreshServiceQueueConnectionString}")]
            ServiceBusReceivedMessage message,
        ServiceBusMessageActions messageActions, CancellationToken ct)
    {
        traceIdAccessor.TraceId = message.CorrelationId;

        using (GlobalLogContext.PushProperty("functionName", nameof(AuthorizationDetailsRefreshService)))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", traceIdAccessor.TraceId))
        {
            logger.LogInformation("Decision Engine Authorization Details Service: got message from the queue: Session: {sessionId}, \n Message: {messageId}. Body: {@message}", message.SessionId, message.MessageId, message.Body);

            try
            {
                var parsedMessage = message.Body.ToObjectFromJson<ScheduledUpdateEvent?>();

                if (parsedMessage is null)
                {
                    logger.LogError(
                        "Decision Engine Authorization Details Service: unable to process message with empty body or invalid fields. Session: {sessionId}, \n Message: {messageId}",
                        message.SessionId, message.MessageId);
                    throw new ArgumentNullException(nameof(message));
                }

                logger.LogInformation("Decision Engine Authorization Details Service: started handling for creditApplicationId: {creditApplicationId}, type: {scheduledUpdateType}",
                    parsedMessage.Details.ApprovedCreditApplicationId, parsedMessage.EventType);

                await refreshService.Process(parsedMessage, ct);

                logger.LogInformation("Decision Engine Authorization Details Service: successfully processed creditApplicationId: {creditApplicationId}, type: {scheduledUpdateType}",
                    parsedMessage.Details.ApprovedCreditApplicationId, parsedMessage.EventType);

            }
            catch (Exception ex)
            {
                logger.LogError("Decision Engine Authorization Details Service: got exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "OnBoardingService"), traceIdAccessor.TraceId, ct);
                throw;
            }
        }
    }
}

