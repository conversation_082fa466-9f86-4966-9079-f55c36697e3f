﻿using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Infrastructure.Extensions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace BlueTape.Services.OnBoardingService.API.Handlers
{
    public class ApiKeyHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        private readonly IKeyVaultService _keyVaultService;
        private readonly ILogger<ApiKeyHandler> _logger;

        public ApiKeyHandler(
            IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            IKeyVaultService keyVaultService
        ) : base(options, logger, encoder)
        {
            _keyVaultService = keyVaultService;
            _logger = logger.CreateLogger<ApiKeyHandler>();
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var principal = new ClaimsPrincipal(new ClaimsIdentity(Enumerable.Empty<Claim>(), AuthenticationConstants.ApiKeyAuthScheme));
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            if (!EnvironmentExtensions.IsProduction()) return AuthenticateResult.Success(ticket);
            if (!Request.Headers.TryGetValue(AuthenticationConstants.ApiKeyHeader, out var extractedApiKey))
            {
                return AuthenticateResult.NoResult();
            }

            var apiKey = await _keyVaultService.GetSecret(AuthenticationConstants.OnBoardingServiceApiKey);

            if (!apiKey.Equals(extractedApiKey))
            {
                _logger.LogWarning("Authentication failed");
                return AuthenticateResult.Fail("Authentication failed");
            }

            _logger.LogInformation("{extractedApiKey} got access to {method}: {path}",
                extractedApiKey.ToString(),
                Request.Method,
                Request.Path.Value);

            return AuthenticateResult.Success(ticket);
        }
    }
}
