﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.Configuration
{
    [BsonIgnoreExtraElements]
    [MongoCollection("authorizationDetailsRefreshConfiguration")]
    public class AuthorizationDetailsRefreshConfigurationDocument : Document
    {
        [BsonElement("checks")]
        public IReadOnlyList<AuthorizationDetailsRefreshCheckDocument> Checks { get; set; } = new List<AuthorizationDetailsRefreshCheckDocument>();
    }
}
