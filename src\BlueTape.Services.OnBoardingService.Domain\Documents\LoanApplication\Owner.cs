﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class Owner
{
    [BsonElement("id")]
    public string? Id { get; set; }

    [BsonElement("businessName")]
    public string? BusinessName { get; set; }

    [BsonElement("firstName")]
    public string? FirstName { get; set; }

    [BsonElement("lastName")]
    public string? LastName { get; set; }

    [BsonElement("type")]
    public string? Type { get; set; }

    [BsonElement("isPrincipal")]
    public bool? IsPrincipal { get; set; }

    //[BsonElement("key")]
    //public string? Key { get; set; }

    [BsonElement("identifier")]
    public string? Identifier { get; set; }
}
