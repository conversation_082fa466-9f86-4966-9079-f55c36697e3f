﻿using BlueTape.OBS.Enums;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class PayableItemDocument
{
    [BsonElement("id")]
    public string Id { get; set; } = string.Empty;

    [BsonElement("type")]
    public string Type { get; set; } = string.Empty;

    [BsonElement("invoiceNumber")]
    public string? InvoiceNumber { get; set; }
    
    [BsonElement("amount")]
    public decimal Amount { get; set; }
    
    
    // Not stored in the database, will be evaluated during query execution
    [BsonElement("status")]
    [BsonIgnoreIfNull]
    public string? Status { get; set; }
    
    [BsonElement("invoiceDueDate")]
    [BsonIgnoreIfNull]
    public DateTime? InvoiceDueDate { get; set; }
}
