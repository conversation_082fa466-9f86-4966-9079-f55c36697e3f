﻿namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;

public class DecisionEngineSteps
{
    public string? Id { get; set; }

    public string? ExecutionType { get; set; }

    public string? ExecutionId { get; set; }

    public string? CreditApplicationId { get; set; }
    
    public string? DrawApprovalId { get; set; }

    public string? AccountAuthorizationDetailsId { get; set; }

    public string? Step { get; set; }

    public string? PreviousStep { get; set; }

    public string? PolicyVersion { get; set; }

    public string? Status { get; set; }

    public IEnumerable<DecisionEngineStepResult>? Results { get; set; }

    public IEnumerable<DecisionEngineStepThresholdModel>? Thresholds { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string? ManualStatus { get; set; }

    public string? ManuallyOverridedBy { get; set; }

    public DateTime? ManuallyOverridedAt { get; set; }

    public string? ManuallyOverridedReason { get; set; }
}
