﻿using BlueTape.OBS.DTOs.DecisionEngineSteps;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.DecisionEngineSteps.UpdateDecisionEngineSteps;

public class UpdateDecisionEngineStepsDtoValidator : AbstractValidator<UpdateDecisionEngineStepsDto>
{
    public UpdateDecisionEngineStepsDtoValidator()
    {
        RuleFor(x => x.NewStatus).NotEmpty().NotNull();
        RuleFor(x => x.Results).NotNull();
        RuleFor(x => x.UpdatedBy).NotNull();
    }
}
