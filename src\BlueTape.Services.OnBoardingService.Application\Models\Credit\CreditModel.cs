﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.Credit;

public class CreditModel
{
    public Guid Id { get; set; }

    public string CompanyId { get; set; } = string.Empty;

    public string CreditApplicationId { get; set; } = string.Empty;

    public string? ProjectId { get; set; }

    public DateOnly StartDate { get; set; }

    public DateOnly? CloseDate { get; set; }

    public int CreditLimit { get; set; }

    public string Currency { get; set; } = string.Empty;

    public CreditStatus Status { get; set; }
    
    public string StatusEvent { get; set; } = string.Empty;

    public bool IsCreditStatusManuallySet { get; set; }

    public string? ManualStatusBy { get; set; } = string.Empty;

    public DateTime? ManualStatusAt { get; set; }

    public string? ManualNote { get; set; }

    public PurchaseTypeOption PurchaseType { get; set; }
}
