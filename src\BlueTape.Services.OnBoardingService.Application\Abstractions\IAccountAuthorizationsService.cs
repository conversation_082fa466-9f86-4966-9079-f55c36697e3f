﻿using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using System.Linq.Expressions;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IAccountAuthorizationsService
{
    Task<IEnumerable<AccountAuthorization>> GetAllByFilters(string? id, string? companyId, string? einHash, string? ssnHash, CancellationToken ct);

    Task<IEnumerable<AccountAuthorization>> GetAll(CancellationToken ct);
    Task<IEnumerable<AccountAuthorization>> GetAll(Expression<Func<AccountAuthorizationDocument, bool>> predicate, CancellationToken ct);
    Task<AccountAuthorization> Create(CreateAccountAuthorization model, CancellationToken ct);

    Task<IEnumerable<AccountAuthorization>> CreateRange(IEnumerable<CreateAccountAuthorization> models, CancellationToken ct);
    Task<AccountAuthorization> GetById(string id, CancellationToken ct);

    Task<AccountAuthorization> Update(UpdateAccountAuthorization model, CancellationToken ct);
    Task<AccountAuthorization> Patch(PatchAccountAuthorization model, CancellationToken ct);
    Task RejectOwners(string companyId, string creditApplicationId, string userId, CancellationToken ct);
    Task<AccountAuthorization> Nullify(string id, NullifyAccountAuthorization model, CancellationToken ct);
    Task<IEnumerable<AccountAuthorization>> GetByEinList(string[] einHashes, CancellationToken ct);
    Task<IEnumerable<AccountAuthorization>> GetBySsnList(string[] ssnHashes, CancellationToken ct);
}