using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApprovalNotes;

[BsonIgnoreExtraElements]
[MongoCollection("drawApprovalNotes")]
public class DrawApprovalNoteDocument: Document
{
    [BsonElement("drawApprovalId")]
    public string DrawApprovalId { get; set; } = string.Empty;
    
    [BsonElement("note")]
    public string? Note { get; set; } = string.Empty;
    
    [BsonElement("createdBy")]
    public string? CreatedBy { get; set; }
    
    [BsonElement("updatedBy")]
    public string? UpdatedBy { get; set; }
    
    [BsonElement("deletedAt")]
    public DateTime? DeletedAt { get; set; }
    
    [BsonElement("deletedBy")]
    public string? DeletedBy { get; set; }
}