﻿using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class LinqPalInteractionServiceTests
{

    private readonly LinqPalInteractionService _interactionService;

    private readonly Mock<INodeExternalService> _nodeExternalServiceMock = new();
    private readonly Mock<ILoanExternalService> _loanExternalServiceMock = new();
    private readonly Mock<ILogger<LinqPalInteractionService>> _logger = new();

    public LinqPalInteractionServiceTests()
    {
        _interactionService = new LinqPalInteractionService(_nodeExternalServiceMock.Object, _loanExternalServiceMock.Object, _logger.Object);
    }

    [Fact]
    public async Task SendOpsTeamNotification_ValidData_MakesExternalRequest()
    {
        var id = Guid.NewGuid().ToString();

        await _interactionService.SendOpsTeamNotification(new LoanApplicationDocument() { Id = id }, default);

        _nodeExternalServiceMock.Verify(x => x.NotifyOpsTeam(It.Is<OpsTeamNotificationRequest>(request => request.ApplicationId == id), default));
    }

    [Fact]
    public async Task StartIssueLoanProcess_ValidData_MakesExternalRequest()
    {
        var id = Guid.NewGuid().ToString();
        var approvedAmount = 20;

        await _interactionService.StartHumanApprovalProcess(new LoanApplicationDocument() { Id = id },
            new DrawApprovalDocument() { DrawAmount = approvedAmount }, default);

        _nodeExternalServiceMock.Verify(x => x.HumanApproval(It.Is<IssueLoanRequest>(request =>
            request.ApplicationId == id && request.Decision.Status == AdminDrawApprovalStatusUpdate.Approved && request.Decision.ApprovedAmount == approvedAmount), default));
    }

    [Fact]
    public Task SendUserApprovalNotification_LmsLoanDoesNotContainReceivables_ShouldNotThrow()
    {
        var id = Guid.NewGuid().ToString();
        var loanId = Guid.NewGuid();
        var drawApprovalId = Guid.NewGuid().ToString();
        var loan = new LoanDto()
        {
            Id = loanId,
            LoanReceivables = new List<LoanReceivableDto>()
        };
        _loanExternalServiceMock.Setup(x => x.FindLoans(It.Is<LoanQueryDto>(q => q.DrawApprovalId == drawApprovalId), default))
            .ReturnsAsync(new List<LoanDto> { loan });
        var act = async () => await _interactionService.SendUserApprovalNotification(id, drawApprovalId, false, default);

        return act.ShouldNotThrowAsync();
    }

    [Fact]
    public async Task SendUserApprovalNotification_LmsLoanValid_MakesExternalRequest()
    {
        var id = Guid.NewGuid().ToString();
        var loanId = Guid.NewGuid();
        var drawApprovalId = Guid.NewGuid().ToString();
        var firstPaymentDate = new DateOnly(2020, 05, 05);

        var loan = new LoanDto()
        {
            Id = loanId,
            LoanReceivables = new List<LoanReceivableDto>()
            {
                new()
                {
                    ExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow)
                },
                new()
                {
                    ExpectedDate = firstPaymentDate
                }
            }
        };
        _loanExternalServiceMock.Setup(x => x.FindLoans(It.Is<LoanQueryDto>(q => q.DrawApprovalId == drawApprovalId), default))
            .ReturnsAsync(new List<LoanDto> { loan });
        await _interactionService.SendUserApprovalNotification(id, drawApprovalId, false, default);

        _nodeExternalServiceMock.Verify(x => x.TriggerUserApprovalNotification(It.Is<UserApprovalNotificationRequest>(request =>
            request.ApplicationId == id && request.FirstPaymentDate == firstPaymentDate), default));
    }

    [Theory, CustomAutoData]
    public async Task SendDrawApprovalIhcNotification_ApprovalRequest_ShouldSend(DrawApprovalDocument document)
    {
        document.Status = DrawApprovalStatus.Approved.ToString();
        document.Type = DrawApprovalType.Factoring.ToString();
        document.CompanyName = "company Name020 /";

        await _interactionService.SendIhcDrawApprovalRejectedOrApprovedNotification(document, default);

        _nodeExternalServiceMock.Verify(x
            => x.TriggerIhcDrawApprovalCustomerNotification(It.Is<DrawApprovalReviewUserNotification>(x =>
                x.InvoiceNumber == document.Payables.FirstOrDefault()!.InvoiceNumber! &&
                x.Decision == InvoiceDecision.Approved.ToString() &&
                x.MerchantId == document.MerchantId &&
                x.CompanyName == "company Name020" &&
                x.InvoiceAmount == document.DrawAmount
            ), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task SendDrawApprovalIhcNotification_RejectionRequest_ShouldSend(DrawApprovalDocument document)
    {
        document.Status = DrawApprovalStatus.Rejected.ToString();
        document.Type = DrawApprovalType.Factoring.ToString();
        document.CompanyName = "company Name020 /";

        await _interactionService.SendIhcDrawApprovalRejectedOrApprovedNotification(document, default);

        _nodeExternalServiceMock.Verify(x
            => x.TriggerIhcDrawApprovalCustomerNotification(It.Is<DrawApprovalReviewUserNotification>(x =>
                x.InvoiceNumber == document.Payables.FirstOrDefault()!.InvoiceNumber! &&
                x.Decision == InvoiceDecision.Rejected.ToString() &&
                x.MerchantId == document.MerchantId &&
                x.CompanyName == "company Name020" &&
                x.InvoiceAmount == document.DrawAmount
            ), default), Times.Once);
    }


    [Theory, CustomAutoData]
    public async Task SendDrawApprovalIhcNotification_InvalidStatus_ShouldntSendNotification(DrawApprovalDocument document)
    {
        document.Status = DrawApprovalStatus.New.ToString();

        await _interactionService.SendIhcDrawApprovalRejectedOrApprovedNotification(document, default);

        _nodeExternalServiceMock.Verify(x
            => x.TriggerIhcDrawApprovalCustomerNotification(It.IsAny<DrawApprovalReviewUserNotification>(), default), Times.Never);
    }
}
