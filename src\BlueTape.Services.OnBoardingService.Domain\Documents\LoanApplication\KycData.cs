﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;

[BsonIgnoreExtraElements]
public class EmailAge
{
    [BsonElement("emailAgeDomainRiskLevel")]
    public string? EmailAgeDomainRiskLevel { get; set; }

    [BsonElement("emailAgeIpRiskLevel")]
    public string? EmailAgeIpRiskLevel { get; set; }

    [BsonElement("emailAgeEaScoreRiskLevel")]
    public string? EmailAgeEaScoreRiskLevel { get; set; }
}

[BsonIgnoreExtraElements]
public class FraudPoint
{
    [BsonElement("fraudPointScoreRiskLevel")]
    public string? FraudPointScoreRiskLevel { get; set; }
}

[BsonIgnoreExtraElements]
public class Fraud
{
    [BsonElement("owner")]
    public Owner? Owner { get; set; }

    [BsonElement("emailAge")]
    public EmailAge? EmailAge { get; set; }

    [BsonElement("fraudPoint")]
    public FraudPoint? FraudPoint { get; set; }
}

[BsonIgnoreExtraElements]
public class KycData
{
    [BsonElement("fraud")]
    public List<Fraud>? Fraud { get; set; }

    [BsonElement("fraudPoint")]
    public FraudPoint? FraudPoint { get; set; }

    [BsonElement("emailAge")]
    public EmailAge? EmailAge { get; set; }
}
