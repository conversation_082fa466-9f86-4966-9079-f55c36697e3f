﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.UpdateAccountAuthorization;

public class UpdateAccountAuthorizationDtoValidator : AbstractValidator<UpdateAccountAuthorizationDto>
{
    public UpdateAccountAuthorizationDtoValidator()
    {
        RuleFor(x => x.CompanyId).NotEmpty();
        RuleFor(x => x.EinHash).NotEmpty();

        RuleFor(x => x.OwnersDetails).ForEach(x => x.SetValidator(new OwnersDetailsUpdateDtoValidator()));
        RuleFor(x => x.BankAccountDetails).ForEach(x => x.SetValidator(new BankAccountsDetailsUpdateDtoValidator()));
    }
}