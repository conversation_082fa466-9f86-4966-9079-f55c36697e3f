﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.Extensions;
using BlueTape.Services.OnBoardingService.DataAccess.Pipelines;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories
{
    public class CreditApplicationRepository : GenericRepository<CreditApplicationDocument>, ICreditApplicationRepository
    {
        public CreditApplicationRepository(
            IObsMongoDBContext context,
            ILogger<CreditApplicationRepository> logger) : base(context, logger)
        {
        }

        public async Task<IEnumerable<CreditApplicationDocument>> GetByIds(string[] ids, CancellationToken ct)
        {
            Logger.LogInformation("Get credit applications by ids: {ids}", JArray.FromObject(ids));
            var validIds = ids.Where(id => ObjectId.TryParse(id, out _));
            var expression = Builders<CreditApplicationDocument>.Filter;
            var filterDefinition = expression.In(x => x.Id, validIds);
            var pipeline = BuildFilteredLookupQuery(filterDefinition);

            using var creditApplicationsQuery = await Collection.AggregateAsync(pipeline, cancellationToken: ct);

            return await creditApplicationsQuery.ToListAsync(ct);
        }

        public override async Task<CreditApplicationDocument> GetById(string id, CancellationToken ct)
        {
            Logger.LogInformation("Get by id {type} documents, id: {id}", typeof(CreditApplicationDocument).ToString(), id);

            var expression = Builders<CreditApplicationDocument>.Filter;
            var filterDefinition = expression.Eq(x => x.Id, id);
            var pipeline = BuildFilteredLookupQuery(filterDefinition);
            using var creditApplicationsQuery = await Collection.AggregateAsync(pipeline, cancellationToken: ct);

            var result = await creditApplicationsQuery.FirstOrDefaultAsync(ct);

            if (result is not null) return result;

            Logger.LogError("Entity type of {type} with {id} not found", typeof(CreditApplicationDocument).ToString(), id);
            throw new VariableNullException($"Document with id {id} not found");
        }

        public async Task<IEnumerable<CreditApplicationDocument>> GetByEinHashes(string[] einHashes, CancellationToken ct)
        {
            Logger.LogInformation("Get credit applications by ein hashes: {hashes}", JArray.FromObject(einHashes));
            var expression = Builders<CreditApplicationDocument>.Filter;
            var filterDefinition = expression.In(x => x.EinHash, einHashes);
            var pipeline = BuildFilteredLookupQuery(filterDefinition);

            using var creditApplicationsQuery = await Collection.AggregateAsync(pipeline, cancellationToken: ct);

            return await creditApplicationsQuery.ToListAsync(ct);
        }

        public async Task<IEnumerable<CreditApplicationDocument>> GetAllByFilters(GetCreditApplicationQuery query, CancellationToken ct)
        {
            Logger.LogInformation("Get credit applications by filters: {@Query}", query);

            var filterDefinition = BuildFilterDefinition(query);
            var sortDefinition = BuildSortDefinition(query);

            var pipeline = BuildFilteredLookupQuery(filterDefinition).Sort(sortDefinition);

            using var creditApplicationsQuery = await Collection.AggregateAsync(pipeline, cancellationToken: ct);

            return await creditApplicationsQuery.ToListAsync(ct);
        }
        public async Task<GetQueryWithPaginationResult<CreditApplicationDocument>> GetAllByFiltersWithPagination(GetCreditApplicationQueryWithPagination query, CancellationToken ct)
        {
            Logger.LogInformation("Get credit applications by filters: {@Query}", query);

            var filterDefinition = BuildFilterDefinition(query);
            var sortDefinition = BuildSortDefinition(query);
            var skip = (query.PageNumber - 1) * query.PageSize;
            var limit = query.PageSize;

            var pipeline = BuildFilteredLookupQuery(filterDefinition)
                .Sort(sortDefinition)
                .Skip(skip)
                .Limit(limit);

            using var creditApplicationsQuery = await Collection.AggregateAsync(pipeline, cancellationToken: ct);

            var totalCount = await Collection.CountDocumentsAsync(filterDefinition, cancellationToken: ct);
            var pageSize = query.PageSize <= 0 ? 1 : query.PageSize;

            return new GetQueryWithPaginationResult<CreditApplicationDocument>()
            {
                TotalCount = totalCount,
                Result = await creditApplicationsQuery.ToListAsync(ct),
                PageNumber = query.PageNumber,
                PagesCount = (long)Math.Round((double)totalCount / pageSize, 0, MidpointRounding.ToPositiveInfinity)
            };
        }


        public async Task<IReadOnlyList<LightCreditApplicationDocument>> GetLightCreditApplications(string[] companyIds, string status, CancellationToken ct)
        {
            Logger.LogInformation("Get credit applications by company ids: {ids} with status: {status}", JArray.FromObject(companyIds), status);

            var expression = Builders<CreditApplicationDocument>.Filter;
            var filterDefinition = expression.And(
                expression.In(x => x.CompanyId, companyIds),
                expression.Regex(x => x.Status, status.BuildRegexDefinition()),
                expression.Ne(x => x.DraftId, null),
                expression.Ne(x => x.DraftId, "")
            );

            var creditApplicationsQuery = await Collection
                .Find(filterDefinition)
                .Project(creditApplication => new LightCreditApplicationDocument
                {
                    Id = creditApplication.Id,
                    Type = creditApplication.Type,
                    CompanyId = creditApplication.CompanyId
                })
                .ToListAsync(ct);

            return creditApplicationsQuery;
        }

        public async Task<IReadOnlyList<LightCreditApplicationDocument>> GetLightCreditApplications(string[] companyIds, CancellationToken ct)
        {
            Logger.LogInformation("Get credit applications by company ids: {ids}", JArray.FromObject(companyIds));

            var expression = Builders<CreditApplicationDocument>.Filter;
            var filterDefinition = expression.In(x => x.CompanyId, companyIds);

            var creditApplicationsQuery = await Collection
                .Find(filterDefinition)
                .Project(creditApplication => new LightCreditApplicationDocument
                {
                    Id = creditApplication.Id,
                    Type = creditApplication.Type,
                    CompanyId = creditApplication.CompanyId
                })
                .ToListAsync(ct);

            return creditApplicationsQuery;
        }

        public async Task<IEnumerable<CreditApplicationDocument>> GetByCompanyIds(
            GetCreditApplicationsByCompanyIdsQuery query,
            CancellationToken ct)
        {
            var builder = Builders<CreditApplicationDocument>.Filter;
            var filters = new List<FilterDefinition<CreditApplicationDocument>>
            {
                builder.In(x => x.CompanyId, query.CompanyIds)
            };

            if (query.Status?.Length > 0)
                filters.Add(builder.Or(query.Status.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.Status)));

            if (query.Type?.Length > 0)
                filters.Add(builder.Or(query.Type.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.Type)));

            if (query.Category?.Length > 0)
                filters.Add(builder.Or(query.Category.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.BusinessCategory)));

            var filter = builder.And(filters);

            var pipeline = BuildFilteredLookupQuery(filter);

            using var creditApplicationsQuery =
                await Collection.AggregateAsync(pipeline, cancellationToken: ct);

            return await creditApplicationsQuery.ToListAsync(ct);
        }

        private static FilterDefinition<CreditApplicationDocument> BuildFilterDefinition(GetCreditApplicationQuery query)
        {
            var expression = Builders<CreditApplicationDocument>.Filter;
            var filter = expression.Empty;

            if (!string.IsNullOrEmpty(query.Id)) filter &= expression.Eq(x => x.Id, query.Id);
            if (!string.IsNullOrEmpty(query.CompanyId)) filter &= expression.Eq(x => x.CompanyId, query.CompanyId);
            if (!string.IsNullOrEmpty(query.EinHash)) filter &= expression.Eq(x => x.EinHash, query.EinHash);
            if (!string.IsNullOrEmpty(query.MerchantId)) filter &= expression.Eq(x => x.MerchantId, query.MerchantId);
            if (query.AppDateFrom.HasValue) filter &= expression.Gte(x => x.ApplicationDate, query.AppDateFrom);
            if (query.AppDateTo.HasValue) filter &= expression.Lt(x => x.ApplicationDate, query.AppDateTo);

            if (!string.IsNullOrEmpty(query.Name))
            {
                var nameRegex = query.Name.BuildRegexDefinition();
                filter &= expression.Or(
                    expression.Regex(x => x.BusinessName, nameRegex),
                    expression.Regex(x => x.BusinessDba, nameRegex),
                    expression.Regex(x => x.ApplicantName, nameRegex)
                );
            }

            if (!string.IsNullOrEmpty(query.MerchantName))
            {
                var merchantNameRegex = query.MerchantName.BuildRegexDefinition();
                filter &= expression.Or(expression.Regex(x => x.SupplierName, merchantNameRegex));
            }

            //$or + $regex[] -> $in
            if (query.Status?.Length > 0) filter &= expression.Or(query.Status.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.Status));
            if (query.AutomatedDecision?.Length > 0) filter &= expression.Or(query.AutomatedDecision.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.AutomatedDecisionResult));
            if (query.Category?.Length > 0) filter &= expression.Or(query.Category.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.BusinessCategory));
            if (query.Type?.Length > 0) filter &= expression.Or(query.Type.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.Type));
            if (query.BankAccountType?.Length > 0) filter &= expression.Or(query.BankAccountType.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.BankAccountType));
            if (query.ArAdvanceApplicationIds?.Length > 0) filter &= expression.Or(query.ArAdvanceApplicationIds.ConvertToEqualsFiltersList<CreditApplicationDocument>(x => x.ArAdvanceApplicationId));
            if (query.CompanyIds?.Length > 0) filter &= expression.Or(query.CompanyIds.ConvertToCaseInsensitiveRegexFiltersList<CreditApplicationDocument>(x => x.CompanyId));
            if (query.SsnHashes?.Length > 0) filter &= expression.AnyIn(x => x.SsnHashes, query.SsnHashes);

            return filter;
        }

        private static SortDefinition<CreditApplicationDocument> BuildSortDefinition(GetCreditApplicationQuery query)
        {
            var builder = Builders<CreditApplicationDocument>.Sort;

            if (string.IsNullOrWhiteSpace(query.SortBy))
            {
                return builder.Descending(x => x.CreatedAt);
            }

            return string.Equals(query.SortOrder, SortConstants.Ascending, StringComparison.OrdinalIgnoreCase)
                ? builder.Ascending(query.SortBy)
                : builder.Descending(query.SortBy);
        }

        private static PipelineDefinition<CreditApplicationDocument, CreditApplicationDocument> BuildFilteredLookupQuery(FilterDefinition<CreditApplicationDocument> filterDefinition)
        {
            var lookupPackagesPipeline = BuildLookupPackagesQuery();
            var lookupCustomersAccountsPipeline = BuildCustomerAccountQuery(lookupPackagesPipeline);
            var lookupSsnHashesPipeline = BuildSsnHashesQuery(lookupCustomersAccountsPipeline);

            return lookupSsnHashesPipeline.Match(filterDefinition);
        }

        private static PipelineDefinition<CreditApplicationDocument, CreditApplicationDocument> BuildLookupPackagesQuery()
        {
            var pipeline = new EmptyPipelineDefinition<CreditApplicationDocument>()
                .AppendStage(CreditApplicationPipelines.LookupCardPricingPackages)
                .AppendStage(CreditApplicationPipelines.LookupLoanPricingPackages)
                .AppendStage(CreditApplicationPipelines.UnwindCardPricingPackages)
                .AppendStage(CreditApplicationPipelines.UnwindLoanPricingPackages)
                .AppendStage(CreditApplicationPipelines.AddPricingPackagesNames)
                .AppendStage(CreditApplicationPipelines.ProjectPricingPackagesNames);

            return pipeline;
        }

        private static PipelineDefinition<CreditApplicationDocument, CreditApplicationDocument> BuildCustomerAccountQuery(PipelineDefinition<CreditApplicationDocument, CreditApplicationDocument> pipeline)
        {
            var updatedPipeline = pipeline
                .AppendStage(CreditApplicationPipelines.LookupUserRoles)
                .AppendStage(CreditApplicationPipelines.UnwindUserRole)
                .AppendStage(CreditApplicationPipelines.LookupUser)
                .AppendStage(CreditApplicationPipelines.UnwindUser)
                .AppendStage(CreditApplicationPipelines.LookupInHouseCustomerAccount)
                .AppendStage(CreditApplicationPipelines.UnwindCustomerAccount)
                .AppendStage(CreditApplicationPipelines.AddCustomerAccountInfo);

            return updatedPipeline;
        }


        private static PipelineDefinition<CreditApplicationDocument, CreditApplicationDocument> BuildSsnHashesQuery(PipelineDefinition<CreditApplicationDocument, CreditApplicationDocument> pipeline)
        {
            var updatedPipeline = pipeline
                .AppendStage(CreditApplicationPipelines.LookupAccountAuthorizations)
                .AppendStage(CreditApplicationPipelines.UnwindAccountAuthorizationDetails)
                .AppendStage(CreditApplicationPipelines.AddSsnHashes);

            return updatedPipeline;
        }
    }
}
