﻿using BlueTape.ServiceBusMessaging;
using BlueTape.Services.OnBoardingService.Domain.Constants;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorService.Senders;

public class RefreshServiceMessageSender(IConfiguration configuration, ILogger<RefreshServiceMessageSender> logger) :
    ServiceBusMessageSender<ScheduledUpdateEvent>(
    configuration,
    logger,
    InfrastructureConstants.RefreshServiceQueueName,
    InfrastructureConstants.RefreshServiceQueueConnectionString), IRefreshServiceMessageSender
{
}
