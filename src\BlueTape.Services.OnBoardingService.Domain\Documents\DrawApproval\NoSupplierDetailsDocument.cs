﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class NoSupplierDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("businessName")]
    public string? BusinessName { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("contactName")]
    public string? ContactName { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("email")]
    public string? Email { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("phone")]
    public string? Phone { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("address")]
    public NoSupplierAddressDocument? Address { get; set; } = new();

    [BsonIgnoreIfNull]
    [BsonElement("bankDetails")]
    public NoSupplierBankDetailsDocument? BankDetails { get; set; } = new();
}
