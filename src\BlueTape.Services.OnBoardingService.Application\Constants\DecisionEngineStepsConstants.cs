﻿using System.Collections.Immutable;

namespace BlueTape.Services.OnBoardingService.Application.Constants
{
    public static class DecisionEngineStepsConstants
    {
        public static readonly ImmutableDictionary<string, string> RuleCodes = new Dictionary<string, string>()
        {
            {"Rule-101","BVI"},
            {"Rule-100","BRI"},
            {"Rule-206","Business2Exec index"},
            {"Rule-204","CVI"},
            {"Rule-205","CVI"},
            {"Rule-203/2","CVI"},
            {"Rule-203/1","CRI"},
            {"Rule-300","Reliability Code"},
            {"Rule-302","Business Bankruptcy"},
            {"Rule-800","Judgements"},
            {"Rule-801","Liens"},
            {"Rule-804","Past Due Amount / Revenue Ratio"},
            {"Rule-400","FICO"},
            {"Rule-401","Personal Bankruptcy"},
            {"Rule-1000","Cashflow annual revenue"}
        }.ToImmutableDictionary();

        public const string Ignored = " ignored";
        public const string Reinstated = " reinstated";
        public const string MatchPattern = @"^\s*([A-Za-z]+-\d+)";
    }
}
