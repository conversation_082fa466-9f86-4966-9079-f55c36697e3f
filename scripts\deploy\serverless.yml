service:  dotnet-onboarding-service 
useDotenv: true
frameworkVersion: ^3.9.0

plugins:
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-plugin-warmup
  - serverless-domain-manager
  - serverless-prune-plugin
package:
  individually: true

custom: ${file(_custom.yml):global} # Global variables

provider:
  name: aws
  runtime: dotnet6
  region: us-west-1
  # stage: ${env:STAGE, 'dev'}
  timeout: 30
  memorySize: 1024
  tracing:
    apiGateway: true
    lambda: true
  apiGateway:
    shouldStartNameWithService: true
    binaryMediaTypes:
      - "multipart/form-data"
  deploymentBucket:
    name: ${self:custom.onBoardingService.deploymentBucket.${env:STAGE}}
  iam:
    role: ${self:custom.onBoardingService.role.${env:STAGE}}
  vpc:
    securityGroupIds: ${self:custom.onBoardingService.vpcGroup.${env:STAGE}}
    subnetIds: ${self:custom.onBoardingService.vpcSubnet.${env:STAGE}}
  environment:
    ASPNETCORE_ENVIRONMENT: "${env:ASPNETCORE_ENVIRONMENT}" # Need to be parameterized, cause not working the same as FE apps
    PROJECT_NAME: OnBoardingService
    LOGZIO_TOKEN: ${env:LOGZIO_TOKEN}

functions:
  API:
    handler: BlueTape.Services.OnBoardingService.API
    package:
      artifact: ${env:API_LAMBDA_PACKAGE_LOCATION} # TODO: generalize
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
    lambdaInsights: ${self:custom.onBoardingService.lambdaInsights.${env:STAGE}}

resources:
  Resources:
    GatewayResponseDefault4XX:
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent,session,challenge'"
        ResponseType: DEFAULT_4XX
        RestApiId:
          Ref: "ApiGatewayRestApi"
    GatewayResponseDefault5XX:
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent,session,challenge'"
        ResponseType: DEFAULT_5XX
        RestApiId:
          Ref: "ApiGatewayRestApi"

