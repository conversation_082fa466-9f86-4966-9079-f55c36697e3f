﻿using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions
{
    public interface IDraftRepository : IGenericRepository<DraftDocument>
    {
        Task<IEnumerable<DraftDocument>> GetAllByFilters(string? id, string? companyId, string? creditApplicationId, CancellationToken ct);
        Task<IEnumerable<DraftDocument>> GetByCompanyIds(string[] companyIds, CancellationToken ct);
    }
}
