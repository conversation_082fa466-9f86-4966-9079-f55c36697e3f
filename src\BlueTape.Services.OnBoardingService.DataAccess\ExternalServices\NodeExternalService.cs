﻿using System.Collections;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.ExternalServices;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.HttpClients;
using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace BlueTape.Services.OnBoardingService.DataAccess.ExternalServices;

public class NodeExternalService(
    INodeHttpClient serviceHttpClient,
    ILogger<NodeExternalService> logger) : INodeExternalService
{
    public async Task NotifyOpsTeam(OpsTeamNotificationRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.OpsTeamNotificationPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("NotifyOpsTeam: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"NotifyOpsTeam failed at path: {NodeServiceConstants.OpsTeamNotificationPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task HumanApproval(IssueLoanRequest request, CancellationToken ct)
    {
        JsonSerializerOptions serializerOptions = new();
        serializerOptions.Converters.Add(new JsonStringEnumConverter());
        var json = JsonSerializer.Serialize(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.HumanApprovalPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("IssueLoan: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"IssueLoan failed at path: {NodeServiceConstants.HumanApprovalPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task IssueFactoringLoan(IssueLoanRequest request, CancellationToken ct)
    {
        JsonSerializerOptions serializerOptions = new();
        serializerOptions.Converters.Add(new JsonStringEnumConverter());
        var json = JsonSerializer.Serialize(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.IssueFactoringLoanPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("IssueFactoringLoanPath: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"IssueFactoringLoanPath failed at path: {NodeServiceConstants.IssueFactoringLoanPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task IssueLoanPrequalified(IssueLoanPrequalifiedRequest request, CancellationToken ct)
    {
        JsonSerializerOptions serializerOptions = new();
        serializerOptions.Converters.Add(new JsonStringEnumConverter());
        var json = JsonSerializer.Serialize(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.IssueLoanPrequalifiedPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("IssueLoanPrequalified: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"IssueLoanPrequalified failed at path: {NodeServiceConstants.HumanApprovalPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task TriggerUserApprovalNotification(UserApprovalNotificationRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.ApprovalUserNotificationPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("CreateCredit: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"CreateCredit failed at path: {NodeServiceConstants.ApprovalUserNotificationPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task TriggerSentBackUserNotification(SendBackUserNotificationRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.SendBackNotificationPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("SentBackUserNotification: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"SentBackUserNotification failed at path: {NodeServiceConstants.ApprovalUserNotificationPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task TriggerLoanCancellationCustomerNotification(LoanCancellationCustomerNotificationRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.LoanCanceledNotificationPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("LoanCancellationCustomerNotification: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"LoanCancellationCustomerNotification failed at path: {NodeServiceConstants.ApprovalUserNotificationPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task RejectLoan(UserRejectedNotificationRequest request, CancellationToken ct)
    {
        JsonSerializerOptions serializerOptions = new();
        serializerOptions.Converters.Add(new JsonStringEnumConverter());
        var json = JsonSerializer.Serialize(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.RejectLoan, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("Reject credit: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"Reject credit failed at path: {NodeServiceConstants.RejectLoan}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task PostTransaction(PostTransactionRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.PostTransactionPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("PostTransaction: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"PostTransaction failed at path: {NodeServiceConstants.PostTransactionPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task UpdateAuthorization(UpdateAuthorizationRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.UpdateAuthorizationPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("UpdateAuthorizationPath: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"UpdateAuthorizationPath failed at path: {NodeServiceConstants.UpdateAuthorizationPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task TriggerCancelLoanApplication(CancelLoanApplicationRequest request, CancellationToken ct)
    {
        JsonSerializerOptions serializerOptions = new();
        serializerOptions.Converters.Add(new JsonStringEnumConverter());
        var json = JsonSerializer.Serialize(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.CancelLoanApplication, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("Cancel loan application: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"Cancel loan application failed at path: {NodeServiceConstants.CancelLoanApplication}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task TriggerIhcDrawApprovalCustomerNotification(DrawApprovalReviewUserNotification request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.IhcDrawApprovalNotificationPath, data, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            logger.LogInformation("DrawApprovalSupplierNotification: string response: {response}", responseString);
            return;
        }

        throw new HttpClientRequestException($"DrawApprovalSupplierNotification failed at path: {NodeServiceConstants.IhcDrawApprovalNotificationPath}." +
                                             $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<InvoiceStatusResponse>?> GetInvoiceStatuses(InvoiceStatusesRequest request, CancellationToken ct)
    {
        JsonSerializerSettings serializerOptions = new();
        serializerOptions.ContractResolver = new CamelCasePropertyNamesContractResolver();
        var json = JsonConvert.SerializeObject(request, serializerOptions);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);

        var response = await serviceHttpClient.Client.PostAsync(NodeServiceConstants.InvoiceStatus, data, ct);

        if (!response.IsSuccessStatusCode)
        {
            throw new HttpClientRequestException($"Getting invoice statuses failed at path: {NodeServiceConstants.InvoiceStatus}." +
                                                 $"Status code: {response.StatusCode}");
        }
        
        var responseString = await response.Content.ReadAsStringAsync(ct);
        logger.LogInformation("DrawApprovalSupplierNotification: string response: {response}", responseString);
        return JsonSerializer.Deserialize<IEnumerable<InvoiceStatusResponse>>(responseString);
    }
}
