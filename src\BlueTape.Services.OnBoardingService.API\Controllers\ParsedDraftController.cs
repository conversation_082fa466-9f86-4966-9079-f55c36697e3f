﻿using AutoMapper;
using BlueTape.OBS.DTOs.ParsedDraft;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using Microsoft.AspNetCore.Mvc;


namespace BlueTape.Services.OnBoardingService.API.Controllers;
[Route(ControllersConstants.ParsedDrafts)]
[ApiController]
public class ParsedDraftController(IParsedDraftService parsedDraftService, IMapper mapper) : ControllerBase
{

    [HttpGet(EndpointConstants.Id)]
    public async Task<ParsedDraftDto> GetById([FromRoute] string id, CancellationToken ct)
    {
        var draft = await parsedDraftService.GetById(id, ct);
        return mapper.Map<ParsedDraftDto>(draft);
    }

    [HttpGet(EndpointConstants.Draft)]
    public async Task<ParsedDraftDto> GetByDraftId([FromQuery] string draftId, CancellationToken ct)
    {
        var draft = await parsedDraftService.GetByDraftId(draftId, ct);
        return mapper.Map<ParsedDraftDto>(draft);
    }

    [HttpGet(EndpointConstants.Company)]
    public async Task<ParsedDraftDto> GetByCompanyId([FromQuery] string companyId, CancellationToken ct)
    {
        var draft = await parsedDraftService.GetByCompanyId(companyId, ct);
        return mapper.Map<ParsedDraftDto>(draft);
    }

    /// <summary>
    /// Replace or create parsed draft based on the original draft id
    /// </summary>
    [HttpPost(EndpointConstants.Id)]
    public async Task<ParsedDraftDto> Post([FromRoute] string id, CancellationToken ct)
    {
        var draft = await parsedDraftService.MigrateDraft(id, ct);
        return mapper.Map<ParsedDraftDto>(draft);
    }
}
