﻿using AutoMapper;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplication.Queries;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Reports;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers
{
    [Route(ControllersConstants.CreditApplications)]
    [ApiController]
    public class CreditApplicationsController(
        ICreditApplicationService creditApplicationService,
        ICreditApplicationExecutionService creditApplicationExecutionService,
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        IValidator<UpdateCreditApplicationDto> updateCreditApplicationDtoValidator,
        IMapper mapper,
        IExcelService excelService,
        IReportsService reportsService,
        IMigrationService migrationService)
        : ControllerBase
    {
        /// <summary>
        /// Array of credit applications by different filters
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications?Id=650b28825f07d3ca092f294a,
        ///     GET /CreditApplications?CompanyId=634fd62d23059967a18b1da3,
        ///     GET /CreditApplications?EinHash=5fc7cb7fc5f00ee425c5d2d689c8383f
        ///     GET /CreditApplications?Status=approved
        ///     GET /CreditApplications?Id=650b28825f07d3ca092f294a&amp;Status=approved
        ///     GET /CreditApplications?Id=650b28825f07d3ca092f294a&amp;CompanyId=634fd62d23059967a18b1da3&amp;Status=approved
        ///     GET /CreditApplications?Id=650b28825f07d3ca092f294a&amp;CompanyId=634fd62d23059967a18b1da3&amp;EinHash=5fc7cb7fc5f00ee425c5d2d689c8383f&amp;Status=approved
        ///
        /// </remarks>
        /// <returns>Array of credit applications</returns>
        [HttpGet]
        public async Task<IEnumerable<CreditApplicationDto>> Get([FromQuery] CreditApplicationQueryDto queryDto, CancellationToken ct)
        {
            var query = mapper.Map<GetCreditApplicationQuery>(queryDto);

            if (query.Id is null && query.CompanyId is null && query.EinHash is null && query.Status is null && query.MerchantId is null)
            {
                return mapper.Map<IEnumerable<CreditApplicationDto>>(await creditApplicationService.GetAll(ct));
            }

            return mapper.Map<IEnumerable<CreditApplicationDto>>(await creditApplicationService.GetAllByFilters(query, ct));
        }

        /// <summary>
        /// Array of credit applications by ids
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications/ids
        ///
        /// </remarks>
        /// <returns>Array of credit applications</returns>
        [HttpPost("ids")]
        public async Task<IEnumerable<CreditApplicationDto>> GetByIds([FromBody] CreditApplicationIdsDto idsModel, CancellationToken ct)
        {
            var ids = idsModel.Ids;
            return mapper.Map<IEnumerable<CreditApplicationDto>>(await creditApplicationService.GetByIds(ids, ct));
        }

        /// <summary>
        /// Array of credit applications by ein hash array
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications/ein
        ///
        /// </remarks>
        /// <returns>Array of credit applications</returns>
        [HttpPost("ein")]
        public async Task<IEnumerable<CreditApplicationDto>> GetByEinHashes([FromBody] string[] einHashes, CancellationToken ct)
        {
            return mapper.Map<IEnumerable<CreditApplicationDto>>(await creditApplicationService.GetByEinHashes(einHashes, ct));
        }

        /// <summary>
        /// Array of credit applications by different filters
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications/Query?Id=650b28825f07d3ca092f294a&amp;PageSize=10&amp;PageNumber=1
        ///     GET /CreditApplications/Query?CompanyId=634fd62d23059967a18b1da3&amp;PageSize=10&amp;PageNumber=1
        ///     GET /CreditApplications/Query?EinHash=5fc7cb7fc5f00ee425c5d2d689c8383f&amp;PageSize=10&amp;PageNumber=1
        ///     GET /CreditApplications/Query?Status=approved&amp;PageSize=10&amp;PageNumber=1
        ///     GET /CreditApplications/Query?Id=650b28825f07d3ca092f294a&amp;Status=approved&amp;PageSize=10&amp;PageNumber=1
        ///     GET /CreditApplications/Query?Id=650b28825f07d3ca092f294a&amp;CompanyId=634fd62d23059967a18b1da3&amp;Status=approved&amp;PageSize=10&amp;PageNumber=1
        ///     GET /CreditApplications/Query?Id=650b28825f07d3ca092f294a&amp;CompanyId=634fd62d23059967a18b1da3&amp;EinHash=5fc7cb7fc5f00ee425c5d2d689c8383f&amp;Status=approved&amp;PageSize=10&amp;PageNumber=1
        ///
        /// </remarks>
        /// <returns>Array of credit applications</returns>
        [HttpGet("Query")]
        public async Task<GetQueryWithPaginationResultDto<CreditApplicationDto>> GetWithPagination([FromQuery] CreditApplicationQueryPaginatedDto queryDto, CancellationToken ct)
        {
            var query = mapper.Map<GetCreditApplicationQueryWithPagination>(queryDto);
            var result = await creditApplicationService.GetAllByFiltersWithPagination(query, ct);

            return mapper.Map<GetQueryWithPaginationResultDto<CreditApplicationDto>>(result);
        }

        /// <summary>
        /// Get a credit application by Id
        /// </summary>
        /// <param name="id">The credit application id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications/650b28825f07d3ca092f294a
        ///
        /// </remarks>
        [HttpGet(EndpointConstants.Id)]
        public async Task<CreditApplicationDto> GetById([FromRoute] string id, CancellationToken ct)
        {
            var creditApplication = await creditApplicationService.GetById(id, ct);

            return mapper.Map<CreditApplicationDto>(creditApplication);
        }

        /// <summary>
        /// Retrieves a collection of credit applications associated with specified company IDs.
        /// </summary>
        /// <param name="companyIds">The object containing a list of company IDs to filter the credit applications.</param>
        /// <param name="query">Additional query parameters for filtering of the results.</param>
        /// <param name="ct">Cancellation token to cancel the operation if needed.</param>
        /// <returns>A collection of credit applications matching the provided filters.</returns>
        [HttpPost("getByCompanyIds")]
        public async Task<IEnumerable<CreditApplicationDto>> GetByCompanyIds(
            [FromBody] CompanyIdsDto companyIds,
            [FromQuery] GetCreditApplicationsByCompanyIdsQueryDto query,
            CancellationToken ct)
        {
            var model = mapper.Map<GetCreditApplicationsByCompanyIdsQuery>((companyIds, query));
            return mapper.Map<IEnumerable<CreditApplicationDto>>(
                await creditApplicationService.GetByCompanyIds(model, ct));
        }

        /// <summary>
        /// Create a credit application
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications
        ///     {
        ///         "companyId": "string",
        ///         "einHash": "string",
        ///         "draftId": "string",
        ///         "applicationDate": "2023-09-28"
        ///         "businessName": "string",
        ///         "businessDba": "string",
        ///         "businessCategory": "string",
        ///         "applicantName": "string",
        ///         "supplierName": "string",
        ///         "requestedAmount": 0,
        ///         "automatedDecisionResult": "string"
        ///     }
        ///
        /// </remarks>
        [HttpPost]
        public async Task<CreditApplicationDto> Create(CreateCreditApplicationDto createCreditApplicationViewModel, CancellationToken ct)
        {
            var createCreditApplication = mapper.Map<CreateCreditApplication>(createCreditApplicationViewModel);
            var creditApplication = await creditApplicationService.Create(createCreditApplication, ct);

            return mapper.Map<CreditApplicationDto>(creditApplication);
        }

        /// <summary>
        /// Update a credit application by id
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /CreditApplications/650b28825f07d3ca092f294a
        ///     {
        ///         "newStatus": "string",
        ///         "creditLimit": 0,
        ///         "approvedCreditLimit": 0,
        ///         "updatedBy": "updatedBy",
        ///         "businessName": "string",
        ///         "businessDba": "string",
        ///         "businessCategory": "string",
        ///         "applicantName": "string",
        ///         "requestedAmount": 0,
        ///         "automatedDecisionResult": "string"
        ///     }
        ///
        /// </remarks>
        [HttpPatch(EndpointConstants.Id)]
        public async Task<CreditApplicationDto> Update([FromRoute] string id, UpdateCreditApplicationDto updateCreditApplicationViewModel, CancellationToken ct)
        {
            await updateCreditApplicationDtoValidator.ValidateAndThrowAsync(updateCreditApplicationViewModel, ct);

            var updateCreditApplication = mapper.Map<UpdateCreditApplication>(updateCreditApplicationViewModel);
            updateCreditApplication.Id = id;
            var creditApplication = await creditApplicationService.Update(updateCreditApplication, ct);

            return mapper.Map<CreditApplicationDto>(creditApplication);
        }

        /// <summary>
        /// Create report by a credit application id
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications/650b28825f07d3ca092f294a/Report
        ///
        /// </remarks>
        [HttpPost($"{EndpointConstants.CreditApplicationId}/{EndpointConstants.Report}")]
        public Task<string> CreateDecisionEngineStepsReport([FromRoute] string creditApplicationId, CancellationToken ct)
        {
            return excelService.UploadDecisionEngineStepsReport(creditApplicationId, ct);
        }

        /// <summary>
        /// Create credit application and start Decision Engine Execution
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications/Submit
        ///
        /// </remarks>
        [HttpPost($"{EndpointConstants.Submit}")]
        public async Task<CreditApplicationDto> SubmitCreditApplication([FromBody] SubmitCreditApplication submitCreditApplication, CancellationToken ct)
        {
            var creditApplication = await creditApplicationExecutionService.SubmitCreditApplication(submitCreditApplication, ct);

            return mapper.Map<CreditApplicationDto>(creditApplication);
        }

        /// <summary>
        /// Start Decision Engine Execution
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications/Execute
        ///
        /// </remarks>
        [HttpPost($"{EndpointConstants.Execute}")]
        public Task<StepFunctionsExecutionResponse> ExecuteDecisionEngineForCreditApplication([FromBody] CreditApplicationDecisionEngineExecutionRequest request, CancellationToken ct)
        {
            return creditApplicationExecutionService.RunDecisionEngineForCreditApplication(request, ct);
        }

        /// <summary>
        /// Start Decision Engine Initialization Step Execution
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /CreditApplications/Execute/InitializationStep
        ///
        /// </remarks>
        [HttpPost($"{EndpointConstants.Execute}/{EndpointConstants.InitializationStep}")]
        public Task<StepFunctionsExecutionResponse> RunDecisionEngineInitializationStepForCreditApplication(
            [FromBody] OBS.DTOs.CreditApplication.Requests.CreditApplicationInitializationStepStartRequest request, CancellationToken ct)
        {
            return creditApplicationExecutionService.RunDecisionEngineInitializationStepForCreditApplication(
                mapper.Map<CreditApplicationInitializationStepStartRequest>(request), ct);
        }

        /// <summary>
        /// Get execution details for credit application id
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Report
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Report?stepname=preliminary-step
        ///
        /// </remarks>
        [HttpGet($"{EndpointConstants.CreditApplicationId}/{EndpointConstants.Report}")]
        public Task<IEnumerable<ReportStepDataModel>> GetExecutionDetails([FromRoute] string creditApplicationId, [FromQuery] StepName? stepname,
            CancellationToken ct)
        {
            return reportsService.RetrieveExecutionDetails(creditApplicationId, stepname, ct);
        }

        /// <summary>
        /// Get formatted raw response data from Lexis Nexis for credit application id (BusinessInstantId option is default)
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Bvi/LexisNexis
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Bvi/LexisNexis?type=businessInstantId
        ///
        /// </remarks>
        [HttpGet($"{EndpointConstants.CreditApplicationId}/{EndpointConstants.Bvi}/{EndpointConstants.LexisNexis}")]
        public Task<string> GetLexisNexisRawData([FromRoute] string creditApplicationId, [FromQuery] LexisNexisSourceType? type,
            [FromQuery] string? reference,
            CancellationToken ct)
        {
            return decisionEngineStepsBviResultsService.GetLexisNexisRawData(creditApplicationId, type, reference, ct);
        }

        /// <summary>
        /// Get formatted raw response data from Experian for credit application id (BusinessCreditStatus option is default)
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Bvi/Experian
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Bvi/Experian?type=businessSearch
        ///
        /// </remarks>
        [HttpGet($"{EndpointConstants.CreditApplicationId}/{EndpointConstants.Bvi}/{EndpointConstants.Experian}")]
        public Task<string> GetExperianRawData([FromRoute] string creditApplicationId, [FromQuery] ExperianSourceType? type,
            CancellationToken ct)
        {
            return decisionEngineStepsBviResultsService.GetExperianRawData(creditApplicationId, type, ct);
        }

        /// <summary>
        /// Get formatted raw response data from Giact for credit application id
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /CreditApplications/650b28825f07d3ca092f294a/Bvi/Giact
        ///
        /// </remarks>
        [HttpGet($"{EndpointConstants.CreditApplicationId}/{EndpointConstants.Bvi}/{EndpointConstants.Giact}")]
        public Task<string> GetGiactRawData([FromRoute] string creditApplicationId, CancellationToken ct)
        {
            return decisionEngineStepsBviResultsService.GetGiactRawData(creditApplicationId, ct);
        }

        [HttpGet("MigrateCreditApplicationType")]
        public Task MigrateType(CancellationToken ct) => migrationService.MigrateType(ct);
    }
}
