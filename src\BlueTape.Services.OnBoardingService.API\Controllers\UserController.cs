﻿using AutoMapper;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.API.ViewModels;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;


[Route(ControllersConstants.Users)]
[ApiController]
public class UsersController(IUserService userService, IMapper mapper)
{
    /// <summary>
    /// Get user short data by company id
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /users/{companyId}
    ///     
    /// </remarks>
    /// <returns>User short data</returns>
    [HttpGet]
    public async Task<UserDto?> GetByCompanyId(string companyId, CancellationToken ctx)
    {
        var user = await userService.GetByCompanyId(companyId, ctx);

        return mapper.Map<UserDto>(user);
    }

    /// <summary>
    /// Get user short data by sub
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /users/sub/{sub}
    ///     
    /// </remarks>
    /// <returns>User short data</returns>
    [HttpGet("sub/{sub}")]
    public async Task<UserDto?> GetBySub([FromRoute] string sub, CancellationToken ctx)
    {
        var user = await userService.GetBySubAsync(sub, ctx);

        return mapper.Map<UserDto?>(user);
    }
}