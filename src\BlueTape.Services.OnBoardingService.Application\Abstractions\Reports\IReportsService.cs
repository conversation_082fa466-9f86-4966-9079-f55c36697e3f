﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions.Reports;

public interface IReportsService
{
    Task<CreateReportModel> RetrieveDataForReportsCreation(string creditApplicationId, CancellationToken ct);

    Task<IEnumerable<ReportStepDataModel>> RetrieveExecutionDetails(string creditApplicationId, StepName? stepName, CancellationToken ct);
}
