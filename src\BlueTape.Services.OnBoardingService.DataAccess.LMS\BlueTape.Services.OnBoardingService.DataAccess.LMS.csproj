﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BlueTape.Common.Extensions" Version="1.1.1" />
    <PackageReference Include="BlueTape.LS" Version="1.1.77" />
    <PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.Infrastructure\BlueTape.Services.OnBoardingService.Infrastructure.csproj" />
  </ItemGroup>

</Project>
