﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.CreateAccountAuthorization;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.API.Tests.Validators;
public class CreateAccountAuthorizationDtoValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new CreateAccountAuthorizationDtoValidator();

        var model = new CreateAccountAuthorizationDto()
        {
            CompanyId = "string",
            EinHash = "string"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }
    [Fact]
    public void Validate_InvalidModel_ReturnsFalse()
    {
        var validator = new CreateAccountAuthorizationDtoValidator();

        var model = new CreateAccountAuthorizationDto()
        {
            CompanyId = "string"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }
}
