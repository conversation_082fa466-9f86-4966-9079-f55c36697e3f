﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;

[BsonIgnoreExtraElements]
[MongoCollection("accountAuthorizations")]
public class AccountAuthorizationDocument : Document
{
    [BsonElement("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    [BsonElement("updatedBy")]
    public string UpdatedBy { get; set; } = string.Empty;

    [BsonElement("companyId")]
    public string CompanyId { get; set; } = string.Empty;

    [BsonElement("einHash")]
    public string EinHash { get; set; } = string.Empty;

    [BsonElement("isSentBack")]
    public bool? IsSentBack { get; set; }

    [BsonElement("creditApplicationIds")]
    [BsonIgnoreIfNull]
    public string[]? CreditApplicationIds { get; set; }

    [BsonElement("businessDetails")]
    public BusinessDetailsDocument? BusinessDetails { get; set; } = new();

    [BsonElement("ownersDetails")]
    public IEnumerable<OwnersDetailsDocument> OwnersDetails { get; set; } = Enumerable.Empty<OwnersDetailsDocument>();

    [BsonElement("ownersEntitiesDetails")]
    public IEnumerable<OwnersEntitiesDetailsDocument> OwnersEntitiesDetails { get; set; } = Enumerable.Empty<OwnersEntitiesDetailsDocument>();

    [BsonElement("bankAccountDetails")]
    public IEnumerable<BankAccountDetailsDocument> BankAccountDetails { get; set; } = Enumerable.Empty<BankAccountDetailsDocument>();

    [BsonElement("creditDetails")]
    public AccountAuthorizationCreditDetailsDocument CreditDetails { get; set; } = new();

    [BsonElement("factoringOverallDetails")]
    public AccountAuthorizationFactoringOverallDetailsDocument FactoringOverallDetails { get; set; } = new();
}