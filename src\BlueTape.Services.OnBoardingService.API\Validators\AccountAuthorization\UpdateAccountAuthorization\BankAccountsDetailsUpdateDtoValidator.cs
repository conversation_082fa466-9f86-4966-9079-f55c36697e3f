﻿using BlueTape.OBS.DTOs.AccountAuthorization;
using FluentValidation;

namespace BlueTape.Services.OnBoardingService.API.Validators.AccountAuthorization.UpdateAccountAuthorization;

public class BankAccountsDetailsUpdateDtoValidator : AbstractValidator<BankAccountDetailsUpdateDto>
{
    public BankAccountsDetailsUpdateDtoValidator()
    {
        RuleFor(x => x.Identifier).NotEmpty();
        RuleFor(x => x.AddressScore).NotEmpty();
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.CompanyAddressScore).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.CompanyNameScore).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.NameScore).NotEmpty();
        RuleFor(x => x.PersonalAddressScore).NotEmpty();
        RuleFor(x => x.PersonalNameScore).NotEmpty();
    }
}
