﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorizationDetailsChanges;

[BsonIgnoreExtraElements]
[MongoCollection("accountAuthorizationsDetailsChanges")]
public class AccountAuthorizationDetailsChangesDocument : Document
{
    [BsonElement("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    [BsonElement("companyId")]
    public string CompanyId { get; set; } = string.Empty;

    [BsonElement("einHash")]
    public string EinHash { get; set; } = string.Empty;

    [BsonElement("executionId")]
    public string ExecutionId { get; set; } = string.Empty;

    [BsonElement("accountAuthorizationDetailsId")]
    public string AccountAuthorizationDetailsId { get; set; } = string.Empty;

    [BsonElement("creditApplicationId")]
    public string CreditApplicationId { get; set; } = string.Empty;

    [BsonElement("key")]
    public string Key { get; set; } = string.Empty;

    [BsonElement("oldValue")]
    public object? OldValue { get; set; }

    [BsonElement("newValue")]
    public object? NewValue { get; set; }

    [BsonElement("integration")]
    public string? Integration { get; set; } = string.Empty;

    [BsonElement("changeType")]
    public string? ChangeType { get; set; } = string.Empty;
}