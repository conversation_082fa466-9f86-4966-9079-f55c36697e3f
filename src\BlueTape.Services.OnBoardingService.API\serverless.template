{"AWSTemplateFormatVersion": "2010-09-09", "Transform": "AWS::Serverless-2016-10-31", "Description": "An AWS Serverless Application that uses the ASP.NET Core framework running in Amazon Lambda.", "Parameters": {}, "Conditions": {}, "Resources": {"AspNetCoreFunction": {"Type": "AWS::Serverless::Function", "Properties": {"Handler": "BlueTape.Services.OnBoardingService.API", "Runtime": "dotnet6", "CodeUri": "", "MemorySize": 256, "Timeout": 30, "Role": null, "Policies": ["AWSLambda_FullAccess"], "Events": {"ProxyResource": {"Type": "Api", "Properties": {"Path": "/{proxy+}", "Method": "ANY"}}, "RootResource": {"Type": "Api", "Properties": {"Path": "/", "Method": "ANY"}}}}}}, "Outputs": {"ApiURL": {"Description": "API endpoint URL for Prod environment", "Value": {"Fn::Sub": "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod"}}}}