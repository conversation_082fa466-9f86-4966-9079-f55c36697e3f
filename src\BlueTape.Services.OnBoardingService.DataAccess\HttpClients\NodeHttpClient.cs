﻿using BlueTape.Services.OnBoardingService.DataAccess.Constants;
using BlueTape.Services.OnBoardingService.DataAccess.Extensions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.HttpClients;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.DataAccess.HttpClients
{
    public class NodeHttpClient : INodeHttpClient
    {
        public HttpClient Client { get; private set; }

        public NodeHttpClient(HttpClient client, IConfiguration configuration)
        {
            Client = client;
            if (EnvironmentExtensions.IsDevelopment()) return;
            var apiKey = configuration.GetSection(NodeServiceConstants.NodeServiceApiKey).Value;
            Client.DefaultRequestHeaders.Add(HttpConstants.ApiKeyHeader, apiKey);
        }
    }
}
