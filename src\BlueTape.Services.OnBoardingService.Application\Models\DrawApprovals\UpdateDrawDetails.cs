using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class UpdateDrawDetails
{
    public DateOnly? LoansLastDefaultedDate { get; set; }

    public int? MaxPastDueDays { get; set; }

    public Decimal? CurrentCreditLimitPercentage { get; set; }
    
    public decimal? CreditLimitPercentageWithCurrentDraw { get; set; }

    public BlueTape.OBS.Enums.CreditPurchaseType? CreditPurchaseType { get; set; }

    public Decimal? CreditAvailableBalance { get; set; }

    public Decimal? ProjectAvailableBalance { get; set; }

    public Decimal? ProjectContractValue { get; set; }

    public DateOnly? ProjectEndDate { get; set; }

    public Decimal? OutstandingBalance { get; set; }

    public CreditStatus? AccountStatus { get; set; }

    public BlueTape.OBS.Enums.ProjectApprovalStatus? ProjectApprovalStatus { get; set; }
}