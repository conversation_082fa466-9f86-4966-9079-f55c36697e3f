﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;

public class AccountAuthorization
{
    public string? Id { get; set; }

    public string CreatedBy { get; set; } = string.Empty;

    public DateTime? CreatedAt { get; set; }

    public string UpdatedBy { get; set; } = string.Empty;

    public DateTime? UpdatedAt { get; set; }

    public string CompanyId { get; set; } = string.Empty;

    public string EinHash { get; set; } = string.Empty;

    public bool? IsSentBack { get; set; }
    public string[]? CreditApplicationIds { get; set; }
    public BusinessDetails BusinessDetails { get; set; } = new();

    public IEnumerable<OwnersDetails> OwnersDetails { get; set; } = Enumerable.Empty<OwnersDetails>();

    public IEnumerable<BankAccountDetails> BankAccountDetails { get; set; } = Enumerable.Empty<BankAccountDetails>();

    public IEnumerable<OwnersEntitiesDetails> OwnersEntitiesDetails { get; set; } = Enumerable.Empty<OwnersEntitiesDetails>();

    public AccountAuthorizationCreditDetails CreditDetails { get; set; } = new();

    public AccountAuthorizationFactoringOverallDetails FactoringOverallDetails { get; set; } = new();
}