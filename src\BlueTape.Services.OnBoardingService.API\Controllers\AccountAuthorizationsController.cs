﻿using AutoMapper;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.AccountAuthorization;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers
{
    [Route(ControllersConstants.AccountAuthorizations)]
    [ApiController]
    public class AccountAuthorizationsController : ControllerBase
    {
        private readonly IAccountAuthorizationsService _accountAuthorizationsService;
        private readonly IMapper _mapper;
        private readonly IValidator<CreateAccountAuthorizationDto> _createAccountAuthorizationViewModelValidator;
        private readonly IValidator<UpdateAccountAuthorizationDto> _updateAccountAuthorizationViewModelValidator;

        public AccountAuthorizationsController(IAccountAuthorizationsService accountAuthorizationsService,
            IMapper mapper,
            IValidator<CreateAccountAuthorizationDto> createAccountAuthorizationViewModelValidator,
            IValidator<UpdateAccountAuthorizationDto> updateAccountAuthorizationViewModelValidator)
        {
            _accountAuthorizationsService = accountAuthorizationsService;
            _mapper = mapper;
            _createAccountAuthorizationViewModelValidator = createAccountAuthorizationViewModelValidator;
            _updateAccountAuthorizationViewModelValidator = updateAccountAuthorizationViewModelValidator;
        }

        /// <summary>
        /// Array of account authorizations by different filters 
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /AccountAuthorizations?Id=650b28825f07d3ca092f294a,
        ///     GET /AccountAuthorizations?CompanyId=634fd62d23059967a18b1da3,
        ///     GET /AccountAuthorizations?EinHash=5fc7cb7fc5f00ee425c5d2d689c8383f
        ///     GET /AccountAuthorizations?Id=650b28825f07d3ca092f294a
        ///     GET /AccountAuthorizations?Id=650b28825f07d3ca092f294a&amp;CompanyId=634fd62d23059967a18b1da3
        ///     GET /AccountAuthorizations?Id=650b28825f07d3ca092f294a&amp;CompanyId=634fd62d23059967a18b1da3&amp;EinHash=5fc7cb7fc5f00ee425c5d2d689c8383f
        ///     
        /// </remarks>
        /// <returns>Array of account authorizations</returns>
        [HttpGet]
        public async Task<IEnumerable<AccountAuthorizationDto>> Get([FromQuery] GetAccountAuthorizationQuery query,
            CancellationToken ct)
        {
            if (query.Id is null && query.CompanyId is null && query.EinHash is null && query.SsnHash is null)
            {
                return _mapper.Map<IEnumerable<AccountAuthorizationDto>>(await _accountAuthorizationsService.GetAll(ct));
            }

            return _mapper.Map<IEnumerable<AccountAuthorizationDto>>(await _accountAuthorizationsService.GetAllByFilters(query.Id, query.CompanyId, query.EinHash, query.SsnHash, ct));
        }

        /// <summary>
        /// Create an account authorization
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /AccountAuthorizations
        ///     {
        ///         "companyId": "string",
        ///         "einHash": "string"
        ///     }
        /// 
        /// </remarks>
        [HttpPost]
        public async Task<AccountAuthorizationDto> Create([FromBody] CreateAccountAuthorizationDto model, CancellationToken ct)
        {
            await _createAccountAuthorizationViewModelValidator.ValidateAndThrowAsync(model, ct);

            var createModel = _mapper.Map<CreateAccountAuthorization>(model);

            return _mapper.Map<AccountAuthorizationDto>(await _accountAuthorizationsService.Create(createModel, ct));
        }

        /// <summary>
        /// Get an account authorization by Id
        /// </summary>
        /// <param name="id">The account authorization id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /AccountAuthorizations/650b28825f07d3ca092f294a
        /// 
        /// </remarks>
        [HttpGet(EndpointConstants.Id)]
        public async Task<AccountAuthorizationDto> GetById([FromRoute] string id,
            CancellationToken ct)
        {
            return _mapper.Map<AccountAuthorizationDto>(await _accountAuthorizationsService.GetById(id, ct));
        }

        /// <summary>
        /// Patch an account authorization by Id
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /AccountAuthorizations/650b28825f07d3ca092f294a
        /// 
        /// </remarks>
        [HttpPut(EndpointConstants.Id)]
        public async Task<AccountAuthorizationDto> Update([FromRoute] string id, [FromBody] UpdateAccountAuthorizationDto model, CancellationToken ct)
        {
            await _updateAccountAuthorizationViewModelValidator.ValidateAndThrowAsync(model, ct);

            var updateModel = _mapper.Map<UpdateAccountAuthorization>(model);
            updateModel.Id = id;

            return _mapper.Map<AccountAuthorizationDto>(await _accountAuthorizationsService.Update(updateModel, ct));
        }

        /// <summary>
        /// Patch an account authorization by Id
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /AccountAuthorizations/650b28825f07d3ca092f294a
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.Id)]
        public async Task<AccountAuthorizationDto> Patch([FromRoute] string id, [FromBody] PatchAccountAuthorizationsDto model, CancellationToken ct)
        {
            var patchModel = _mapper.Map<PatchAccountAuthorization>(model);
            patchModel.Id = id;

            return _mapper.Map<AccountAuthorizationDto>(await _accountAuthorizationsService.Patch(patchModel, ct));
        }

        [HttpPatch($"{EndpointConstants.Id}/nullify")]
        public async Task<AccountAuthorizationDto> Nullify([FromRoute] string id, [FromBody] NullifyAccountAuthorizationDto dto, CancellationToken ct)
            => _mapper.Map<AccountAuthorizationDto>(await _accountAuthorizationsService.Nullify(id, _mapper.Map<NullifyAccountAuthorization>(dto), ct));

        [HttpPost($"{EndpointConstants.GetByEinList}")]
        public async Task<IEnumerable<AccountAuthorizationDto>> GetByEinList([FromBody] GetByEinListDto dto, CancellationToken ct)
            => _mapper.Map<IEnumerable<AccountAuthorizationDto>>(await _accountAuthorizationsService.GetByEinList(dto.EinListHashes, ct));

        [HttpPost($"{EndpointConstants.GetBySsnList}")]
        public async Task<IEnumerable<AccountAuthorizationDto>> GetBySsnList([FromBody] SsnListDto dto, CancellationToken ct)
            => _mapper.Map<IEnumerable<AccountAuthorizationDto>>(await _accountAuthorizationsService.GetBySsnList(dto.SsnListHashes, ct));
    }
}