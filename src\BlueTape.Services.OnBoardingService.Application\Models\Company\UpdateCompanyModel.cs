namespace BlueTape.Services.OnBoardingService.Application.Models.Company;

public class UpdateCompanyModel
{
    public bool? ApproveRead { get; set; }
    public string? BankAccountStatus { get; set; }
    public string? Status { get; set; }
    public string? LegalName { get; set; }
    public string? Name { get; set; }
    
    public int? Limit { get; set; }

    public bool? AcceptAchPayment { get; set; }

    public string? PurchaseType { get; set; }
}