﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class DrawDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("loansLastDefaultedDate")]
    public DateTime? LoansLastDefaultedDate { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("maxPastDueDays")]
    public int? MaxPastDueDays { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("creditLimit")]
    public decimal? CreditLimit { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("currentCreditLimitPercentage")]
    public decimal? CurrentCreditLimitPercentage { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("creditLimitPercentageWithCurrentDraw")]
    public decimal? CreditLimitPercentageWithCurrentDraw { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("creditPurchaseType")]
    public string? CreditPurchaseType { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("creditAvailableBalance")]
    public decimal? CreditAvailableBalance { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("projectAvailableBalance")]
    public decimal? ProjectAvailableBalance { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("projectContractValue")]
    public decimal? ProjectContractValue { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("projectEndDate")]
    public DateTime? ProjectEndDate { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("outstandingBalance")]
    public decimal? OutstandingBalance { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("accountStatus")]
    public string? AccountStatus { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("projectApprovalStatus")]
    public string? ProjectApprovalStatus { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("pastDueAmount")]
    public decimal? PastDueAmount { get; set; }
}
