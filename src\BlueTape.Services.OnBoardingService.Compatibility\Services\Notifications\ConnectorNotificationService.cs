﻿using BlueTape.AWSMessaging.Abstractions;
using BlueTape.AWSMessaging.SQSCommands;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.ConnectorNotifications;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Notifications;

public class ConnectorNotificationService(
    ITraceIdAccessor traceIdAccessor,
    ILogger<ConnectorNotificationService> logger,
    ISqsEndpoint<ConnectorMessageBody> sqsEndpoint)
    : IConnectorNotificationService
{
    public async Task SendInvoiceMessageAsync(string id, ConnectorOperationType operationType, CancellationToken ctx)
    {
        try
        {
            var attributes = new SqsMessageAttributes
            {
                MessageGroupId = id,
                MessageDeduplicationId = Guid.NewGuid().ToString()
            };

            var body = new ConnectorMessageBody
            {
                Id = id,
                OperationType = operationType,
                Status = GetInvoiceStatus(operationType)
            };

            await sqsEndpoint.SendAsync(body, attributes, traceIdAccessor.TraceId, ctx);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Message sending has been failed. Id: {id} operationType: {operationType}", id, operationType);
        }
    }

    private static string? GetInvoiceStatus(ConnectorOperationType operationType)
    {
        if (operationType == ConnectorOperationType.LoanApplicationCanceled)
            return InvoiceStatus.CANCELLED.ToString();

        return null;
    }
}