﻿using System.Text.Json.Serialization;

namespace BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;

public class CreditApplicationInitializationStepStartRequest
{
    [JsonPropertyName("draftId")]
    public string? DraftId { get; set; } = string.Empty;

    [JsonPropertyName("companyId")]
    public string? CompanyId { get; set; } = string.Empty;

    [JsonPropertyName("creditApplicationId")]
    public string CreditApplicationId { get; set; } = string.Empty;

    [JsonPropertyName("scheduledUpdate")]
    public string ScheduledUpdate { get; set; } = string.Empty;

    [JsonPropertyName("scheduleMode")]
    public string ScheduleMode { get; set; } = OBS.Enums.ScheduleMode.CreateNew.ToString();

    [JsonPropertyName("jobId")]
    public Guid JobId { get; set; }

    [JsonPropertyName("merchantId")]
    public string? MerchantId { get; set; }

    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("correlationId")]
    public string CorrelationId { get; set; } = string.Empty;

    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;
}