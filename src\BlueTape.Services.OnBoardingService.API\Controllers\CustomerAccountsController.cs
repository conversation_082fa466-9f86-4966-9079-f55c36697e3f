using BlueTape.OBS.DTOs.CustomerAccounts;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.CustomerAccounts)]
[ApiController]
public class CustomerAccountsController(ICustomerAccountsService userService) : ControllerBase
{
    [HttpGet($"{EndpointConstants.Company}/{EndpointConstants.Id}")]
    public async Task<CustomerAccountIdsResponse> GetByCompanyIdsCustomerAccountId([FromRoute] string id, CancellationToken ct)
        => new() { Ids = await userService.GetCustomerAccountIdsByCompanyId(id, ct) };
}