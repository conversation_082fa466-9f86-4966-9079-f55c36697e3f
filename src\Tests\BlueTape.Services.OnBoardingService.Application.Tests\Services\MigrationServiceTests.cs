using System.Linq.Expressions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class MigrationServiceTests
{
    private readonly Mock<ICreditApplicationService> _creditApplicationServiceMock = new();
    private readonly Mock<IDraftService> _draftServiceMock = new();
    private readonly Mock<ICompanyHttpClient> _companyServiceMock = new();
    private readonly Mock<ICompanyRepository> _companyRepositoryMock = new();
    private readonly Mock<IAccountAuthorizationsService> _accountAuthorizationsServiceMock = new();
    private readonly Mock<IUserRepository> _userRepositoryMock = new();
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock = new();
    private readonly Mock<ICardPricingPackageRepository> _cardPricingPackageRepositoryMock = new();
    private readonly Mock<ILoanPricingPackageRepository> _loanPricingPackageRepositoryMock = new();
    private readonly Mock<IDecisionEngineStepsRepository> _decisionEngineStepsRepositoryMock = new();
    private readonly Mock<IDrawApprovalRepository> _drawApprovalRepositoryMock = new();
    private readonly Mock<ILogger<MigrationService>> _loggerMock = new();

    private readonly MigrationService _service;

    public MigrationServiceTests()
    {
        _service = new MigrationService(
            _creditApplicationServiceMock.Object,
            _draftServiceMock.Object,
            _companyServiceMock.Object,
            _companyRepositoryMock.Object,
            _accountAuthorizationsServiceMock.Object,
            _userRepositoryMock.Object,
            _creditApplicationRepositoryMock.Object,
            _cardPricingPackageRepositoryMock.Object,
            _loanPricingPackageRepositoryMock.Object,
            _decisionEngineStepsRepositoryMock.Object,
            _drawApprovalRepositoryMock.Object,
            _loggerMock.Object
        );
    }

    [Fact]
    public async Task MigrateExecutionType_ShouldMigrate()
    {
        _decisionEngineStepsRepositoryMock.Setup(x => x.MigrateExecutionType(default));
        await _service.MigrateDecisionEngineExecutionType(default);
        _decisionEngineStepsRepositoryMock.Verify(x => x.MigrateExecutionType(default), Times.Once);
    }

    [Fact]
    public async Task MigrateExecutionIdForDrawApprovals()
    {
        var executionId = "executionId";
        var drawApprovalId = "drawApprovalId";
        var drawApprovals = new List<DrawApprovalDocument>()
        {
            new() { Id = drawApprovalId, ExecutionId = null }
        };

        var steps = new List<DecisionEngineStepsDocument>()
        {
            new() { DrawApprovalId = drawApprovalId, ExecutionId = executionId }
        };
        
        _drawApprovalRepositoryMock.Setup(x => x.GetAll(It.IsAny<Expression<Func<DrawApprovalDocument, bool>>>(),default)).ReturnsAsync(drawApprovals);
        _decisionEngineStepsRepositoryMock.Setup(x => x.GetByDrawApprovalIds(new[]{drawApprovalId}, default)).ReturnsAsync(steps);
        _drawApprovalRepositoryMock.Setup(x => x.Update(It.Is<DrawApprovalDocument>(i => i.Id == drawApprovalId && i.ExecutionId == executionId), default));

        await _service.MigrateExecutionIdForDrawApprovals(default);
        
        _drawApprovalRepositoryMock.Verify(x => x.GetAll(It.IsAny<Expression<Func<DrawApprovalDocument, bool>>>(), default), Times.Once());
        _decisionEngineStepsRepositoryMock.Verify(x => x.GetByDrawApprovalIds(new[]{drawApprovalId}, default), Times.Once);
        _drawApprovalRepositoryMock.Verify(x => x.Update(It.Is<DrawApprovalDocument>(i => i.Id == drawApprovalId && i.ExecutionId == executionId), default), Times.Once);
    }

    [Fact]
    public async Task MigrateExecutionIdForCreditApplications()
    {
        var executionId = "executionId";
        var creditApplicationId = "creditApplicationId";
        var creditApplication = new List<CreditApplicationDocument>()
        {
            new() { Id = creditApplicationId, ExecutionId = null }
        };

        var steps = new List<DecisionEngineStepsDocument>()
        {
            new() { CreditApplicationId = creditApplicationId, ExecutionId = executionId }
        };
        
        _creditApplicationRepositoryMock.Setup(x => x.GetAll(It.IsAny<Expression<Func<CreditApplicationDocument, bool>>>(),default)).ReturnsAsync(creditApplication);
        _decisionEngineStepsRepositoryMock.Setup(x => x.GetByCreditApplicationIds(new[]{creditApplicationId}, default)).ReturnsAsync(steps);
        _creditApplicationRepositoryMock.Setup(x => x.Update(It.Is<CreditApplicationDocument>(i => i.Id == creditApplicationId && i.ExecutionId == executionId), default));

        await _service.MigrateExecutionIdForCreditApplications(default);
        
        _creditApplicationRepositoryMock.Verify(x => x.GetAll(It.IsAny<Expression<Func<CreditApplicationDocument, bool>>>(), default), Times.Once());
        _decisionEngineStepsRepositoryMock.Verify(x => x.GetByCreditApplicationIds(new[]{creditApplicationId}, default), Times.Once);
        _creditApplicationRepositoryMock.Verify(x => x.Update(It.Is<CreditApplicationDocument>(i => i.Id == creditApplicationId && i.ExecutionId == executionId), default), Times.Once);
    }
}
