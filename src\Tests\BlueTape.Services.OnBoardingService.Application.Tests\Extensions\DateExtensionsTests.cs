using BlueTape.Services.OnBoardingService.Application.Extensions;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Extensions;
using Shouldly;
using Xunit;

public class DateExtensionsTests
{
    [Fact]
    public void ConvertToCstDate_ValidDateTime_ReturnsValidDate()
    {
        var dateTime = new DateTime(2024, 08, 01, 08, 00,00, DateTimeKind.Utc);
        var expectedDate = new DateOnly(2024, 08, 01);
        var result = dateTime.ToCstDate();
        result.ShouldBe(expectedDate);
    }
    
    [Fact]
    public void ConvertToCstDate_InvalidDateTime_ReturnsValidDate()
    {
        var dateTime = DateTime.MinValue;
        var expectedDate = DateOnly.MinValue;
        var result = dateTime.ToCstDate();
        result.ShouldBe(expectedDate);
    }
}