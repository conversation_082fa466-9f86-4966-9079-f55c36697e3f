﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Extensions;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Extensions;

public class CreditApplicationExtensions
{
    [Theory]
    [InlineData(CreditApplicationType.LineOfCredit, "loc")]
    [InlineData(CreditApplicationType.ARAdvance, "aradvance")]
    [InlineData(CreditApplicationType.InHouseCredit, "ihc")]
    [InlineData(null, "loc")]
    public static void GetShortType_CreditApplicationType_ReturnsCorrectValue(CreditApplicationType type, string expectedResult)
    {
        var actualResult = type.GetShortType();

        expectedResult.ShouldBe(actualResult);
    }
}
