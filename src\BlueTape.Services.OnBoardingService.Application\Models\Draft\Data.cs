﻿namespace BlueTape.Services.OnBoardingService.Application.Models.Draft;

public class Data
{
    // General application
    public BusinessInfo? BusinessInfo { get; set; }

    public BusinessOwner? BusinessOwner { get; set; }

    public Finance? Finance { get; set; }

    public Bank? Bank { get; set; }

    public CoOwnerInfo? CoOwnerInfo { get; set; }

    public PersonalInfo? PersonalInfo { get; set; }

    // Supplier application

    public Business? Business { get; set; }
    public Ownership? Ownership { get; set; }
    public Registered? Registered { get; set; }
}