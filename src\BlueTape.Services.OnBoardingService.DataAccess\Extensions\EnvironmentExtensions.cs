﻿using BlueTape.Services.OnBoardingService.DataAccess.Constants;

namespace BlueTape.Services.OnBoardingService.DataAccess.Extensions
{
    public static class EnvironmentExtensions
    {
        public static bool IsDevelopment()
        {
            var currentEnv = Environment.GetEnvironmentVariable(ApplicationConstants.AspNetCoreEnvironmentVariable);
            return currentEnv is null or ApplicationConstants.DevelopmentEnvironment;
        }
    }
}
