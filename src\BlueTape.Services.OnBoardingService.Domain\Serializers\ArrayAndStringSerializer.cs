﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization;

namespace BlueTape.Services.OnBoardingService.Domain.Serializers;

public class ArrayAndStringSerializer : IBsonSerializer
{
    public Type ValueType { get; } = typeof(List<string?>);

    public object? Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        switch (context.Reader.CurrentBsonType)
        {
            case BsonType.Array:
                var result = new List<string>();
                context.Reader.ReadStartArray();

                while (context.Reader.ReadBsonType() != BsonType.EndOfDocument)
                {
                    result.Add(context.Reader.ReadString());
                }

                context.Reader.ReadEndArray();

                return result;
            case BsonType.String:
                return GetStringAsStringList(context);
            case BsonType.Null:
                context.Reader.ReadNull();
                return null;
            default:
                throw new NotImplementedException($"No implementation to deserialize {context.Reader.CurrentBsonType}");
        }
    }

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object? value)
    {
        if (value is null) context.Writer.WriteNull();
        else
        {
            var idsList = (List<string>)value;
            context.Writer.WriteStartArray();
            idsList.ForEach(id => context.Writer.WriteString(id));
            context.Writer.WriteEndArray();
        }
    }

    private static object GetStringAsStringList(BsonDeserializationContext context)
    {
        var value = context.Reader.ReadString();

        return new List<string>() { value };
    }
}