﻿using BlueTape.Services.OnBoardingService.Domain.Documents.Configuration;

namespace BlueTape.Services.OnBoardingService.DataAccess.Abstractions
{
    public interface IAuthorizationDetailsRefreshConfigurationRepository : IGenericRepository<AuthorizationDetailsRefreshConfigurationDocument>
    {
        Task<AuthorizationDetailsRefreshConfigurationDocument> GetConfiguration(CancellationToken ct);
    }
}
