﻿using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.API.Validators.DecisionEngineSteps.CreateDecisionEngineSteps;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.API.Tests.Validators;

public class CreateDecisionEngineStepsDtoValidatorTest
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new CreateDecisionEngineStepsDtoValidator();

        var model = new CreateDecisionEngineStepsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            Step = "step"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }


    [Fact]
    public void Validate_InvalidModelMissedCreditApplicationId_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsDtoValidator();

        var model = new CreateDecisionEngineStepsDto()
        {
            ExecutionId = "executionId",
            PolicyVersion = "ver 1",
            Step = "step"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_InvalidModelMissedExecutionId_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsDtoValidator();

        var model = new CreateDecisionEngineStepsDto()
        {
            CreditApplicationId = "appId",
            PolicyVersion = "ver 1",
            Step = "step"
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_InvalidModelMissedPolicyVersion_ReturnsFalse()
    {
        var validator = new CreateDecisionEngineStepsDtoValidator();

        var model = new CreateDecisionEngineStepsDto()
        {
            CreditApplicationId = "appId",
            ExecutionId = "executionId",
            Step = "step",
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }
}
