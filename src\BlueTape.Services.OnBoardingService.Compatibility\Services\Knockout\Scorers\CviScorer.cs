﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Extensions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;

public class CviScorer(IConfiguration config) : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        int passThreshold = config.GetValue<int?>("ScoringThresholds:СviThresholdPass") ?? 30;
        var result = new List<OwnerScore>();

        if (kyb?.KYC != null && kyb.KYC.Any())
        {
            foreach (var item in kyb.KYC)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                int.TryParse(item.CVI, out var cvi);

                ownerScore.Scores?.Add(KnockoutCalculationExtension.Calculate("CVI", cvi, passThreshold));

                result.Add(ownerScore);
            }

            return result;
        }

        var score = KnockoutCalculationExtension.Calculate("CVI", kyb?.CVI, passThreshold);
        return [new OwnerScore { Scores = [score] }];
    }
}
