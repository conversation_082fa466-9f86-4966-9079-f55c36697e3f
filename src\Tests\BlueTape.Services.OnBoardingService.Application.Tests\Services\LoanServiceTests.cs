﻿using AutoMapper;
using BlueTape.LS.DTOs.Credit;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Models.Credit;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.LMS.Abstractions.ExternalServices;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class LoanServiceTests
{
    private readonly ILoanService _loanService;
    private readonly Mock<ILoanExternalService> _loanRepositoryMock = new();

    public LoanServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });

        IMapper mapper = new Mapper(mapperConfig);
        _loanService = new LoanService(_loanRepositoryMock.Object, mapper);
    }

    [Theory, CustomAutoData]
    public async Task GetById_DraftIsExist_ReturnsDraft(CreateCreditModel createModel, CreditDto dto)
    {
        _loanRepositoryMock.Setup(x => x.CreateCredit(It.IsAny<CreateCreditDto>(), default)).ReturnsAsync(dto);

        var result = await _loanService.CreateCredit(createModel, default);

        result.CompanyId.ShouldBe(dto.CompanyId);
        result.CreditApplicationId.ShouldBe(dto.CreditApplicationId);
        _loanRepositoryMock.Verify(service => service.CreateCredit(It.IsAny<CreateCreditDto>(), default), Times.Once);
    }
}
