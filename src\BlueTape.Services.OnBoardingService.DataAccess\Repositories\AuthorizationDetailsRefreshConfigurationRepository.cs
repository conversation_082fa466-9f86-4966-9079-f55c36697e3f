﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories
{
    public class AuthorizationDetailsRefreshConfigurationRepository(
        IObsMongoDBContext context,
        ILogger<GenericRepository<AuthorizationDetailsRefreshConfigurationDocument>> logger)
        : GenericRepository<AuthorizationDetailsRefreshConfigurationDocument>(context, logger), IAuthorizationDetailsRefreshConfigurationRepository
    {
        public Task<AuthorizationDetailsRefreshConfigurationDocument> GetConfiguration(CancellationToken ct)
        {
            return Collection.Find(x => true).FirstOrDefaultAsync(ct);
        }
    }
}
