﻿using AutoMapper;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.CreditApplicationStatusChange;

public abstract class ChangeCreditApplicationStatusStrategy
{
    protected readonly IDateProvider DateProvider;
    protected readonly IMapper Mapper;
    protected readonly IAccountStatusService AccountStatusService;
    protected readonly ICreditApplicationRepository CreditApplicationRepository;
    protected readonly ICreditApplicationSyncService CreditApplicationSyncService;
    protected readonly ICreditApplicationNotesService CreditApplicationNotesService;
    protected readonly ILogger<ChangeCreditApplicationStatusStrategy> Logger;

    protected ChangeCreditApplicationStatusStrategy(IDateProvider dateProvider,
        IMapper mapper,
        IAccountStatusService accountStatusService,
        ICreditApplicationRepository creditApplicationRepository,
        ICreditApplicationSyncService creditApplicationSyncService,
        ICreditApplicationNotesService creditApplicationNotesService,
        ILogger<ChangeCreditApplicationStatusStrategy> logger)
    {
        DateProvider = dateProvider;
        Mapper = mapper;
        AccountStatusService = accountStatusService;
        CreditApplicationRepository = creditApplicationRepository;
        CreditApplicationSyncService = creditApplicationSyncService;
        CreditApplicationNotesService = creditApplicationNotesService;
        Logger = logger;
    }

    public abstract bool IsApplicable(string status);

    protected static bool ShouldStatusNoteBeGenerated(CreditApplicationDocument creditApplication)
    {
        return CreditApplicationType.GetPaid.IsEnum(creditApplication.Type) || CreditApplicationType.ARAdvance.IsEnum(creditApplication.Type);
    }

    protected async Task GenerateStatusNote(CreditApplicationDocument creditApplication, string? note, string userId, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(note))
        {
            Logger.LogInformation("Started generating status note for credit application {creditApplicationId}", creditApplication.Id);

            await CreditApplicationNotesService.Add(new CreateCreditApplicationNote()
            {
                CreatedAt = DateProvider.CurrentDateTime,
                CreatedBy = userId,
                CreditApplicationId = creditApplication.Id,
                Note = note
            }, ct);

            Logger.LogInformation("Generated status note for credit application {creditApplicationId}", creditApplication.Id);
        }
    }

    public abstract Task<CreditApplication> ChangeStatus(CreditApplicationDocument creditApplicationDocument,
        ReviewCreditApplicationDto reviewCreditApplication, string userId, CancellationToken ct);

    //Needed for moq lib to be able to create mock-instance of the class
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    // ReSharper disable once PublicConstructorInAbstractClass
    public ChangeCreditApplicationStatusStrategy()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    {

    }
}
