﻿namespace BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
public class OwnerDetailsBase
{
    public string Id { get; set; } = string.Empty;

    public string Identifier { get; set; } = string.Empty;

    public decimal PercentOwned { get; set; }

    public string? Phone { get; set; }

    public string? Email { get; set; }

    public AddressModel Address { get; set; } = new();

    public bool IsPrincipal { get; set; }


    public string? InquiriesDuringLast6Months { get; set; }
}
