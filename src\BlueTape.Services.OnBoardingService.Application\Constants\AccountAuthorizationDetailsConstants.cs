﻿using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using System.Collections.Immutable;

namespace BlueTape.Services.OnBoardingService.Application.Constants;

public static class AccountAuthorizationDetailsConstants
{
    private const string OnBoardingServiceIntegration = "OnBoardingService";
    private const string Internal = "Internal";
    private const string LoanServiceIntegration = "LoanService";
    private const string LexisNexisIntegration = "LexisNexis";
    private const string ExperianIntegration = "Experian";
    private const string CashFlowIntegration = "CashFlow";

    public const string IdentifierFieldName = "Identifier";
    public const string BankAccountIdFieldName = "Id";

    public static readonly ImmutableDictionary<string[], string> BusinessDetailsIntegrations = new Dictionary<string[], string>()
    {
        {new[] {nameof(BusinessDetailsDocument.LastEINRejectionDate),
                nameof(BusinessDetailsDocument.BusinessStartDate)},
                OnBoardingServiceIntegration},
        {new[] {nameof(BusinessDetailsDocument.LoansLastDefaultedDate),
                nameof(BusinessDetailsDocument.PastDueAmount),
                nameof(BusinessDetailsDocument.LoansLastDefaultedDate),
                nameof(BusinessDetailsDocument.LoanRevenue),
                nameof(BusinessDetailsDocument.BusinessOutstandingBalance)
            },
                LoanServiceIntegration},
        {new[] {nameof(BusinessDetailsDocument.BRICodes),
                nameof(BusinessDetailsDocument.BVI)},
                LexisNexisIntegration},
        {new[] {nameof(BusinessDetailsDocument.ReliabilityCode),
                nameof(BusinessDetailsDocument.FirstReportedTradeLineDate),
                nameof(BusinessDetailsDocument.BankruptcyIndicator),
                nameof(BusinessDetailsDocument.LastBankruptcyDate),
                nameof(BusinessDetailsDocument.JudgmentBalance),
                nameof(BusinessDetailsDocument.JudgmentIndicator),
                nameof(BusinessDetailsDocument.LastJudgmentDate),
                nameof(BusinessDetailsDocument.LienBalance),
                nameof(BusinessDetailsDocument.LienIndicator),
                nameof(BusinessDetailsDocument.LastLienDate),
                nameof(BusinessDetailsDocument.DBT60PlusPercentage),
                nameof(BusinessDetailsDocument.TradeLinesPercentage),
                nameof(BusinessDetailsDocument.DBT60PlusAmount),
                nameof(BusinessDetailsDocument.TotalAcceptableDebtAmount),
                nameof(BusinessDetailsDocument.TotalTradeLines)},
                ExperianIntegration},
        {new[] {nameof(BusinessDetailsDocument.CompanyIncome),
                nameof(BusinessDetailsDocument.AnnualRevenue),
                nameof(BusinessDetailsDocument.InquiriesDuringLast6Months),
                nameof(BusinessDetailsDocument.DTI2Value),
                nameof(BusinessDetailsDocument.RevenueVariancePercentage)},
                CashFlowIntegration},
    }.ToImmutableDictionary();

    public static readonly ImmutableDictionary<string[], string> OwnersDetailsIntegrations = new Dictionary<string[], string>()
    {
        {new[] {nameof(OwnersDetailsDocument.Identifier),
                nameof(OwnersDetailsDocument.IsPrincipal),
                nameof(OwnersDetailsDocument.PercentOwned),
                nameof(OwnersDetailsDocument.Phone),
                nameof(OwnersDetailsDocument.LastName),
                nameof(OwnersDetailsDocument.SsnHash),
                nameof(OwnersDetailsDocument.FirstName),
                nameof(OwnersDetailsDocument.Birthday),
                nameof(OwnersDetailsDocument.LastSSNRejectionDate),
                nameof(OwnersDetailsDocument.Address),
                nameof(OwnersDetailsDocument.Email)},
                OnBoardingServiceIntegration},
        {new[] {nameof(OwnersDetailsDocument.B2ELinkIndex),
                nameof(OwnersDetailsDocument.CRICodes),
                nameof(OwnersDetailsDocument.CVI),
                nameof(OwnersDetailsDocument.FraudpointScore),
                nameof(OwnersDetailsDocument.IPRiskLevel),
                nameof(OwnersDetailsDocument.DomainRiskLevel),
                nameof(OwnersDetailsDocument.EmailRiskScore)},
                LexisNexisIntegration},
        {new[] {nameof(OwnersDetailsDocument.LastPersonalBankruptcyDate),
                nameof(OwnersDetailsDocument.FICOScore),
                nameof(OwnersDetailsDocument.InquiriesDuringLast6Months)},
                ExperianIntegration}
    }.ToImmutableDictionary();

    public static readonly ImmutableDictionary<string[], string> BankAccountsDetailsIntegrations = new Dictionary<string[], string>()
    {
        {new[] {nameof(BankAccountDetailsDocument.Identifier),
                nameof(BankAccountDetailsDocument.Id),
                nameof(BankAccountDetailsDocument.Name),
                nameof(BankAccountDetailsDocument.Type)},
                OnBoardingServiceIntegration},
        {new[] {nameof(BankAccountDetailsDocument.AddressScore),
                nameof(BankAccountDetailsDocument.CompanyAddressScore),
                nameof(BankAccountDetailsDocument.CompanyNameScore),
                nameof(BankAccountDetailsDocument.PersonalAddressScore),
                nameof(BankAccountDetailsDocument.NameScore),
                nameof(BankAccountDetailsDocument.PersonalNameScore)},
                Internal}
    }.ToImmutableDictionary();
}
