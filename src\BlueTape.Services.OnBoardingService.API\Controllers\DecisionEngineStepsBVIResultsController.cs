﻿using AutoMapper;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.API.Constants;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DecisionEngineSteps;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.OnBoardingService.API.Controllers;

[Route(ControllersConstants.DecisionEngineStepsBVIResults)]
[ApiController]
public class DecisionEngineStepsBviResultsController
{
    private readonly IDecisionEngineStepsBviResultsService _decisionEngineStepsBviResultsService;
    private readonly IValidator<IEnumerable<CreateDecisionEngineStepsBVIResultsDto>> _createDecisionEngineStepsRangeBVIResultsDtoValidator;
    private readonly IMapper _mapper;

    public DecisionEngineStepsBviResultsController(
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        IValidator<IEnumerable<CreateDecisionEngineStepsBVIResultsDto>> createDecisionEngineStepsRangeBVIResultsDtoValidator,
        IMapper mapper)
    {
        _decisionEngineStepsBviResultsService = decisionEngineStepsBviResultsService;
        _createDecisionEngineStepsRangeBVIResultsDtoValidator = createDecisionEngineStepsRangeBVIResultsDtoValidator;
        _mapper = mapper;
    }

    /// <summary>
    /// Array of credit Decision Engine Steps BVI Results by different filters 
    /// </summary>

    /// <returns>Array of Decision Engine Steps BVI Results</returns>
    [HttpGet]
    public async Task<IEnumerable<DecisionEngineStepsBVIResultsDto>> Get([FromQuery] GetDecisionEngineStepsBviResultsQuery query, CancellationToken ct)
    {
        return _mapper.Map<IEnumerable<DecisionEngineStepsBVIResultsDto>>(await _decisionEngineStepsBviResultsService.GetAllByFilters(
            query.Id, query.ExecutionId, query.IntegrationLogId, query.StepId, query.CreditApplicationId, ct));
    }

    /// <summary>
    /// Create a collection of Decision Engine Steps BVI Results
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /DecisionEngineStepsResults
    ///     [
    ///          {
    ///              "decisionEngineStepId": "string",
    ///              "integrationLogId": "string",
    ///              "integrationSource": "string",
    ///              "creditApplicationId": "string",
    ///              "executionId": "string"
    ///           }
    ///      ]
    /// 
    /// </remarks>
    [HttpPost]
    public async Task<IEnumerable<DecisionEngineStepsBVIResultsDto>> CreateRange(IEnumerable<CreateDecisionEngineStepsBVIResultsDto> createDtos, CancellationToken ct)
    {
        await _createDecisionEngineStepsRangeBVIResultsDtoValidator.ValidateAndThrowAsync(createDtos, ct);

        var createModels = _mapper.Map<IEnumerable<CreateDecisionEngineStepsBviResultsModel>>(createDtos);
        var createdModels = await _decisionEngineStepsBviResultsService.AddRange(createModels, ct);

        return _mapper.Map<IEnumerable<DecisionEngineStepsBVIResultsDto>>(createdModels);
    }
}
