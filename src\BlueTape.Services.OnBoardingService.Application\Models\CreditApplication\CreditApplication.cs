﻿using BlueTape.LS.DTOs.Credit.CreditDetails;
using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;

public class CreditApplication
{
    public string Id { get; set; } = null!;

    public string? CreatedBy { get; set; }

    public string? UpdatedBy { get; set; }

    public string? CompanyId { get; set; }

    public string? EinHash { get; set; }

    public string? DraftId { get; set; }

    public DateTime? ApplicationDate { get; set; }

    public CreditApplicationType Type { get; set; }

    public bool? IsArAdvanceRequested { get; set; }

    public string? ArAdvanceApplicationId { get; set; }

    public string? MerchantId { get; set; }

    public string? Status { get; set; }

    public string? PurchaseTypeOption { get; set; }

    public DateTime? LastStatusChangedAt { get; set; }

    public string? LastStatusChangedBy { get; set; }

    public decimal? CreditLimit { get; set; }

    public decimal? ApprovedCreditLimit { get; set; }

    public double? RevenueFallPercentage { get; set; }

    public string? BusinessName { get; set; }

    public string? BusinessDba { get; set; }

    public string? BusinessCategory { get; set; }

    public string? ApplicantName { get; set; }

    public string? SupplierName { get; set; }

    public string? ExecutionId { get; set; }

    public string? GetPaidApplicationId { get; set; }

    public DateTime? GetPaidCreatedAt { get; set; }

    public DateTime? GetPaidApplicationDate { get; set; }

    public DateTime? GetPaidApprovalDate { get; set; }

    public string? GetPaidApprovedBy { get; set; }

    public decimal? RequestedAmount { get; set; }

    public string? AutomatedDecisionResult { get; set; }

    public DateTime? ApprovedAt { get; set; }

    public string? ApprovedBy { get; set; }

    public DateTime? RejectedAt { get; set; }

    public string? RejectedBy { get; set; }
    public string? StatusCode { get; set; }
    public string? StatusNote { get; set; }
    public bool? ShouldIgnoreCaching { get; set; }
    public string? BankAccountType { get; set; }
    public MerchantSettings? MerchantSettings { get; set; }
    public bool? IsInHouseCreditEnabled { get; set; }
    public string[]? SsnHashes { get; set; }

    public CreditDetailsDto? CreditDetails { get; set; }

    public DateTime? CanceledAt { get; set; }

    public string? CanceledBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public bool? IsSecured { get; set; }

    public decimal? DepositAmount { get; set; }
}