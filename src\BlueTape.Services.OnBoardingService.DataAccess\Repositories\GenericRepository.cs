﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using System.Linq.Expressions;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories
{
    public class GenericRepository<TDocument> : IGenericRepository<TDocument> where TDocument : Document
    {
        protected readonly ILogger Logger;
        protected readonly IMongoCollection<TDocument> Collection;
        protected readonly IObsMongoDBContext Context;

        protected GenericRepository(
            IObsMongoDBContext context,
            ILogger<GenericRepository<TDocument>> logger)
        {
            Context = context;
            Logger = logger;
            Collection = context.GetCollection<TDocument>();
        }

        public async Task<TDocument> Add(TDocument entity, CancellationToken ct)
        {
            Logger.LogInformation("Add new {type} document", typeof(TDocument).ToString());

            entity.CreatedAt ??= DateTime.UtcNow;

            await Collection.InsertOneAsync(entity, cancellationToken: ct);
            return entity;
        }

        public async Task<IEnumerable<TDocument>> AddRange(IEnumerable<TDocument> documents, CancellationToken ct)
        {
            Logger.LogInformation("Add new {type} document", typeof(TDocument).ToString());

            foreach (var document in documents)
            {
                document.CreatedAt ??= DateTime.UtcNow;
            }

            await Collection.InsertManyAsync(documents, cancellationToken: ct);
            return documents;
        }

        public async Task<IEnumerable<TDocument>> GetAll(CancellationToken ct)
        {
            Logger.LogInformation("Get all {type} documents", typeof(TDocument).ToString());
            return await Collection.Find(x => true).ToListAsync(ct);
        }

        public async Task<IEnumerable<TDocument>> GetAll(Expression<Func<TDocument, bool>> predicate, CancellationToken ct)
        {
            Logger.LogInformation("Get by expression {type} documents", typeof(TDocument).ToString());
            return await Collection.Find(predicate).ToListAsync(ct);
        }

        public virtual async Task<TDocument> GetById(string id, CancellationToken ct)
        {
            Logger.LogInformation("Get by id {type} documents, id: {id}", typeof(TDocument).ToString(), id);

            var result = await Collection.Find(x => x.Id == id).FirstOrDefaultAsync(ct);
            if (result is not null) return result;

            Logger.LogError("Entity type of {type} with {id} not found", typeof(TDocument).ToString(), id);
            throw new VariableNullException($"Document with id {id} not found");
        }

        public virtual async Task<TDocument> Update(TDocument entity, CancellationToken ct)
        {
            Logger.LogInformation("Update {type} document, id: {id}", typeof(TDocument).ToString(), entity.Id);
            entity.UpdatedAt = DateTime.UtcNow;
            var result = await Collection.ReplaceOneAsync(x => x.Id == entity.Id, entity, cancellationToken: ct);
            Logger.LogWarning(JsonConvert.SerializeObject(result));
            return entity;
        }
    }
}
