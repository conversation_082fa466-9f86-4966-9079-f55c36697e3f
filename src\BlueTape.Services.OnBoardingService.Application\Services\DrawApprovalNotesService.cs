using AutoMapper;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApprovalNotes;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services;

public class DrawApprovalNotesService(IDrawApprovalNotesRepository repository, IMapper mapper, ILogger<DrawApprovalNotesService> logger) : IDrawApprovalNotesService
{
    public async Task<IEnumerable<DrawApprovalNote>> GetByDrawApprovalId(string id, CancellationToken ct)
    {
        logger.LogInformation("Retrieving notes for draw approval {drawApprovalId}", id);
        var result = await repository.GetByDrawApprovalId(id, ct);
        logger.LogInformation("Found {Count} notes for credit application {ApplicationId}", result.Count(), id);

        return mapper.Map<IEnumerable<DrawApprovalNote>>(result);
    }

    public async Task<DrawApprovalNote> Add(CreateDrawApprovalNote createNote, CancellationToken ct)
    {
        logger.LogInformation("Adding note {@Note} for draw approval {id}", createNote, createNote.DrawApprovalId);
        var result = await repository.Add(mapper.Map<DrawApprovalNoteDocument>(createNote), ct);
        logger.LogInformation("Added note {@Note} for draw approval {id}", result, result.DrawApprovalId);
        return mapper.Map<DrawApprovalNote>(result);
    }

    public async Task<DrawApprovalNote> Patch(PatchDrawApprovalNote patchModel, CancellationToken ct)
    {
        logger.LogInformation("Updating note {@note} for draw approval {id}", patchModel, patchModel.DrawApprovalId);
        var existingDocument = await repository.GetById(patchModel.Id, ct);
        mapper.Map(patchModel, existingDocument);
        var result = await repository.Update(existingDocument, ct);
        logger.LogInformation("Added note {@Note} for draw approval {id}", result, result.DrawApprovalId);
        return mapper.Map<DrawApprovalNote>(result);
    }

    public async Task Delete(DeleteDrawApprovalNote note, CancellationToken ct)
    {
        logger.LogInformation("Soft deleting note with id: {id}", note.Id);
        await repository.SoftDelete(note.Id, note.UserId, ct);
        logger.LogInformation("Soft deleted note with id: {id}", note.Id);
    }
}