using BlueTape.CompanyService.Companies;
using CompanyUpdateModelFromOnboarding = BlueTape.Services.OnBoardingService.Application.Models.Company.UpdateCompanyModel;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface ICompanyService
{
    Task<CompanyModel?> GetCompanyById(string id, CancellationToken ct);
    Task PatchCompanyCredit(string companyId, CompanyUpdateModelFromOnboarding model, CancellationToken ct);
    Task<List<CompanyModel>?> GetCompaniesByActiveAccounts(CancellationToken ct);
}