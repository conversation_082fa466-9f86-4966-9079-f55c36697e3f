﻿using System.Text.Json.Serialization;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
public class NoSupplierDetailsModel
{
    [JsonPropertyName("businessName")]
    public string? BusinessName { get; set; }
    [JsonPropertyName("contactName")]
    public string? ContactName { get; set; }
    [JsonPropertyName("email")]
    public string? Email { get; set; }
    [JsonPropertyName("phone")]
    public string? Phone { get; set; }
    [JsonPropertyName("address")]
    public NoSupplierAddressModel? Address { get; set; }
    [JsonPropertyName("bankDetails")]
    public NoSupplierBankDetailsModel? BankDetails { get; set; }
}
