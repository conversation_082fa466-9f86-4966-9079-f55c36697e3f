name: 'Terraform Plan And Apply'

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: Select the environment
        required: true

permissions:
  contents: read

jobs:
  terraform-plan:
    name: 'Terraform'
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    env:
      PACKAGE_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
      PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}
      ARM_CLIENT_ID: "${{ secrets.AZURE_CLIENT_ID }}"
      ARM_SUBSCRIPTION_ID: "${{ secrets.AZURE_SUBSCRIPTION_ID }}"
      ARM_TENANT_ID: "${{ secrets.AZURE_TENANT_ID }}"
      ARM_CLIENT_SECRET: "${{ secrets.AZURE_CLIENT_SECRET }}"
      ACR_PASSWORD: "${{ secrets.AZURE_ACR_PASSWORD }}"
      AWS_ACCESS_KEY_ID: "${{ secrets.AWS_ACCESS_KEY_ID_DEV }}"
      AWS_SECRET_ACCESS_KEY: "${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}"
      LP_AWS_ACCOUNT: "${{ secrets.AWS_ACCOUNT_ID }}"
      CONTAINER_REGISTRY: containerregistrybt${{ vars.STAGE }}.azurecr.io
      IMAGE_NAME: obs-service:latest
      SOURCE_CODE_PATH: src/
      ENV_NAME: "${{ vars.STAGE }}"

    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Azure Login
        run: az login --service-principal -u $ARM_CLIENT_ID -p $ARM_CLIENT_SECRET --tenant $ARM_TENANT_ID

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Build and Push Docker Image
        uses: azure/docker-login@v1
        with:
          login-server: containerregistrybt${{ vars.STAGE }}.azurecr.io
          username: ${{ secrets.AZURE_CLIENT_ID }}
          password: ${{ secrets.AZURE_CLIENT_SECRET }}
      -  run: |
           docker build \
           --build-arg ASPNETCORE_ENVIRONMENT=${ENV_NAME} \
           --build-arg PACKAGE_REGISTRY_USERNAME=${PACKAGE_REGISTRY_USERNAME} \
           --build-arg PACKAGE_REGISTRY_PASSWORD=${PACKAGE_REGISTRY_PASSWORD} \
           -t $CONTAINER_REGISTRY/$IMAGE_NAME \
           $SOURCE_CODE_PATH
           docker push ${CONTAINER_REGISTRY}/${IMAGE_NAME}

      - name: Terraform Init
        run: |
          cd infra/environments/${ENV_NAME}
          terraform init

      - name: Terraform Validate
        run: |
          cd infra/environments/${ENV_NAME}
          terraform validate

      - name: Terraform Plan
        run: |
          cd infra/environments/${ENV_NAME}
          terraform plan -var="acr_password=$ACR_PASSWORD" -var="client_id=$ARM_CLIENT_ID" -var="tenant_id=$ARM_TENANT_ID" -var="client_secret=$ARM_CLIENT_SECRET" -var="lp_aws_account=$LP_AWS_ACCOUNT" -var="aws_access_key_id=$AWS_ACCESS_KEY_ID" -var="aws_secret_access_key=$AWS_SECRET_ACCESS_KEY" -out=${ENV_NAME}.tfplan

      - name: Terraform Apply
        run: |
          cd infra/environments/${ENV_NAME}
          terraform apply ${ENV_NAME}.tfplan
