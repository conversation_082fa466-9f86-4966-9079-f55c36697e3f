﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class CreditApplicationAuthorizationDetailsRepository(IObsMongoDBContext context,
        ILogger<GenericRepository<CreditApplicationAuthorizationDetailsDocument>> logger)
    :
        GenericRepository<CreditApplicationAuthorizationDetailsDocument>(context, logger),
        ICreditApplicationAuthorizationDetailsRepository
{
    private readonly IMongoCollection<CreditApplicationAuthorizationDetailsDocument> _collection = context.GetCollection<CreditApplicationAuthorizationDetailsDocument>();

    public async Task<IEnumerable<CreditApplicationAuthorizationDetailsDocument>> GetByCreditApplicationId(
        string creditApplicationId, CancellationToken ct)
    {
        var filter = Builders<CreditApplicationAuthorizationDetailsDocument>.Filter.Eq(x => x.CreditApplicationId, creditApplicationId);
        return await _collection.Find(filter).ToListAsync(ct);
    }

    public async Task<CreditApplicationAuthorizationDetailsDocument> UpdateByCreditApplicationId(
        CreditApplicationAuthorizationDetailsDocument updateDocument, CancellationToken ct)
    {
        var findDocumentFilter = Builders<CreditApplicationAuthorizationDetailsDocument>
            .Filter.Eq(x => x.CreditApplicationId, updateDocument.CreditApplicationId);
        var updateDocumentFilter = Builders<CreditApplicationAuthorizationDetailsDocument>.Update
            .Set(x => x.AccountAuthorizationDetailsSnapshot, updateDocument.AccountAuthorizationDetailsSnapshot)
            .Set(x => x.UpdatedAt, DateTime.UtcNow);

        await Collection.UpdateOneAsync(findDocumentFilter, updateDocumentFilter, cancellationToken: ct);

        return updateDocument;
    }
}