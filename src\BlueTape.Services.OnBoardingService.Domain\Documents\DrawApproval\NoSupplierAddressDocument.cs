﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class NoSupplierAddressDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("address")]
    public string? Address { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("city")]
    public string? City { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("state")]
    public string? State { get; set; }

    [BsonIgnoreIfNull]
    [BsonElement("zip")]
    public string? Zip { get; set; }
}
