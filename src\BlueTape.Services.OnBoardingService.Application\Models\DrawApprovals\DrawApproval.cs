﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class DrawApproval
{
    public string Id { get; set; } = null!;
    public string CompanyId { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;

    public string EinHash { get; set; } = string.Empty;

    public string? CreditId { get; set; } = string.Empty;
    public string? ArAdvanceCreditId { get; set; } = string.Empty;
    public string? InHouseCreditId { get; set; } = string.Empty;

    public string? CreditApplicationId { get; set; }
    public DateTime ApplicationDate { get; set; }

    public string PaymentPlanId { get; set; } = string.Empty;
    public DrawApprovalType Type { get; set; }

    public LoanOrigin LoanOrigin { get; set; }

    public string? VirtualCardId { get; set; }
    public string? ExecutionId { get; set; }
    public string MerchantId { get; set; } = string.Empty;

    public string MerchantName { get; set; } = string.Empty;

    public decimal DrawAmount { get; set; }

    public decimal? CreditHoldAmount { get; set; }
    public string? AuthorizationPeriodId { get; set; }
    public string? ArAdvanceAuthorizationPeriodId { get; set; }

    public DrawAmountRiskLevel DrawAmountRiskLevel { get; set; }

    public string? ProjectId { get; set; }

    public AutomatedDecisionResult? AutomatedDecisionResult { get; set; }

    public AutomatedApprovalResult? AutomatedApprovalResult { get; set; }

    public DrawApprovalStatus Status { get; set; }

    public DebtInvestorType? DebtInvestor { get; set; }

    public DateTime? ExpirationDate { get; set; }

    public DateTime LastStatusChangedAt { get; set; }

    public string LastStatusChangedBy { get; set; } = string.Empty;

    public DateTime? ApprovedAt { get; set; }

    public string? ApprovedBy { get; set; }

    public DateTime? RejectedAt { get; set; }

    public string? RejectedBy { get; set; }

    public DateTime? CanceledAt { get; set; }

    public string? CanceledBy { get; set; }

    public DateTime? LastRerunAt { get; set; }

    public string? LastRerunBy { get; set; }

    public DateTime? InvoicedAt { get; set; }

    public string? InvoicedBy { get; set; }

    public string? StatusCode { get; set; }

    public string? StatusNote { get; set; }

    public DrawDetails DrawDetails { get; set; } = new();
    public FactoringDetailsModel FactoringDetails { get; set; } = new();
    public FactoringOverallDetailsModel FactoringOverallDetails { get; set; } = new();
    public PayNowDetailsModel PayNowDetails { get; set; } = new();
    public AutomatedApprovalDetailsModel AutomatedApprovalDetails { get; set; } = new();

    public IEnumerable<PayableItem> Payables { get; set; } = new List<PayableItem>();
    public NoSupplierDetails? NoSupplierDetails { get; set; } = new();
    public DownPaymentDetails? DownPaymentDetails { get; set; } = new();

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string CreatedBy { get; set; } = string.Empty;

    public string UpdatedBy { get; set; } = string.Empty;
}
