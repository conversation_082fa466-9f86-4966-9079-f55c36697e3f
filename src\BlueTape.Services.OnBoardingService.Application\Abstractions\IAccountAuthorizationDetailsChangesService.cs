﻿using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IAccountAuthorizationDetailsChangesService
{
    Task CompareAccountAuthorizationsDetailsAndLogChangesAsync(
        AccountAuthorizationDocument initialAccountAuthorizationDocument,
        AccountAuthorizationDocument updatedAccountAuthorizationDocument,
        string creditApplicationId,
        ExecutionChangeType executionChangeType,
        CancellationToken cancellationToken);
}
