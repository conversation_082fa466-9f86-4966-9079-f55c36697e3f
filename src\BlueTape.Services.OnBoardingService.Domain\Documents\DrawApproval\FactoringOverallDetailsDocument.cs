using BlueTape.OBS.Enums;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class FactoringOverallDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("loansLastDefaultedDate")]
    public DateTime? LoansLastDefaultedDate { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("pastDueAmount")]
    public decimal? PastDueAmount { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("outstandingBalance")]
    public decimal? OutstandingBalance { get; set; }
}