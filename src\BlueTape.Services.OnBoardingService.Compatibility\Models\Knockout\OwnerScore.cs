﻿using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;

[BsonIgnoreExtraElements]
public class OwnerScore
{
    [BsonElement("owner")]
    public Owner? Owner { get; set; } = new();
    [BsonElement("scores")]
    public List<Score>? Scores { get; set; } = new();
}
