﻿using BlueTape.CompanyService.CashFlow.Queries;
using BlueTape.CompanyService.CashFlow.Responses;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;

namespace BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;

public interface ICompanyExternalService
{
    Task<CompanyModel?> GetById(string id, CancellationToken ctx);

    Task<IEnumerable<CashFlowItemResponse>?> GetPlaidAssetReport(string companyId, AssetReportQuery query,
        CancellationToken ctx);

    Task<PaginatedCompanyResponse?> GetCompaniesByQueryAsync(CompanyQueryPaginated query, CancellationToken ct);

    Task<CustomerModel?> SetCustomerIhcSettings(string customerId, UpdateCustomerModel updateCustomerModel, CancellationToken ct);

    Task UpdateCompany(string id, string userId, UpdateCompanyModel updateCompanyModel, CancellationToken ct);
}