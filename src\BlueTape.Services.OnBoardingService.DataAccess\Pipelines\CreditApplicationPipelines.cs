﻿using MongoDB.Bson;

namespace BlueTape.Services.OnBoardingService.DataAccess.Pipelines;

public static class CreditApplicationPipelines
{
    public static readonly BsonDocument LookupUserRoles = new("$lookup",
        new BsonDocument
        {
            { "from", "userroles" },
            {
                "let",
                new BsonDocument("companyId", "$companyId")
            },
            {
                "pipeline",
                new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$eq",
                                new BsonArray
                                {
                                    "$company_id",
                                    "$$companyId"
                                })))
                }
            },
            { "as", "userRoles" }
        });

    public static readonly BsonDocument LookupAccountAuthorizations = new("$lookup",
        new BsonDocument
        {
            { "from", "accountAuthorizations" },
            {
                "let",
                new BsonDocument("companyId", "$companyId")
            },
            {
                "pipeline",
                new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$eq",
                                new BsonArray
                                {
                                    "$companyId",
                                    "$$companyId"
                                })))
                }
            },
            { "as", "accountAuthorizationsCollection" }
        });

    public static readonly BsonDocument UnwindAccountAuthorizationDetails = new("$addFields",
        new BsonDocument("accountAuthorizationDetails",
            new BsonDocument("$arrayElemAt",
                new BsonArray
                {
                    "$accountAuthorizationsCollection",
                    0
                })));

    public static readonly BsonDocument AddSsnHashes = new("$addFields",
        new BsonDocument("ssnHashes",
            new BsonDocument("$map",
                new BsonDocument
                {
                    { "input", "$accountAuthorizationDetails.ownersDetails" },
                    { "as", "ownerDetail" },
                    { "in", "$$ownerDetail.ssnHash" }
                })));

    public static readonly BsonDocument UnwindUserRole = new("$addFields",
        new BsonDocument("userRole",
            new BsonDocument("$arrayElemAt",
                new BsonArray
                {
                    "$userRoles",
                    0
                })));

    public static readonly BsonDocument LookupUser = new("$lookup",
        new BsonDocument
        {
            { "from", "users" },
            {
                "let",
                new BsonDocument("sub", "$userRole.sub")
            },
            {
                "pipeline",
                new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$eq",
                                new BsonArray
                                {
                                    "$sub",
                                    "$$sub"
                                })))
                }
            },
            { "as", "users" }
        });

    public static readonly BsonDocument UnwindUser = new("$addFields",
        new BsonDocument("user",
            new BsonDocument("$arrayElemAt",
                new BsonArray
                {
                    "$users",
                    0
                })));

    public static readonly BsonDocument LookupInHouseCustomerAccount = new("$lookup",
        new BsonDocument
        {
            { "from", "customeraccounts" },
            {
                "let",
                new BsonDocument
                {
                    { "merchantId", "$merchantId" },
                    { "userEmail", "$user.email" }
                }
            },
            {
                "pipeline",
                new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$and",
                                new BsonArray
                                {
                                    new BsonDocument("$eq",
                                        new BsonArray
                                        {
                                            "$company_id",
                                            "$$merchantId"
                                        }),
                                    new BsonDocument("$eq",
                                        new BsonArray
                                        {
                                            "$email",
                                            "$$userEmail"
                                        })
                                })))
                }
            },
            { "as", "customerAccounts" }
        });


    public static readonly BsonDocument UnwindCustomerAccount = new("$addFields",
        new BsonDocument("customerAccount",
            new BsonDocument("$arrayElemAt",
                new BsonArray
                {
                    new BsonDocument("$filter", new BsonDocument
                    {
                        { "input", "$customerAccounts" },
                        { "as", "account" },
                        { "cond", new BsonDocument("$eq", new BsonArray { "$$account.isDeleted", false }) }
                    }),
                    0
                })));

    public static readonly BsonDocument AddCustomerAccountInfo = new("$addFields",
        new BsonDocument("isInHouseCreditEnabled",
            "$customerAccount.settings.inHouseCredit.isEnabled"));

    public static readonly BsonDocument LookupCardPricingPackages = new("$lookup",
        new BsonDocument
        {
            { "from", "cardpricingpackages" },
            {
                "let",
                new BsonDocument("cardPricingPackageId",
                    new BsonDocument("$toObjectId", "$merchantSettings.cardPricingPackageId"))
            },
            {
                "pipeline",
                new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$eq",
                                new BsonArray
                                {
                                    "$_id",
                                    "$$cardPricingPackageId"
                                })))
                }
            },
            { "as", "cardPricing" }
        });

    public static readonly BsonDocument LookupLoanPricingPackages = new("$lookup",
        new BsonDocument
        {
            { "from", "loanpricingpackages" },
            {
                "let",
                new BsonDocument("loanPricingPackageId",
                    new BsonDocument("$toObjectId", "$merchantSettings.loanPricingPackageId"))
            },
            {
                "pipeline",
                new BsonArray
                {
                    new BsonDocument("$match",
                        new BsonDocument("$expr",
                            new BsonDocument("$eq",
                                new BsonArray
                                {
                                    "$_id",
                                    "$$loanPricingPackageId"
                                })))
                }
            },
            { "as", "loanPricing" }
        });

    public static readonly BsonDocument UnwindCardPricingPackages = new("$unwind",
        new BsonDocument
        {
            { "path", "$cardPricing" },
            { "preserveNullAndEmptyArrays", true }
        });

    public static readonly BsonDocument UnwindLoanPricingPackages = new("$unwind",
        new BsonDocument
        {
            { "path", "$loanPricing" },
            { "preserveNullAndEmptyArrays", true }
        });

    public static readonly BsonDocument AddPricingPackagesNames = new("$addFields",
        new BsonDocument
        {
            { "merchantSettings.cardPricingPackageName", "$cardPricing.title" },
            { "merchantSettings.loanPricingPackageName", "$loanPricing.title" }
        });

    public static readonly BsonDocument ProjectPricingPackagesNames = new("$project",
        new BsonDocument
        {
            { "cardPricing", 0 },
            { "loanPricing", 0 }
        });
}
