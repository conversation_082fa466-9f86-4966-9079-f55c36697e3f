﻿using BlueTape.Services.OnBoardingService.Compatibility.Abstractions;
using BlueTape.Services.OnBoardingService.Compatibility.Extensions;
using BlueTape.Services.OnBoardingService.Compatibility.Models.Knockout;
using BlueTape.Services.OnBoardingService.Domain.Documents.LoanApplication;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Services.OnBoardingService.Compatibility.Services.Knockout.Scorers;
public class FraudPointScorer(IConfiguration config) : IScoring
{
    public List<OwnerScore> Decide(KycData? fraud, KybData? kyb, ExperianData? experian, LoanDecisionData? decision)
    {
        int passThreshold = config.GetValue<int?>("ScoringThresholds:FraudPointThresholdPass") ?? 501;
        var result = new List<OwnerScore>();

        if (fraud?.Fraud != null && fraud.Fraud.Any())
        {
            foreach (var item in fraud.Fraud)
            {
                OwnerScore ownerScore = new();

                if (item.Owner is not null)
                    ownerScore.Owner = item.Owner;

                int.TryParse(item.FraudPoint?.FraudPointScoreRiskLevel, out var fraudRiskLevel);

                ownerScore.Scores?.Add(KnockoutCalculationExtension.Calculate("fraudPoint", fraudRiskLevel, passThreshold));

                result.Add(ownerScore);
            }

            return result;
        }

        int.TryParse(fraud?.FraudPoint?.FraudPointScoreRiskLevel, out var fraudRiskLevel1);
        var score = KnockoutCalculationExtension.Calculate("fraudPoint", fraudRiskLevel1, passThreshold);
        return [new OwnerScore { Scores = [score] }];
    }
}
