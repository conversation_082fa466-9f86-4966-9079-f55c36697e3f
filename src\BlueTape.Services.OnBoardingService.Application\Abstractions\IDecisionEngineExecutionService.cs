﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface IDecisionEngineExecutionService
{
    Task<StepFunctionsExecutionResponse> StartCreditApplicationInitializationStep(
        CreditApplicationInitializationStepStartRequest stepStartRequest, CancellationToken ctx);

    Task PreValidateCreditApplicationDecisionEngineExecution(CreditApplicationType type, string? merchantId,
        string? companyId, string einHash, CancellationToken ctx);

    Task<StepFunctionsExecutionResponse> StartDrawApprovalInitializationStep(DrawApprovalInitializationStepStartRequest stepStartRequest,
        CancellationToken ctx);

    void PreValidateDrawApprovalDecisionEngineExecution(DrawApprovalDecisionEngineExecutionRequest request);
}
