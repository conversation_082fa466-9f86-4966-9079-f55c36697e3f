﻿using BlueTape.OBS.Enums;

namespace BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;

public class PatchInternalDrawApproval
{
    public InternalDrawApprovalStatusUpdate? NewStatus { get; set; }
    public DebtInvestorType? DebtInvestor { get; set; }
    public PatchDrawDetails DrawDetails { get; set; } = new();
    public PatchFactoringDetailsModel? FactoringDetails { get; set; }
    public PatchFactoringOverallDetailsModel? FactoringOverallDetails { get; set; }
    public PatchPayNowDetailsModel? PayNowDetails { get; set; }
    public PatchAutomatedApprovalDetailsModel? AutomatedApprovalDetails { get; set; }
}
