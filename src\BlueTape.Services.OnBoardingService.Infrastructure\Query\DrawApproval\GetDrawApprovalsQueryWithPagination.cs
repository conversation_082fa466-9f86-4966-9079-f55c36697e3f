﻿namespace BlueTape.Services.OnBoardingService.Infrastructure.Query.DrawApproval;

public class GetDrawApprovalsQueryWithPagination
{
    public string? Id { get; set; }
    public string? CompanyId { get; set; }
    public string? EinHash { get; set; }
    public string? CreditId { get; set; }
    public string? ArAdvanceCreditId { get; set; }
    public string? InHouseCreditId { get; set; }
    public string? MerchantId { get; set; }
    public string? MerchantName { get; set; }
    public string? Search { get; set; }
    public string? InvoiceNumber { get; set; }
    public string[]? InvoiceStatuses { get; set; }
    public string[]? IhcStatus { get; set; }
    public string[]? PayableIds { get; set; }
    public string? CompanyName { get; set; }
    public string? CompanyDba { get; set; }
    public string[]? Status { get; set; }
    public string? StatusCode { get; set; }
    public string? Product { get; set; }
    public string[]? Type { get; set; }
    public string? Term { get; set; }
    public string[]? PayableTypes { get; set; }
    public string? PaymentPlanId { get; set; }
    public string[]? AutomatedDecisionResult { get; set; }
    public string[]? AutomatedApprovalResult { get; set; }
    public string? VelocityCheckResult { get; set; }
    public string[]? AccountStatus { get; set; }
    public DateOnly? AppDateFrom { get; set; }
    public DateOnly? AppDateTo { get; set; }
    public DateOnly? DecisionDateFrom { get; set; }
    public DateOnly? DecisionDateTo { get; set; }
    public DateOnly? InvoiceDueFrom { get; set; }
    public DateOnly? InvoiceDueTo { get; set; }
    public int PageSize { get; set; } = 10;
    public int PageNumber { get; set; } = 1;
    public string? SortBy { get; set; }
    public string? SortOrder { get; set; }
}
