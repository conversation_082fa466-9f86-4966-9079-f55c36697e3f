using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.Services.OnBoardingService.Application.Mappers;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.CompanyService.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.Company;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class CompanyServiceTests
{
    private readonly Mock<ICompanyRepository> _companyRepositoryMock = new();
    private readonly Mock<ICompanyExternalService> _companyExternalServiceMock = new();
    private readonly Mock<ILogger<Application.Services.CompanyService>> _loggerMock = new();
    private readonly Application.Services.CompanyService _service;

    public CompanyServiceTests()
    {
        var mapperConfig = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile(new ModelsProfile());
            });
        IMapper mapper = new Mapper(mapperConfig);
        _service = new Application.Services.CompanyService(_loggerMock.Object, _companyRepositoryMock.Object, _companyExternalServiceMock.Object, mapper);
    }

    [Fact]
    public async Task GetCompaniesByActiveAccounts_SinglePage_ReturnsAllCompanies()
    {
        // Arrange
        var companies = new List<CompanyModel> { new CompanyModel(), new CompanyModel() };
        var paginatedResult = new PaginatedCompanyResponse { Result = companies, Total = companies.Count };

        _companyExternalServiceMock.Setup(x => x.GetCompaniesByQueryAsync(
            It.Is<CompanyQueryPaginated>(q => q.ActiveAccountOnly && q.PageNumber == 1 && q.PageSize == 100),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _service.GetCompaniesByActiveAccounts(CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(companies.Count, result.Count);
        _companyExternalServiceMock.Verify(x => x.GetCompaniesByQueryAsync(It.IsAny<CompanyQueryPaginated>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetCompaniesByActiveAccounts_MultiplePages_ReturnsAllCompanies()
    {
        // Arrange
        var firstPageCompanies = Enumerable.Range(0, 100).Select(_ => new CompanyModel()).ToList();
        var secondPageCompanies = Enumerable.Range(0, 50).Select(_ => new CompanyModel()).ToList();

        var firstPageResult = new PaginatedCompanyResponse { Result = firstPageCompanies, Total = 150 };
        var secondPageResult = new PaginatedCompanyResponse { Result = secondPageCompanies, Total = 150 };

        _companyExternalServiceMock.SetupSequence(x => x.GetCompaniesByQueryAsync(It.IsAny<CompanyQueryPaginated>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(firstPageResult)
            .ReturnsAsync(secondPageResult);

        // Act
        var result = await _service.GetCompaniesByActiveAccounts(CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(150, result.Count);
        _companyExternalServiceMock.Verify(x => x.GetCompaniesByQueryAsync(It.IsAny<CompanyQueryPaginated>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task GetCompaniesByActiveAccounts_ExternalServiceReturnsNull_ReturnsNull()
    {
        // Arrange

        _companyExternalServiceMock.Setup(x => x.GetCompaniesByQueryAsync(It.IsAny<CompanyQueryPaginated>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PaginatedCompanyResponse)null!);

        // Act
        var result = await _service.GetCompaniesByActiveAccounts(CancellationToken.None);

        // Assert
        Assert.Null(result);
        _companyExternalServiceMock.Verify(x => x.GetCompaniesByQueryAsync(It.IsAny<CompanyQueryPaginated>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task UpdateCredit_ValidRequest_ShouldPatchCreditLimit(string companyId, BlueTape.Services.OnBoardingService.Application.Models.Company.UpdateCompanyModel model)
    {
        model.PurchaseType = "Both";

        _companyRepositoryMock.Setup(x => x.UpdateManyByIds(new string[] { companyId }, It.Is<UpdateCompanyDocument>(x =>
            x.PurchaseType == "projectOrInventory"
            && x.Limit == model.Limit), default));

        await _service.PatchCompanyCredit(companyId, model, default);

        _companyRepositoryMock.Verify(x => x.UpdateManyByIds(new string[] { companyId }, It.Is<UpdateCompanyDocument>(x =>
            x.PurchaseType == "projectOrInventory"
            && x.Limit == model.Limit), default), Times.Once);
    }

    [Theory, CustomAutoData]
    public async Task UpdateCredit_InvalidPurchaseType_ShouldPatchCreditLimit(string companyId, BlueTape.Services.OnBoardingService.Application.Models.Company.UpdateCompanyModel model)
    {
        model.PurchaseType = "test";

        _companyRepositoryMock.Setup(x => x.UpdateManyByIds(new string[] { companyId }, It.Is<UpdateCompanyDocument>(x =>
            x.PurchaseType == null
            && x.Limit == model.Limit), default));

        await _service.PatchCompanyCredit(companyId, model, default);

        _companyRepositoryMock.Verify(x => x.UpdateManyByIds(new string[] { companyId }, It.Is<UpdateCompanyDocument>(x =>
            x.PurchaseType == null
            && x.Limit == model.Limit), default), Times.Once);
    }
}