﻿using BlueTape.Services.OnBoardingService.Domain.Serializers;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;

public class DecisionEngineStepResultDocument
{
    [BsonElement("code")]
    public string? Code { get; set; }

    [BsonElement("scope")]
    public string? Scope { get; set; }

    [BsonElement("ownerIdentifier")]
    public string? OwnerIdentifier { get; set; }

    [BsonElement("bankAccountIdentifier")]
    public string? BankAccountIdentifier { get; set; }

    [BsonElement("ownerSsnHash")]
    public string? OwnerSsnHash { get; set; }

    [BsonElement("comparisonSource")]
    public string? ComparisonSource { get; set; }

    [BsonElement("comparisonJustification")]
    public string? ComparisonJustification { get; set; }

    [BsonSerializer(typeof(ObjectSerializer))]
    [BsonElement("comparisonValue")]
    public object? ComparisonValue { get; set; }

    [BsonSerializer(typeof(ObjectSerializer))]
    [BsonElement("thresholdValue")]
    public object? ThresholdValue { get; set; }

    [BsonElement("result")]
    public string? Result { get; set; }

    [BsonElement("manualResult")]
    public string? ManualResult { get; set; }

    [BsonElement("manualResultAt")]
    public string? ManualResultAt { get; set; }

    [BsonElement("manualResultBy")]
    public string? ManualResultBy { get; set; }

    [BsonElement("manualResultNote")]
    public string? ManualResultNote { get; set; }
}
