using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;

[BsonIgnoreExtraElements]
public class FactoringDetailsDocument
{
    [BsonIgnoreIfNull]
    [BsonElement("arAdvanceCreditLimit")]
    public decimal? ArAdvanceCreditLimit { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("arAdvanceCreditOutstandingBalance")]
    public decimal? ArAdvanceCreditOutstandingBalance { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("arAdvanceCreditAvailableBalance")]
    public decimal? ArAdvanceCreditAvailableBalance { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("inHouseCreditStatus")]
    public string? InHouseCreditStatus { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("inHouseCreditLimit")]
    public decimal? InHouseCreditLimit { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("inHouseCreditOutstandingBalance")]
    public decimal? InHouseCreditOutstandingBalance { get; set; }
    
    [BsonIgnoreIfNull]
    [BsonElement("inHouseCreditAvailableBalance")]
    public decimal? InHouseCreditAvailableBalance { get; set; }
}