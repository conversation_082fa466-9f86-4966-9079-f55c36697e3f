variable "environment" {
  type    = string
}

variable "application_name" {
  type = map(any)
}

variable "key_vault_id" {
  type    = string
}

variable "resource_group_name" {
  type    = string
}

variable "resource_group_location" {
  type    = string
}

variable "loan_application_sync_queue_name" {
  default = "loanApplicationSyncQueueName"
}

variable "loan_application_sync_queue_connection" {
  default = "loanApplicationSyncQueueConnection"
}

variable "authorization_details_refresh_queue_connection" {
  default = "refreshServiceQueueConnection"
}

variable "authorization_details_refresh_queue_name" {
  default = "refreshServiceQueueName"
}