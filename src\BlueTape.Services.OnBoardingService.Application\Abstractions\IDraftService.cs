﻿using BlueTape.Services.OnBoardingService.Application.Models.Draft;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions
{
    public interface IDraftService
    {
        Task<IEnumerable<Draft>> GetAll(CancellationToken ct);
        Task<IEnumerable<Draft>> GetAllByFilters(DraftFilter filter, CancellationToken ct);
        Task<IEnumerable<Draft>> GetByCompanyIds(string[] companyIds, CancellationToken ct);
        Task<Draft> GetById(string id, CancellationToken ct);
    }
}
