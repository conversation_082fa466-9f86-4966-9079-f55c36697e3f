﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\BlueTape.Services.OnBoardingService.Compatibility\BlueTape.Services.OnBoardingService.Compatibility.csproj" />
		<ProjectReference Include="..\BlueTape.Services.OnBoardingService.RefreshDetectorService\BlueTape.Services.OnBoardingService.RefreshDetectorService.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.4.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
		<PackageReference Include="BlueTape.LambdaBase" Version="1.1.1" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="BlueTape.AzureKeyVault" Version="1.0.3" />
		<PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
		<PackageReference Include="Microsoft.Azure.Functions.Worker" Version="1.23.0" />
	</ItemGroup>

</Project>
