﻿using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions.Contexts;
using BlueTape.Services.OnBoardingService.Domain.Documents.Company;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.Services.OnBoardingService.DataAccess.Repositories;

public class CompanyRepository : GenericRepository<CompanyDocument>, ICompanyRepository
{
    public CompanyRepository(IObsMongoDBContext context, ILogger<GenericRepository<CompanyDocument>> logger) : base(context, logger)
    {
    }

    public Task UpdateManyByIds(IEnumerable<string> companyIds, UpdateCompanyDocument updateCompany,
        CancellationToken cancellationToken)
    {
        if (!companyIds.Any()) return Task.CompletedTask;
        var collection = Context.GetCollection<CompanyDocument>();

        var filterBuilder = new FilterDefinitionBuilder<CompanyDocument>();
        var filter = filterBuilder.Where(x => companyIds.Contains(x.Id));

        var updateDefinition = BuildUpdateDefinition(updateCompany);

        return collection.UpdateManyAsync(filter, updateDefinition, null, cancellationToken);
    }

    public Task<IEnumerable<CompanyDocument>> GetSuppliers(CancellationToken cancellationToken)
    {
        return GetAll(x => x.Status == "approved" || x.Status == "rejected", cancellationToken);
    }

    private static UpdateDefinition<CompanyDocument> BuildUpdateDefinition(UpdateCompanyDocument updateLoanApplication)
    {
        var updateBuilder = new UpdateDefinitionBuilder<CompanyDocument>();
        var updateDefinition = updateBuilder
            .Set(x => x.UpdatedAt, DateTime.UtcNow);

        if (!string.IsNullOrEmpty(updateLoanApplication.Status))
            updateDefinition = updateDefinition.Set(x => x.Status, updateLoanApplication.Status);

        if (updateLoanApplication.ApproveRead.HasValue)
            updateDefinition = updateDefinition.Set(x => x.Settings!.ApproveRead, updateLoanApplication.ApproveRead);

        if (!string.IsNullOrEmpty(updateLoanApplication.Name))
            updateDefinition = updateDefinition.Set(x => x.Name, updateLoanApplication.Name);

        if (!string.IsNullOrEmpty(updateLoanApplication.LegalName))
            updateDefinition = updateDefinition.Set(x => x.LegalName, updateLoanApplication.LegalName);

        if (updateLoanApplication.AcceptAchPayment.HasValue)
            updateDefinition = updateDefinition.Set(x => x.Settings!.AcceptAchPayment, updateLoanApplication.AcceptAchPayment);

        if (!string.IsNullOrEmpty(updateLoanApplication.PurchaseType))
            updateDefinition = updateDefinition.Set(x => x.Credit!.PurchaseType, updateLoanApplication.PurchaseType);

        if (updateLoanApplication.Limit is not null or 0)
            updateDefinition = updateDefinition.Set(x => x.Credit!.Limit, updateLoanApplication.Limit);

        if (!string.IsNullOrEmpty(updateLoanApplication.CardPricingPackageId))
            updateDefinition = updateDefinition.Set(x => x.Settings!.CardPricingPackageId, updateLoanApplication.CardPricingPackageId);

        if (!string.IsNullOrEmpty(updateLoanApplication.LoanPricingPackageId))
            updateDefinition = updateDefinition.Set(x => x.Settings!.LoanPricingPackageId, updateLoanApplication.LoanPricingPackageId);

        if (updateLoanApplication.AchDelayDisabled.HasValue)
            updateDefinition = updateDefinition.Set(x => x.Settings!.AchDelayDisabled, updateLoanApplication.AchDelayDisabled);

        return updateDefinition;
    }
}
