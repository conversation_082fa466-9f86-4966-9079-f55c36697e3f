﻿using Amazon.StepFunctions.Model;
using BlueTape.AWSStepFunction.Abstractions;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Constants;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Application.Services;
using BlueTape.Services.OnBoardingService.Application.Tests.Attributes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.CreditApplication;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.Application.Tests.Services;

public class DecisionEngineExecutionServiceTests
{
    private readonly DecisionEngineExecutionService _decisionEngineExecutionService;

    private readonly Mock<IStepFunctionClient<CreditApplicationInitializationStepStartRequest>> _creditApplicationFunctionClientMock = new();
    private readonly Mock<IStepFunctionClient<DrawApprovalInitializationStepStartRequest>> _drawApprovalFunctionClientMock = new();
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock = new();
    private readonly Mock<ILogger<DecisionEngineExecutionService>> _loggerMock = new();

    public DecisionEngineExecutionServiceTests()
    {
        _decisionEngineExecutionService = new DecisionEngineExecutionService(_creditApplicationFunctionClientMock.Object,
            _drawApprovalFunctionClientMock.Object, _creditApplicationRepositoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public Task StartCreditApplicationInitializationStep_StepStartRequestNull_ThrowsArgumentNullException()
    {
        var act = () => _decisionEngineExecutionService.StartCreditApplicationInitializationStep(null!, default);

        return act.ShouldThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task StartCreditApplicationInitializationStep_ValidData_TriggersDecisionEngineExecution()
    {
        var arn = Guid.NewGuid().ToString();
        var request = new CreditApplicationInitializationStepStartRequest()
        {
            CreditApplicationId = "creditAppId",
            JobId = Guid.NewGuid()
        };
        _creditApplicationFunctionClientMock.Setup(x => x.StartAsync(request, request.JobId.ToString(), default, default)).ReturnsAsync(new StartExecutionResponse()
        {
            ExecutionArn = arn
        });

        var result = await _decisionEngineExecutionService.StartCreditApplicationInitializationStep(request, default);

        _creditApplicationFunctionClientMock.Verify(x => x.StartAsync(request, request.JobId.ToString(), default, default), Times.Once);
        result.ExecutionArn.ShouldBe(arn);
    }

    [Fact]
    public async Task StartDrawApprovalInitializationStep_ValidData_TriggersDecisionEngineExecution()
    {
        var arn = Guid.NewGuid().ToString();
        var request = new DrawApprovalInitializationStepStartRequest()
        {
            DrawApprovalId = "drawAppId",
            JobId = Guid.NewGuid()
        };

        _drawApprovalFunctionClientMock.Setup(x => x.StartAsync(request, request.JobId.ToString(), default, default)).ReturnsAsync(new StartExecutionResponse()
        {
            ExecutionArn = arn
        });
        var result = await _decisionEngineExecutionService.StartDrawApprovalInitializationStep(request, default);

        _drawApprovalFunctionClientMock.Verify(x => x.StartAsync(request, request.JobId.ToString(), default, default), Times.Once);
        result.ExecutionArn.ShouldBe(arn);
    }

    [Fact]
    public Task StartDrawApprovalInitializationStep_StepStartRequestNull_ThrowsArgumentNullException()
    {
        var act = () => _decisionEngineExecutionService.StartDrawApprovalInitializationStep(null!, default);

        return act.ShouldThrowAsync<ArgumentNullException>();
    }

    [Theory, CustomAutoData]
    public Task PreValidateCreditApplicationDecisionEngineExecution_ApprovedApplicationExists_ThrowsValidationException(CreditApplicationDocument creditApplication)
    {
        var einHash = "einHash";
        var merchantId = "merchantId";
        var companyId = "companyId";
        var type = CreditApplicationType.LineOfCredit;

        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(query =>
            query.EinHash == einHash && query.MerchantId == merchantId && query.Type![0] == type.ToString()), default)).ReturnsAsync(new List<CreditApplicationDocument>()
        {
            creditApplication
        });

        var act = async () => await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(type, merchantId,
            companyId, einHash, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Theory, CustomAutoData]
    public Task PreValidateCreditApplicationDecisionEngineExecution_FilterIncludesNonApplicableStatuses_ThrowsValidationException(CreditApplicationDocument creditApplication)
    {
        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(query =>
            query.Status == CreditApplicationConstants.SingleCreditApplicationApplicableStatuses), default)).ReturnsAsync(new List<CreditApplicationDocument>()
        {
            creditApplication
        });

        var act = async () => await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(CreditApplicationType.InHouseCredit, "merchantId",
            "companyId", "einHash", default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public Task PreValidateCreditApplicationDecisionEngineExecution_CompanyIdEmpty_ThrowsValidationException()
    {
        var einHash = "einHash";
        var merchantId = "merchantId";
        var type = CreditApplicationType.LineOfCredit;
        var companyId = "";

        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFilters(It.IsAny<GetCreditApplicationQuery>(), default)).ReturnsAsync(new List<CreditApplicationDocument>()
        {
        });

        var act = async () => await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(type, merchantId,
            companyId, einHash, default);

        return act.ShouldThrowAsync<ValidationException>();
    }

    [Fact]
    public Task PreValidateCreditApplicationDecisionEngineExecution_PreValidationSucceeded_DoesNotThrowValidationException()
    {
        var einHash = "einHash";
        var merchantId = "merchantId";
        var type = CreditApplicationType.LineOfCredit;
        var companyId = "company";

        _creditApplicationRepositoryMock.Setup(x => x.GetAllByFilters(It.Is<GetCreditApplicationQuery>(query =>
            query.EinHash == einHash && query.MerchantId == merchantId && query.Type![0] == type.ToString() && query.CompanyId == companyId), default)).ReturnsAsync(new List<CreditApplicationDocument>());

        var act = async () => await _decisionEngineExecutionService.PreValidateCreditApplicationDecisionEngineExecution(type, merchantId,
            companyId, einHash, default);

        return act.ShouldNotThrowAsync();
    }

    [Fact]
    public void PreValidateDrawApprovalDecisionEngineExecution_PayablesArrayIsEmpty_ThrowsValidationException()
    {
        var request = new DrawApprovalDecisionEngineExecutionRequest()
        {
            CompanyId = "companyId"
        };

        var act = () => _decisionEngineExecutionService.PreValidateDrawApprovalDecisionEngineExecution(request);

        act.ShouldThrow<ValidationException>();
    }
}
