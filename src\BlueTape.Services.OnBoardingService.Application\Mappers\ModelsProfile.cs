﻿using AutoMapper;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Credit;
using BlueTape.MongoDB.DTO;
using BlueTape.MongoDB.DTO.Base;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Company;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.Credit;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Application.Models.Draft;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.Application.Models.DrawApprovals;
using BlueTape.Services.OnBoardingService.Application.Models.IntegrationLogs;
using BlueTape.Services.OnBoardingService.Application.Models.Notes;
using BlueTape.Services.OnBoardingService.Application.Models.ParsedDraft;
using BlueTape.Services.OnBoardingService.Application.Models.PaymentPlans;
using BlueTape.Services.OnBoardingService.Application.Models.User;
using BlueTape.Services.OnBoardingService.Domain.Documents;
using BlueTape.Services.OnBoardingService.Domain.Documents.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Domain.Documents.Company;
using BlueTape.Services.OnBoardingService.Domain.Documents.Configuration;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationAuthorizationDetails;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.Domain.Documents.DecisionEngineSteps;
using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApproval;
using BlueTape.Services.OnBoardingService.Domain.Documents.DrawApprovalNotes;
using BlueTape.Services.OnBoardingService.Domain.Documents.ParsedDraft;
using BlueTape.Services.OnBoardingService.Domain.Documents.PaymentPlan;
using BlueTape.Services.OnBoardingService.Domain.Documents.User;
using BlueTape.Services.OnBoardingService.Domain.Extensions;
using BlueTape.Services.OnBoardingService.Infrastructure.Query.DecisionEngineSteps;
using CipherModel = BlueTape.Services.OnBoardingService.Application.Models.Cipher.CipherModel;

namespace BlueTape.Services.OnBoardingService.Application.Mappers;

public class ModelsProfile : Profile
{
    public ModelsProfile()
    {
        CreateMap<CreateDecisionEngineStepsBviResultsModel, DecisionEngineStepsBviResultsDocument>();
        CreateMap<DecisionEngineStepsBviResultsDocument, DecisionEngineStepsBviResultsModel>().ReverseMap();

        CreateMap<DecisionEngineSteps, DecisionEngineStepsDocument>().ReverseMap();
        CreateMap<DecisionEngineStepResult, DecisionEngineStepResultDocument>().ReverseMap();
        CreateMap<DecisionEngineStepsQueryModel, DecisionEngineStepsQuery>();

        CreateMap<CreateSystemCreditApplicationNote, SystemNoteTemplateInfo>();
        CreateMap<CreateSystemCreditApplicationNote, CreditApplicationNoteDocument>();

        CreateMap<CreateDecisionEngineSteps, DecisionEngineStepsDocument>();
        CreateMap<Draft, DraftDocument>().ReverseMap();
        CreateMap<PersonalInfo, PersonalInfoDocument>().ReverseMap();
        CreateMap<Bank, BankDocument>().ReverseMap();
        CreateMap<BusinessInfo, BusinessInfoDocument>().ReverseMap();
        CreateMap<BusinessOwner, BusinessOwnerDocument>().ReverseMap();
        CreateMap<CoOwnerInfo, CoOwnerInfoDocument>().ReverseMap();
        CreateMap<Data, DataDocument>().ReverseMap();
        CreateMap<Business, BusinessDocument>().ReverseMap();
        CreateMap<Ownership, OwnershipDocument>().ReverseMap();
        CreateMap<Registered, RegisteredDocument>().ReverseMap();
        CreateMap<Finance, FinanceDocument>().ReverseMap();
        CreateMap<Item, ItemDocument>().ReverseMap();
        CreateMap<CreateCreditApplication, CreditApplicationDocument>();
        CreateMap<CreateMerchantSettings, MerchantSettingsDocument>();
        CreateMap<DrawApprovalDecisionEngineExecutionRequest, DrawApprovalInitializationStepStartRequest>()
            .ForMember(x => x.LoanOrigin, y => y.MapFrom(dest => dest.LoanOrigin.HasValue ? dest.LoanOrigin.ToString() : null));
        CreateMap<CreditApplicationDocument, CreditApplication>()
            .ForMember(x => x.Type, y => y.MapFrom(z => StringToEnum<CreditApplicationType>(z.Type))).ReverseMap();
        CreateMap<LightCreditApplicationDocument, LightCreditApplication>()
            .ForMember(x => x.Type, y => y.MapFrom(z => StringToEnum<CreditApplicationType>(z.Type)));
        CreateMap<UpdateCompanyModel, UpdateCompanyDocument>();

        CreateMap<CreditApplicationNoteDocument, CreditApplicationNote>().ReverseMap();
        CreateMap<CreateCreditApplicationNote, CreditApplicationNoteDocument>().ReverseMap();
        CreateMap<PatchCreditApplicationNote, CreditApplicationNoteDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));

        CreateMap<CreateDrawApprovalNote, DrawApprovalNoteDocument>().ReverseMap();
        CreateMap<DrawApprovalNote, DrawApprovalNoteDocument>().ReverseMap();
        CreateMap<PatchDrawApprovalNote, DrawApprovalNoteDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));
        CreateMap<SubmitCreditApplication, CreateCreditApplication>();
        CreateMap<UpdateCreditApplication, CreditApplicationDocument>()
            .ForMember(x => x.StatusNote, opt =>
                opt.Ignore())
            .ForMember(x => x.Status, opt =>
                opt.MapFrom(y => y.NewStatus == null ? y.NewStatus : y.NewStatus.ToLower()))
            .ForAllMembers(opts =>
                opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));

        CreateMap<PatchCreditApplicationAdminModel, CreditApplicationDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));

        CreateMap<CreateCreditModel, CreateCreditDto>();
        CreateMap<CreditModel, CreditDto>().ReverseMap();

        CreateMap<CreateAccountAuthorization, AccountAuthorizationDocument>()
            .ForMember(x => x.UpdatedBy, opt => opt.MapFrom(x => x.CreatedBy));

        CreateMap<AccountAuthorizationFactoringOverallDetails, AccountAuthorizationFactoringOverallDetailsDocument>()
            .ForMember(x => x.LastDefaultedDate, opt => opt.MapFrom(d => ConvertNullableDateOnlyToDateTime(d.LastDefaultedDate)))
            .ForMember(x => x.LastEinRejectionDate, opt => opt.MapFrom(d => ConvertNullableDateOnlyToDateTime(d.LastEinRejectionDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));

        CreateMap<AccountAuthorizationFactoringOverallDetailsDocument, AccountAuthorizationFactoringOverallDetails>()
            .ForMember(x => x.LastDefaultedDate, opt => opt.MapFrom(d => ConvertNullableDateTimeToDateOnly(d.LastDefaultedDate)))
            .ForMember(x => x.LastEinRejectionDate, opt => opt.MapFrom(d => ConvertNullableDateTimeToDateOnly((d.LastEinRejectionDate))));

        CreateMap<AccountAuthorizationCreditDetails, AccountAuthorizationCreditDetailsDocument>()
            .ForMember(x => x.LastActivityDate, opt => opt.MapFrom(d => ConvertNullableDateOnlyToDateTime(d.LastActivityDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));

        CreateMap<AccountAuthorizationCreditDetailsDocument, AccountAuthorizationCreditDetails>()
            .ForMember(x => x.LastActivityDate, opt => opt.MapFrom(d => ConvertNullableDateTimeToDateOnly(d.LastActivityDate)));

        CreateMap<BankAccountDetailsPatch, BankAccountDetailsDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));
        CreateMap<OwnersDetailsPatch, OwnersDetailsDocument>()
            .ForMember(x => x.Birthday, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.Birthday)))
            .ForMember(x => x.LastSSNRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastSSNRejectionDate)))
            .ForMember(x => x.LastPersonalBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastPersonalBankruptcyDate)))
            .ForMember(x => x.CRICodes, opts => opts.Condition((_, _, srcMember) => srcMember.Any()))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));

        CreateMap<BusinessDetailsPatch, BusinessDetailsDocument>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastEINRejectionDate)))
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LoansLastDefaultedDate)))
            .ForMember(x => x.BusinessStartDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.BusinessStartDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastLienDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember, _, _) =>
            {
                if (srcMember is decimal)
                {
                    return true;
                }

                return !IsNullOrDefault(srcMember);
            }));

        CreateMap<OwnersEntitiesDetailsPatch, OwnersEntitiesDetailsDocument>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastEINRejectionDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastLienDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember, _, _) =>
            {
                if (srcMember is decimal)
                {
                    return true;
                }

                return !IsNullOrDefault(srcMember);
            }));

        CreateMap<BusinessDetailsPatch, BusinessDetailsDocument>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastEINRejectionDate)))
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LoansLastDefaultedDate)))
            .ForMember(x => x.BusinessStartDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.BusinessStartDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastLienDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember, _, _) =>
            {
                if (srcMember is decimal)
                {
                    return true;
                }

                return !IsNullOrDefault(srcMember);
            }));

        CreateMap<PatchAccountAuthorization, AccountAuthorizationDocument>()
            .ForMember(x => x.CreatedBy, opts => opts.Ignore())
            .ForMember(x => x.CreatedAt, opts => opts.Ignore())
            .ForMember(x => x.CompanyId, opts => opts.Ignore())
            .ForMember(x => x.EinHash, opts => opts.Ignore())
            .ForMember(x => x.BankAccountDetails, opts => opts.Ignore())
            .ForMember(x => x.OwnersDetails, opt => opt.Ignore())
            .ForMember(x => x.OwnersEntitiesDetails, opt => opt.Ignore())
            .ForMember(x => x.FactoringOverallDetails, opt => opt.Ignore())
            .ForMember(x => x.CreditDetails, opt => opt.Ignore())
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));
        CreateMap<AddressPatch, AddressDocument>();

        CreateMap<BankAccountDetailsUpdate, BankAccountDetailsDocument>();
        CreateMap<OwnersDetailsUpdate, OwnersDetailsDocument>()
            .ForMember(x => x.Birthday, opt => opt.MapFrom(x => x.Birthday.ToDateTime(TimeOnly.MinValue)))
            .ForMember(x => x.LastSSNRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastSSNRejectionDate)))
            .ForMember(x => x.LastPersonalBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastPersonalBankruptcyDate)));
        CreateMap<OwnersEntitiesDetailsUpdate, OwnersEntitiesDetailsDocument>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastEINRejectionDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastLienDate)));

        CreateMap<BusinessDetailsUpdate, BusinessDetailsDocument>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastEINRejectionDate)))
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LoansLastDefaultedDate)))
            .ForMember(x => x.BusinessStartDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.BusinessStartDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastLienDate)));
        CreateMap<UpdateAccountAuthorization, AccountAuthorizationDocument>()
            .ForMember(x => x.BusinessDetails, opt => opt.MapFrom(y => y.BusinessDetails))
            .ForMember(x => x.BankAccountDetails, opt => opt.MapFrom(y => y.BankAccountDetails))
            .ForMember(x => x.OwnersDetails, opt => opt.MapFrom(y => y.OwnersDetails))
            .ForMember(x => x.OwnersEntitiesDetails, opt => opt.MapFrom(y => y.OwnersEntitiesDetails));
        CreateMap<AddressUpdate, AddressDocument>();

        CreateMap<BankAccountDetails, BankAccountDetailsDocument>().ReverseMap();
        CreateMap<OwnersDetailsDocument, OwnersDetails>()
            .ForMember(x => x.Birthday, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.Birthday)))
            .ForMember(x => x.LastSSNRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastSSNRejectionDate)))
            .ForMember(x => x.LastPersonalBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastPersonalBankruptcyDate)));
        CreateMap<OwnersEntitiesDetailsDocument, OwnersEntitiesDetails>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastEINRejectionDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastLienDate)));
        CreateMap<BusinessDetailsDocument, BusinessDetails>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastEINRejectionDate)))
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LoansLastDefaultedDate)))
            .ForMember(x => x.BusinessStartDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.BusinessStartDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateTimeToDateOnly(x.LastLienDate)));
        CreateMap<AccountAuthorizationDocument, AccountAuthorization>()
            .ForMember(x => x.BusinessDetails, opt => opt.MapFrom(y => y.BusinessDetails))
            .ForMember(x => x.BankAccountDetails, opt => opt.MapFrom(y => y.BankAccountDetails))
            .ForMember(x => x.OwnersDetails, opt => opt.MapFrom(y => y.OwnersDetails))
            .ReverseMap();

        CreateMap<OwnersDetails, OwnersDetailsDocument>()
            .ForMember(x => x.Birthday, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.Birthday)))
            .ForMember(x => x.LastSSNRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastSSNRejectionDate)))
            .ForMember(x => x.LastPersonalBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastPersonalBankruptcyDate)));
        CreateMap<BusinessDetails, BusinessDetailsDocument>()
            .ForMember(x => x.LastEINRejectionDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastEINRejectionDate)))
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LoansLastDefaultedDate)))
            .ForMember(x => x.BusinessStartDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.BusinessStartDate)))
            .ForMember(x => x.FirstReportedTradeLineDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.FirstReportedTradeLineDate)))
            .ForMember(x => x.LastBankruptcyDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastBankruptcyDate)))
            .ForMember(x => x.LastJudgmentDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastJudgmentDate)))
            .ForMember(x => x.LastLienDate, opt => opt.MapFrom(x => ConvertNullableDateOnlyToDateTime(x.LastLienDate)));
        CreateMap<AccountAuthorization, AccountAuthorizationDocument>().ReverseMap();

        CreateMap<AddressDocument, AddressModel>().ReverseMap();
        CreateMap<RequestResponseBaseModel, RequestResponseBaseDocument>().ReverseMap();
        CreateMap<ResponseModel, ResponseDocument>().ReverseMap();
        CreateMap<MerchantSettings, MerchantSettingsDocument>().ReverseMap();

        CreateMap<UserDocument, User>().ReverseMap();

        CreateMap<GetQueryWithPaginationResult<CreditApplicationDocument>,
            GetQueryWithPaginationResultModel<CreditApplication>>().ReverseMap();
        CreateMap<GetQueryWithPaginationResult<DrawApprovalDocument>,
            GetQueryWithPaginationResultModel<DrawApproval>>().ReverseMap();

        CreateMap<FactoringDetailsDocument, FactoringDetailsModel>()
            .ForMember(x => x.InHouseCreditStatus, opt => opt.MapFrom(s => StringToEnum<CreditStatus>(s.InHouseCreditStatus))).ReverseMap();
        CreateMap<FactoringOverallDetailsModel, FactoringOverallDetailsDocument>()
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(s => ConvertNullableDateOnlyToDateTime(s.LoansLastDefaultedDate)));
        CreateMap<FactoringOverallDetailsDocument, FactoringOverallDetailsModel>()
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(s => ConvertNullableDateTimeToDateOnly(s.LoansLastDefaultedDate)));
        CreateMap<AutomatedApprovalDetailsDocument, AutomatedApprovalDetailsModel>()
            .ForMember(x => x.VelocityCheckResult, opt => opt.MapFrom(s => StringToEnum<VelocityCheckResult>(s.VelocityCheckResult)))
            .ForMember(x => x.Reason, opt => opt.MapFrom(s => StringToEnum<AutomatedApprovalResultReason>(s.Reason)))
            .ReverseMap();
        CreateMap<PayNowDetailsModel, PayNowDetailsDocument>().ReverseMap();

        CreateMap<CreditApplicationAuthorizationDetails, CreditApplicationAuthorizationDetailsDocument>().ForMember(x => x.AccountAuthorizationDetailsSnapshot,
                opt => opt.MapFrom(y => y.AccountAuthorizationDetailsSnapshot))
            .ReverseMap();
        CreateMap<UpdateCreditApplicationAuthorizationDetails, CreditApplicationAuthorizationDetailsDocument>();
        CreateMap<DecisionEngineStepThresholdModel, DecisionEngineThresholdDocument>()
            .ForMember(x => x.Pass, opt => opt.MapFrom(y => ConvertNullableObjectToString(y.Pass)))
            .ForMember(x => x.SoftFail, opt => opt.MapFrom(y => ConvertNullableObjectToString(y.SoftFail)))
            .ForMember(x => x.HardFail, opt => opt.MapFrom(y => ConvertNullableObjectToString(y.HardFail)));
        CreateMap<DecisionEngineThresholdDocument, DecisionEngineStepThresholdModel>();

        CreateMap<CreateDrawApproval, DrawApprovalDocument>();

        CreateMap<DrawApprovalDocument, DrawApproval>()
            .ForMember(x => x.LoanOrigin, y => y.MapFrom(z => StringToEnum<LoanOrigin>(z.LoanOrigin)))
            .ForMember(x => x.Type, y => y.MapFrom(d => StringToEnum<DrawApprovalType>(d.Type)))
            .ReverseMap();

        CreateMap<DrawDetailsDocument, DrawDetails>()
            .ForMember(x => x.AccountStatus, opt => opt.MapFrom(s => StringToEnumOrNull<CreditStatus>(s.AccountStatus)))
            .ReverseMap();

        CreateMap<PayableItemDocument, PayableItem>()
            .ForMember(x => x.Type, opt => opt.MapFrom(d => StringToEnum<PayableType>(d.Type)))
            .ReverseMap();

        CreateMap<NoSupplierDetailsDocument, NoSupplierDetails>().ReverseMap();
        CreateMap<NoSupplierBankDetailsDocument, NoSupplierBankDetails>().ReverseMap();
        CreateMap<NoSupplierAddressDocument, NoSupplierAddress>().ReverseMap();
        CreateMap<NoSupplierBankAccountNumberDocument, NoSupplierBankAccountNumber>().ReverseMap();

        CreateMap<NoSupplierDetailsModel, NoSupplierDetailsDocument>().ReverseMap();
        CreateMap<NoSupplierBankDetailsModel, NoSupplierBankDetailsDocument>().ReverseMap();
        CreateMap<NoSupplierAddressModel, NoSupplierAddressDocument>().ReverseMap();

        CreateMap<LoanPaymentPlanDocument, LoanPaymentPlan>();

        CreateMap<ReviewDrawApprovalModel, DrawApprovalDocument>()
            .ForMember(x => x.Status, opt => opt.MapFrom(y => y.NewStatus));
        CreateMap<PatchDrawApproval, DrawApprovalDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));
        CreateMap<DownPaymentDetailsDocument, DownPaymentDetails>();
        CreateMap<DownPaymentDetails, DownPaymentDetailsDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) =>
            {
                if (srcMember is (decimal or int) and not null)
                {
                    return true;
                }
                return !IsNullOrDefault(srcMember);
            }));
        CreateMap<PatchDrawDetails, DrawDetailsDocument>()
            .ForMember(x => x.LoansLastDefaultedDate,
                opt => opt.MapFrom(y => ConvertNullableDateOnlyToDateTime(y.LoansLastDefaultedDate)))
            .ForMember(x => x.ProjectEndDate,
                opt => opt.MapFrom(y => ConvertNullableDateOnlyToDateTime(y.ProjectEndDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) =>
            {
                if (srcMember is (decimal or int) and not null)
                {
                    return true;
                }
                return !IsNullOrDefault(srcMember);
            }));
        CreateMap<PatchPayNowDetailsModel, PayNowDetailsDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) =>
            {
                if (srcMember is (decimal or int) and not null)
                {
                    return true;
                }
                return !IsNullOrDefault(srcMember);
            }));
        CreateMap<PatchFactoringOverallDetailsModel, FactoringOverallDetailsDocument>()
            .ForMember(x => x.LoansLastDefaultedDate, opt => opt.MapFrom(d => ConvertNullableDateOnlyToDateTime(d.LoansLastDefaultedDate)))
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) =>
            {
                if (srcMember is (decimal or int) and not null)
                {
                    return true;
                }
                return !IsNullOrDefault(srcMember);
            }));
        CreateMap<PatchFactoringDetailsModel, FactoringDetailsDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) =>
            {
                if (srcMember is (decimal or int) and not null)
                {
                    return true;
                }
                return !IsNullOrDefault(srcMember);
            }));
        CreateMap<PatchAutomatedApprovalDetailsModel, AutomatedApprovalDetailsDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) =>
            {
                if (srcMember is decimal or int)
                {
                    return true;
                }
                return !IsNullOrDefault(srcMember);
            }));

        CreateMap<UpdateDrawApproval, DrawApprovalDocument>()
            .ForAllMembers(opts => opts.Condition((_, _, srcMember) => !IsNullOrDefault(srcMember)));
        CreateMap<UpdateDrawDetails, DrawDetailsDocument>()
            .ForMember(x => x.LoansLastDefaultedDate,
                opt => opt.MapFrom(y => ConvertNullableDateOnlyToDateTime(y.LoansLastDefaultedDate)))
            .ForMember(x => x.ProjectEndDate,
                opt => opt.MapFrom(y => ConvertNullableDateOnlyToDateTime(y.ProjectEndDate)));

        CreateMap<InvoiceModel, PayableItemDocument>()
            .ForMember(x => x.Amount, opt => opt.MapFrom(x => x.TotalAmount))
            .ForMember(x => x.Id, opt => opt.MapFrom(i => i.Id))
            .ForMember(x => x.Status, opt => opt.Ignore())
            .ForMember(x => x.InvoiceDueDate, opt => opt.Ignore());

        CreateMap<CipherModel, CipherDocument>().ReverseMap();
        CreateMap<ParsedDraftModel, ParsedDraftDocument>().ReverseMap();
        CreateMap<CoOwnerModel, CoOwnerDocument>().ReverseMap();
        CreateMap<BankDetailsModel, BankDetailsDocument>().ReverseMap();
        CreateMap<BusinessNameModel, BusinessNameDocument>().ReverseMap();

        CreateMap<ParsedBodyLoggingDocument, CipherModel>().ReverseMap();

        CreateMap<AuthorizationDetailsRefreshCheckDocument, RefreshCheckConfiguration>()
            .ForMember(x => x.CreditApplicationTypes,
                opt => opt.MapFrom(y => y.CreditApplicationTypes.Select(x => x.GetCreditApplicationTypeByShortDefinition())));
    }

    private static T? StringToEnum<T>(string? value) where T : struct, Enum
    {
        Enum.TryParse<T>(value, true, out var result);
        return result;
    }

    private static T? StringToEnumOrNull<T>(string? value) where T : struct, Enum
    {
        return Enum.TryParse<T>(value, true, out var result) ? result : (T?)null;
    }


    private static string? ConvertNullableObjectToString(object? obj)
    {
        return obj?.ToString();
    }

    private static DateTime? ConvertNullableDateOnlyToDateTime(DateOnly? dateOnly)
    {
        return dateOnly?.ToDateTime(TimeOnly.MinValue) ?? null;
    }

    private static DateOnly? ConvertNullableDateTimeToDateOnly(DateTime? dateTime)
    {
        return dateTime is null ? null : DateOnly.FromDateTime(dateTime.Value);
    }

    private static bool IsNullOrDefault<T>(T argument)
    {
        if (argument == null) return true;
        if (Equals(argument, default(T))) return true;

        var methodType = typeof(T);
        if (Nullable.GetUnderlyingType(methodType) != null) return false;

        if (argument is string && string.IsNullOrEmpty(argument.ToString())) return true;

        var argumentType = argument.GetType();
        if (argumentType.IsValueType && argumentType != typeof(bool) && argumentType != methodType)
        {
            var obj = Activator.CreateInstance(argument.GetType())!;
            return obj!.Equals(argument);
        }

        return false;
    }
}