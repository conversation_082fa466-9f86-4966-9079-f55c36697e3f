﻿using MongoDB.Bson;

namespace BlueTape.Services.OnBoardingService.DataAccess.Pipelines;

public static class AccountAuthorizationPipelines
{
    public static readonly BsonDocument LookupCreditApplications = new("$lookup",
        new BsonDocument
        {
            { "from", "creditApplications" },
            { "localField", "einHash" },
            { "foreignField", "einHash" },
            { "as", "matchingApplications" }
        });

    public static readonly BsonDocument AddCreditApplicationsIds = new("$addFields",
        new BsonDocument("creditApplicationIds",
            new BsonDocument("$map",
                new BsonDocument
                {
                    { "input", "$matchingApplications" },
                    { "as", "app" },
                    { "in",  new BsonDocument("$toString", "$$app._id") }
                })));

    public static readonly BsonDocument ProjectMatchingApplications = new("$project",
        new BsonDocument("matchingApplications", 0));
}
