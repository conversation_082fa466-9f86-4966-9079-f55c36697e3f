﻿using BlueTape.Services.OnBoardingService.Domain.Documents.Draft;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Newtonsoft.Json.Linq;

namespace BlueTape.Services.OnBoardingService.Application.Extensions;

public static class DraftExtensions
{
    public static string GetEinHash(this DraftDocument draft)
    {
        var businessInfoItems = draft.Data?.BusinessInfo?.Items?.ToList();
        var businessEin = businessInfoItems?.Find(x => x.Identifier == "ein")?.Content
                          ?? throw new ValidationException($"Draft {draft.Id} does not contain ein");
        var einObject = JObject.FromObject(businessEin);
        var einHash = einObject.Value<string>("hash") ?? throw new ValidationException($"Draft {draft.Id} does not contain ein hash");

        return einHash;
    }
}
