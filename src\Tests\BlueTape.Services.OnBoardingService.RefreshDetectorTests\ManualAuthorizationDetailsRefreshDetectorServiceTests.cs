﻿using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplicationNotes;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Abstractions;
using BlueTape.Services.OnBoardingService.RefreshDetectorService.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorTests;

public class ManualAuthorizationDetailsRefreshDetectorServiceTests
{
    private readonly Mock<IAuthorizationDetailsRefreshConfigurationService> _configurationServiceMock;
    private readonly Mock<ICreditApplicationRepository> _creditApplicationRepositoryMock;
    private readonly Mock<ICompanyManualScheduledUpdateEventsGenerator> _eventsGeneratorMock;
    private readonly Mock<IRefreshServiceMessageSender> _refreshServiceMessageSenderMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<ICreditApplicationNotesService> _creditApplicationNotesServiceMock;
    private readonly Mock<IDecisionEngineStepsRepository> _decisionEngineStepsRepositoryMock;
    private readonly Mock<ICompanyService> _companyServiceMock;
    private readonly Mock<ILogger<ManualAuthorizationDetailsRefreshDetectorService>> _loggerMock;
    private readonly ManualAuthorizationDetailsRefreshDetectorService _service;

    public ManualAuthorizationDetailsRefreshDetectorServiceTests()
    {
        _configurationServiceMock = new Mock<IAuthorizationDetailsRefreshConfigurationService>();
        _creditApplicationRepositoryMock = new Mock<ICreditApplicationRepository>();
        _eventsGeneratorMock = new Mock<ICompanyManualScheduledUpdateEventsGenerator>();
        _refreshServiceMessageSenderMock = new Mock<IRefreshServiceMessageSender>();
        _decisionEngineStepsRepositoryMock = new Mock<IDecisionEngineStepsRepository>();
        _dateProviderMock = new Mock<IDateProvider>();
        _creditApplicationNotesServiceMock = new Mock<ICreditApplicationNotesService>();
        _companyServiceMock = new Mock<ICompanyService>();
        _loggerMock = new Mock<ILogger<ManualAuthorizationDetailsRefreshDetectorService>>();

        _service = new ManualAuthorizationDetailsRefreshDetectorService(
            _configurationServiceMock.Object,
            _creditApplicationRepositoryMock.Object,
            _eventsGeneratorMock.Object,
            _creditApplicationNotesServiceMock.Object,
            _dateProviderMock.Object,
            _decisionEngineStepsRepositoryMock.Object,
            _refreshServiceMessageSenderMock.Object,
            _companyServiceMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task ManualRun_ValidCompany_ProcessesCorrectly()
    {
        var companyId = "validCompanyId";
        var manualRefreshRunRequest = new ManualRefreshRunRequest();
        var userId = "userId";
        var scheduleMode = ScheduleMode.CreateNew;
        var ctx = CancellationToken.None;

        var company = new CompanyModel { Id = companyId };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
            {
                new() { Id = "1" }
            };
        var scheduledChecks = new List<RefreshCheckConfiguration>
            {
                new()
            };
        var generatedEvents = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>
            {
                new(new ScheduledUpdateEvent())
            };

        _companyServiceMock.Setup(x => x.GetCompanyById(companyId, ctx)).ReturnsAsync(company);
        _creditApplicationRepositoryMock.Setup(x => x.GetLightCreditApplications(It.IsAny<string[]>(), It.IsAny<string>(), ctx)).ReturnsAsync(approvedCreditApplications);
        _configurationServiceMock.Setup(x => x.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);
        _eventsGeneratorMock.Setup(x => x.GenerateScheduledUpdateEvents(company, It.IsAny<RefreshCheckConfiguration>(), approvedCreditApplications, scheduleMode, userId)).Returns(generatedEvents);

        await _service.ManualRun(companyId, manualRefreshRunRequest, userId, scheduleMode, ctx);

        _companyServiceMock.Verify(x => x.GetCompanyById(companyId, ctx), Times.Once);
        _creditApplicationRepositoryMock.Verify(x => x.GetLightCreditApplications(It.IsAny<string[]>(), It.IsAny<string>(), ctx), Times.Once);
        _eventsGeneratorMock.Verify(x => x.GenerateScheduledUpdateEvents(company, It.IsAny<RefreshCheckConfiguration>(), approvedCreditApplications, scheduleMode, userId), Times.Once);
        _refreshServiceMessageSenderMock.Verify(x => x.SendMessages(It.IsAny<IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>>>(), ctx), Times.Once);
        _refreshServiceMessageSenderMock.Verify(x => x.SendMessages(generatedEvents, ctx), Times.Once);
        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }

    [Fact]
    public Task ManualRun_InvalidCompany_ThrowsException()
    {
        var companyId = "invalidCompanyId";
        var manualRefreshRunRequest = new ManualRefreshRunRequest();
        var userId = "userId";
        var scheduleMode = ScheduleMode.CreateNew;
        var ctx = CancellationToken.None;

        _companyServiceMock.Setup(x => x.GetCompanyById(companyId, ctx)).ReturnsAsync((CompanyModel)null);

        return Assert.ThrowsAsync<VariableNullException>(() => _service.ManualRun(companyId, manualRefreshRunRequest, userId, scheduleMode, ctx));
    }

    [Fact]
    public async Task ManualRun_ValidCompanyAndRefreshServiceManualOverrideExecutionFlowIsClearOverrides_ProcessesCorrectly()
    {
        var companyId = "validCompanyId";
        var manualRefreshRunRequest = new ManualRefreshRunRequest();
        manualRefreshRunRequest.ExecutionFlow = RefreshServiceManualOverrideExecutionFlow.ClearOverrides;
        var userId = "userId";
        var scheduleMode = ScheduleMode.CreateNew;

        var ctx = CancellationToken.None;

        var company = new CompanyModel { Id = companyId };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
            {
                new() { Id = "1" }
            };
        var scheduledChecks = new List<RefreshCheckConfiguration>
            {
                new()
            };
        var generatedEvents = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>
            {
                new(new ScheduledUpdateEvent())
            };

        _companyServiceMock.Setup(x => x.GetCompanyById(companyId, ctx)).ReturnsAsync(company);
        _creditApplicationRepositoryMock.Setup(x => x.GetLightCreditApplications(It.IsAny<string[]>(), It.IsAny<string>(), ctx)).ReturnsAsync(approvedCreditApplications);
        _configurationServiceMock.Setup(x => x.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);
        _eventsGeneratorMock.Setup(x => x.GenerateScheduledUpdateEvents(company, It.IsAny<RefreshCheckConfiguration>(), approvedCreditApplications, scheduleMode, userId)).Returns(generatedEvents);

        await _service.ManualRun(companyId, manualRefreshRunRequest, userId, scheduleMode, ctx);

        _companyServiceMock.Verify(x => x.GetCompanyById(companyId, ctx), Times.Once);
        _creditApplicationRepositoryMock.Verify(x => x.GetLightCreditApplications(It.IsAny<string[]>(), It.IsAny<string>(), ctx), Times.Once);
        _eventsGeneratorMock.Verify(x => x.GenerateScheduledUpdateEvents(company, It.IsAny<RefreshCheckConfiguration>(), approvedCreditApplications, scheduleMode, userId), Times.Once);
        _refreshServiceMessageSenderMock.Verify(x => x.SendMessages(It.IsAny<IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>>>(), ctx), Times.Once);
        _refreshServiceMessageSenderMock.Verify(x => x.SendMessages(generatedEvents, ctx), Times.Once);
        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }

    [Fact]
    public async Task ManualRun_ValidCompanyAndRefreshServiceManualOverrideExecutionFlowIsRerun_ProcessesCorrectly()
    {
        var companyId = "validCompanyId";
        var manualRefreshRunRequest = new ManualRefreshRunRequest();
        manualRefreshRunRequest.ExecutionFlow = RefreshServiceManualOverrideExecutionFlow.Rerun;
        var userId = "userId";
        var scheduleMode = ScheduleMode.CreateNew;

        var ctx = CancellationToken.None;

        var company = new CompanyModel { Id = companyId };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
            {
                new() { Id = "1" }
            };
        var scheduledChecks = new List<RefreshCheckConfiguration>
            {
                new()
            };
        var generatedEvents = new List<ServiceBusMessageBt<ScheduledUpdateEvent>>
            {
                new(new ScheduledUpdateEvent())
            };

        _companyServiceMock.Setup(x => x.GetCompanyById(companyId, ctx)).ReturnsAsync(company);
        _creditApplicationRepositoryMock.Setup(x => x.GetLightCreditApplications(It.IsAny<string[]>(), It.IsAny<string>(), ctx)).ReturnsAsync(approvedCreditApplications);
        _configurationServiceMock.Setup(x => x.GetRefreshChecks(default)).ReturnsAsync(scheduledChecks);
        _eventsGeneratorMock.Setup(x => x.GenerateScheduledUpdateEvents(company, It.IsAny<RefreshCheckConfiguration>(), approvedCreditApplications, scheduleMode, userId)).Returns(generatedEvents);

        await _service.ManualRun(companyId, manualRefreshRunRequest, userId, scheduleMode, ctx);

        _companyServiceMock.Verify(x => x.GetCompanyById(companyId, ctx), Times.Once);
        _creditApplicationRepositoryMock.Verify(x => x.GetLightCreditApplications(It.IsAny<string[]>(), It.IsAny<string>(), ctx), Times.Once);
        _eventsGeneratorMock.Verify(x => x.GenerateScheduledUpdateEvents(company, It.IsAny<RefreshCheckConfiguration>(), approvedCreditApplications, scheduleMode, userId), Times.Once);
        _refreshServiceMessageSenderMock.Verify(x => x.SendMessages(It.IsAny<IReadOnlyList<ServiceBusMessageBt<ScheduledUpdateEvent>>>(), ctx), Times.Once);
        _refreshServiceMessageSenderMock.Verify(x => x.SendMessages(generatedEvents, ctx), Times.Once);
        _creditApplicationNotesServiceMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<CreateCreditApplicationNote>>(), default), Times.Once);
    }
}
