using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplicationNotes;

[BsonIgnoreExtraElements]
[MongoCollection("creditApplicationNotes")]
public class CreditApplicationNoteDocument : Document
{
    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("note")]
    public string? Note { get; set; }

    [BsonElement("caption")]
    public string? Caption { get; set; }

    [BsonElement("createdBy")]
    public string? CreatedBy { get; set; }

    [BsonElement("displayName")]
    public string? DisplayName { get; set; }

    [BsonElement("deletedAt")]
    public DateTime? DeletedAt { get; set; }

    [BsonElement("deletedBy")]
    public string? DeletedBy { get; set; }
    [BsonElement("executionId")]
    public string? ExecutionId { get; set; }

    [BsonElement("isSystemGenerated")]
    public bool? IsSystemGenerated { get; set; }
}
