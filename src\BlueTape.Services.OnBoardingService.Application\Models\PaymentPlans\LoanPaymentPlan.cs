﻿namespace BlueTape.Services.OnBoardingService.Application.Models.PaymentPlans;

public class LoanPaymentPlan
{
    public string Id { get; set; } = string.Empty;
    public string? Name { get; set; }
    public string? LmsTemplateId { get; set; }
    public string? Frequency { get; set; }
    public string? Type { get; set; }
    public int Days { get; set; }
    public double Fee { get; set; }
    public int Term { get; set; }
    public int FirstPaymentDelayDays { get; set; }
}
