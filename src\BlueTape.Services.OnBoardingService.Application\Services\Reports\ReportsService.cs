﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Abstractions.Reports;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.OnBoardingService.Application.Services.Reports;

public class ReportsService : IReportsService
{
    private readonly ILogger<ReportsService> _logger;
    private readonly IEnumerable<StepDataRetrievingStrategyBase> _strategies;
    private readonly ICreditApplicationService _creditApplicationService;

    public ReportsService(IEnumerable<StepDataRetrievingStrategyBase> strategies, ICreditApplicationService creditApplicationService,
        ILogger<ReportsService> logger)
    {
        _logger = logger;
        _strategies = strategies;
        _creditApplicationService = creditApplicationService;
    }

    public async Task<CreateReportModel> RetrieveDataForReportsCreation(string creditApplicationId, CancellationToken ct)
    {
        _logger.LogInformation("Start RetrievingDataForReportsCreation. Getting credit application by id {creditApplicationId}", creditApplicationId);
        var creditApplication = await _creditApplicationService.GetById(creditApplicationId, ct);
        if (creditApplication is null) throw new ValidationException($"Credit application {creditApplicationId} does not exists");

        _logger.LogInformation("RetrieveDataForReportsCreation: Start getting all steps data credit application by id {creditApplicationId}", creditApplicationId);
        var reportStepsData = await GetAllStepsData(creditApplicationId, creditApplication.CompanyId, ct);
        _logger.LogInformation("RetrieveDataForReportsCreation: Collected data for all steps " +
                               "credit application by id {creditApplicationId}", creditApplicationId);

        var createReportModel = new CreateReportModel()
        {
            ReportStepsData = reportStepsData
        };

        return createReportModel;
    }

    public async Task<IEnumerable<ReportStepDataModel>> RetrieveExecutionDetails(string creditApplicationId, StepName? stepName, CancellationToken ct)
    {
        _logger.LogInformation("Start RetrievingExecutionDetails. Getting credit application by id {creditApplicationId}", creditApplicationId);

        var creditApplication = await _creditApplicationService.GetById(creditApplicationId, ct);

        _logger.LogInformation("RetrieveExecutionDetails: Got credit application by id {creditApplicationId}", creditApplicationId);

        if (stepName is null)
        {
            _logger.LogInformation("RetrieveExecutionDetails: step name null. " +
                                   "Retrieve all steps data {creditApplicationId}", creditApplicationId);
            return await GetAllStepsData(creditApplicationId, creditApplication.CompanyId, ct);
        }

        var strategy = _strategies.FirstOrDefault(x => x.IsApplicable(stepName.Value));
        var stepData = await strategy?.CollectReportStepData(creditApplicationId, creditApplication.CompanyId, ct)!;

        _logger.LogInformation("RetrieveExecutionDetails: Collected data for step {stepName} " +
                               "and credit application by id {creditApplicationId}", stepName.Value, creditApplicationId);

        return stepData is not null
            ? new List<ReportStepDataModel> { stepData }
            : new List<ReportStepDataModel>();
    }

    private async Task<ReportStepDataModel[]> GetAllStepsData(string creditApplicationId, string? companyId, CancellationToken ct)
    {
        var stepDataForReportRetrievingTasks = _strategies.Select(x =>
            x.CollectReportStepData(creditApplicationId, companyId, ct)).ToArray();

        await Task.WhenAll(stepDataForReportRetrievingTasks);
        return stepDataForReportRetrievingTasks.Select(x => x.Result).Where(x => x is not null).ToArray()!;
    }
}