﻿using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Abstractions;
using BlueTape.Services.OnBoardingService.Application.Models.AccountAuthorization;
using BlueTape.Services.OnBoardingService.Application.Models.Reports;
using BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving.Base;
using BlueTape.Services.OnBoardingService.DataAccess.Abstractions;

namespace BlueTape.Services.OnBoardingService.Application.Strategies.StepReportsDataRetrieving;

public class KybStepDataRetrievingStrategy : StepDataRetrievingStrategyBase
{
    public KybStepDataRetrievingStrategy(IDecisionEngineStepsRepository decisionEngineStepsRepository,
        IDecisionEngineStepsBviResultsService decisionEngineStepsBviResultsService,
        ICreditApplicationAuthorizationDetailsService creditApplicationAuthorizationDetailsService)
        : base(decisionEngineStepsRepository, decisionEngineStepsBviResultsService, creditApplicationAuthorizationDetailsService)
    {
    }

    protected override StepName StepName => StepName.KYB;

    protected override Task<BviResultModel[]> GetBviResults(string? stepId, string? companyId, AccountAuthorization? accountAuthorizationDetails, CancellationToken ct)
    {
        return GetBviResultsFromIntegrationLogs(stepId, ct);
    }
}