﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.OBS.Enums;
using BlueTape.Services.OnBoardingService.Application.Models.Configuration.AuthorizationDetailsRefreshConfiguration;
using BlueTape.Services.OnBoardingService.Domain.Documents.CreditApplication;
using BlueTape.Services.OnBoardingService.RefreshDetectorService;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.OnBoardingService.RefreshDetectorTests;

public class CompanyManualScheduledUpdateEventsGeneratorTests
{
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly CompanyManualScheduledUpdateEventsGenerator _generator;

    public CompanyManualScheduledUpdateEventsGeneratorTests()
    {
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();
        Mock<ILogger<CompanyManualScheduledUpdateEventsGenerator>> loggerMock = new();
        _generator = new CompanyManualScheduledUpdateEventsGenerator(_traceIdAccessorMock.Object, loggerMock.Object);
    }

    [Fact]
    public void GenerateScheduledUpdateEvents_NoCreditApplications_ReturnsEmptyList()
    {
        var company = new CompanyModel();
        var scheduledCheck = new RefreshCheckConfiguration
        {
            CreditApplicationTypes = new List<CreditApplicationType>()
        };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>();
        var scheduleMode = ScheduleMode.CreateNew;

        var result = _generator.GenerateScheduledUpdateEvents(company, scheduledCheck, approvedCreditApplications, scheduleMode, string.Empty);

        result.ShouldBeEmpty();
    }

    [Fact]
    public void GenerateScheduledUpdateEvents_WithCreditApplications_ReturnsEvents()
    {
        var company = new CompanyModel();
        var scheduledCheck = new RefreshCheckConfiguration
        {
            CreditApplicationTypes = new List<CreditApplicationType> { CreditApplicationType.LineOfCredit }
        };
        var approvedCreditApplications = new List<LightCreditApplicationDocument>
            {
                new () { Id = "2", Type = CreditApplicationType.InHouseCredit.ToString() },
                new () { Id = "1", Type =CreditApplicationType.LineOfCredit.ToString() }
            };
        var scheduleMode = ScheduleMode.CreateNew;
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns("trace-id");

        var result = _generator.GenerateScheduledUpdateEvents(company, scheduledCheck, approvedCreditApplications, scheduleMode, string.Empty);

        result.ShouldNotBeEmpty();
        result.Count.ShouldBe(1);
        result[0].MessageBody.Details.ApprovedCreditApplicationId.ShouldBe("1");
        result[0].MessageBody.BlueTapeCorrelationId.ShouldBe("trace-id");
    }
}