﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions.Json" Version="6.1.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="TinyHelpers" Version="3.1.5" />
    <PackageReference Include="BlueTape.AWSMessaging" Version="2.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.Application\BlueTape.Services.OnBoardingService.Application.csproj" />
    <ProjectReference Include="..\BlueTape.Services.OnBoardingService.DataAccess\BlueTape.Services.OnBoardingService.DataAccess.csproj" />
  </ItemGroup>

</Project>
