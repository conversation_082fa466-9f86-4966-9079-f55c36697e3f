﻿using BlueTape.Services.OnBoardingService.Application.Models.Common;
using BlueTape.Services.OnBoardingService.Application.Models.CreditApplication;
using BlueTape.Services.OnBoardingService.Application.Models.DecisionEngineExecution;

namespace BlueTape.Services.OnBoardingService.Application.Abstractions;

public interface ICreditApplicationExecutionService
{
    Task<StepFunctionsExecutionResponse> RunDecisionEngineForCreditApplication(CreditApplicationDecisionEngineExecutionRequest request, CancellationToken ct);
    Task<CreditApplication> SubmitCreditApplication(SubmitCreditApplication submitCreditApplication,
        CancellationToken ct);
    Task<StepFunctionsExecutionResponse> RunDecisionEngineAsArAdvance(string getPaidApplicationId, CancellationToken ct);
    Task<StepFunctionsExecutionResponse> RunDecisionEngineInitializationStepForCreditApplication(CreditApplicationInitializationStepStartRequest request, CancellationToken ct);
}
