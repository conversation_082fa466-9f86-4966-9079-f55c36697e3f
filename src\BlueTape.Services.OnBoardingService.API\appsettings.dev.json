{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "AllowedHosts": "*", "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_dev"}, "StepsReportOptions": {"BucketName": "dev.uw1.linqpal-temp-assets"}, "SlackNotification": {"ErrorSnsTopicName": "obs-notifications-dev"}, "ConnectorQueueOptions": {"TopicName": "netsuite-connector-dev.fifo"}, "InitializationStepOptions": {"CreditApplicationsStateMachineName": "dotnet-decision-engine-dev", "DrawApprovalsStateMachineName": "dotnet-draw-approval-dev"}}