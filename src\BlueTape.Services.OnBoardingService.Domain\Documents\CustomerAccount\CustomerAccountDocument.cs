﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BlueTape.Services.OnBoardingService.Domain.Documents.CustomerAccount;

[BsonIgnoreExtraElements]
[MongoCollection("customeraccounts")]
public class CustomerAccountDocument : Document
{
    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }
    
    [BsonElement("email")]
    public string? Email { get; set; }
    
    [BsonElement("phone")]
    public string? Phone { get; set; }
    
    [BsonElement("isDeleted")]
    public bool IsDeleted { get; set; }
}
